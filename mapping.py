mappings = {
    "properties": {
      "UID": {
        "type": "keyword"
      },
      "SourceKey": {
        "type": "keyword"
      },
      "SourceCountry": {
        "type": "keyword"
      },
      "SourceUID": {
        "type": "keyword"
      },
      "SourceLangCode": {
        "type": "keyword"
      },
      "SourceURL": {
        "type": "keyword"
      },
      "CrawlTimestamp": {
        "type": "date"
      },
      "CompanyName": {
        "type": "text"
      },
      "CompanyLogoFileURL": {
        "type": "keyword"
      },
      "NormalizedCompanyName": {
        "type": "keyword"
      },
      "JobTitle": {
        "type": "text"
      },
      "CleanContent": {
        "type": "text"
      },
      "RawContent": {
        "type": "text"
      },
      "PostedDate": {
        "type": "date"
      },
      "InsertDate": {
        "type": "date"
      },
      "JobTypeText": {
        "type": "text"
      },
      "JobType": {
        "type": "integer"
      },
      "JobContractTypeText": {
        "type": "text"
      },
      "JobContactText": {
        "type": "text"
      },
      "JobContactEmails": {
        "type": "text"
      },
      "JobContactPhone": {
        "type": "keyword"
      },
      "JobContactEmail": {
        "type": "keyword"
      },
      "JobSector": {
        "type": "text"
      },
      "JobSectors": {
        "type": "text"
      },
      "JobLocation": {
        "type": "text"
      },
      "JobLocationCountryCode": {
        "type": "keyword"
      },
      "JobLocationPostalCode": {
        "type": "keyword"
      },
      "JobLocationState": {
        "type": "keyword"
      },
      "JobLocationRegions": {
        "type": "keyword"
      },
      "JobLocationGeoLatLon": {
        "type": "geo_point"
      },
      "Requirements": {
        "type": "text"
      },
      "Skills": {
        "type": "text"
      },
      "RequiredLanguages": {
        "type": "keyword"
      },
      "SalaryAmount": {
        "type": "integer"
      },
      "SalaryCurrency": {
        "type": "keyword"
      },
      "SalaryType": {
        "type": "integer"
      },
      "IsHomeOfficeJob": {
        "type": "boolean"
      },
      "IsExecutiveJob": {
        "type": "boolean"
      },
      "IsApprenticeshipJob": {
        "type": "boolean"
      },
      "IsStudentJob": {
        "type": "boolean"
      },
      "JobSubCategories": {
        "type": "keyword"
      },
      "IsJobdesk": {
        "type": "boolean"
      },
      "AllowJobdeskOnly": {
        "type": "boolean"
      },
      "JobIsPromoted": {
        "type": "boolean"
      },
      "IsRecruiterCompany": {
        "type": "boolean"
      },
      "MatchingTags": {
        "type": "text"
      },
      "IsDuplicate": {
        "type": "boolean"
      },
      "Duplicates": {
        "type": "keyword"
      },
      "SimHash": {
        "type": "keyword"
      },
      "DEBUG_JobSector": {
        "type": "text"
      },
      "DEBUG_CategoriesStr": {
        "type": "text"
      },
      "IsGovernmentJob": {
        "type": "boolean"
      },
      "IsFeatured": {
        "type": "boolean"
      },
      "IsJobdeskFeatured": {
        "type": "boolean"
      },
      "IsVerifiedCompany": {
        "type": "boolean"
      },
      "PID": {
        "type": "integer"
      },
      "PUID": {
        "type": "keyword"
      },
      "CC": {
        "type": "keyword"
      },
      "LangCode": {
        "type": "keyword"
      },
      "RID": {
        "type": "integer"
      },
      "RefCode": {
        "type": "keyword"
      },
      "Source": {
        "type": "keyword"
      },
      "IsFromAPIUpload": {
        "type": "boolean"
      },
      "Age": {
        "type": "integer"
      },
      "Gender": {
        "type": "integer"
      },
      "Nationality": {
        "type": "keyword"
      },
      "Title": {
        "type": "text"
      },
      "ProfileTageLine": {
        "type": "text"
      },
      "Summary": {
        "type": "text"
      },
      "Rating": {
        "type": "double"
      },
      "ProfilePicture": {
        "type": "text"
      },
      "CoverPhoto": {
        "type": "keyword"
      },
      "AvailabilityStatus": {
        "type": "integer"
      },
      "AvailableFrom": {
        "type": "date"
      },
      "AvailableTo": {
        "type": "date"
      },
      "AvailableInCC": {
        "type": "text"
      },
      "AvailableInCountries": {
        "type": "keyword"
      },
      "AvailableStates": {
        "type": "keyword"
      },
      "AvailableRegions": {
        "type": "keyword"
      },
      "AvailableText": {
        "type": "text"
      },
      "DegreeTypeText": {
        "type": "keyword"
      },
      "ExpectedSalaryText": {
        "type": "text"
      },
      "MinExpectedSalary": {
        "type": "double"
      },
      "MinExpectedSalaryCurrency": {
        "type": "keyword"
      },
      "SalaryUnit": {
        "type": "integer"
      },
      "Sectors": {
        "type": "keyword"
      },
      "EmploymentType": {
        "type": "keyword"
      },
      "ContractType": {
        "type": "keyword"
      },
      "MatchingSkills": {
        "type": "keyword"
      },
      "MatchingLanguages": {
        "type": "keyword"
      },
      "MatchingProfessionTitles": {
        "type": "keyword"
      },
      "MatchingAvailableStates": {
        "type": "keyword"
      },
      "MatchingAvailableRegions": {
        "type": "keyword"
      },
      "CallbackURLA": {
        "type": "keyword"
      },
      "CallbackURLB": {
        "type": "keyword"
      },
      "IsPro": {
        "type": "boolean"
      },
      "IsVerified": {
        "type": "boolean"
      },
      "IsPremium": {
        "type": "boolean"
      },
      "PriceOption": {
        "type": "integer"
      },
      "SalePrice": {
        "type": "double"
      },
      "SaleCurrency": {
        "type": "keyword"
      },
      "PublicationDate": {
        "type": "date"
      },
      "PublisherEmail": {
        "type": "keyword"
      },
      "PermittedUsers": {
        "type": "keyword"
      },
      "ExpiryDate": {
        "type": "date"
      },
      "LastUpdate": {
        "type": "date"
      },
      "IsPhoneVerified": {
        "type": "boolean"
      },
      "IsEmailVerified": {
        "type": "boolean"
      },
"DocUID": { "type": "keyword" },
      "AppClientID": { "type": "keyword" },
      "CID": { "type": "long" },
      "UploadDate": { "type": "date" },
      "contacts_salutation": { "type": "text" },
      "contacts_lastemployer": { "type": "text" },
      "contact_Professions": { "type": "text" },
      "contacts_description": { "type": "text" },
      "contacts_privatefields": { "type": "text" },
      "contactwishes_Occupation": { "type": "text" },
      "contactwishes_Activities": { "type": "text" },
      "contactwishes_Objectives": { "type": "text" },
      "contactwishes_Description": { "type": "text" },
      "contactwishes_WageNotes": { "type": "text" },
      "contactwishes_AvailableNotes": { "type": "text" },
      "contactwishes_RegionNotes": { "type": "text" },
      "contactwishes_Credentials": { "type": "text" },
      "contactwishes_JobChangeReasons": { "type": "text" },
      "contactwishes_CurrentStatus": { "type": "text" },
      "contactwishes_ShortProfile": { "type": "text" },
      "contactwishes_Position": { "type": "text" },
      "contactwishes_Diplomas": { "type": "text" },
      "contactwishes_PreviousApplications": { "type": "text" },
      "contactwishes_wageIST": { "type": "text" },
      "contactwishes_AnonymizedProfile": { "type": "text" },
      "contactwishes_WageNotes2": { "type": "text" },
      "contactwishes_comments": { "type": "text" },
      "contactwishes_AnonymizedProfileTitle": { "type": "text" },
      "contactwishes_RegionText": { "type": "text" },
      "contactwishes_Wage": { "type": "text" },
      "contactwishes_RedemptionCommitment": { "type": "text" },
      "contactwishes_Sectors": { "type": "text" },
      "contactwishes_ExpectationAG": { "type": "text" },
      "contactwishes_ExpectationChef": { "type": "text" },
      "contactwishes_ManagementStyle": { "type": "text" },
      "contactwishes_PreferredCompanies": { "type": "text" },
      "contactwishes_NotPreferredCompanies": { "type": "text" },
      "contactwishes_ProfessionalGoals": { "type": "text" },
      "contactwishes_PersonalGoals": { "type": "text" },
      "contactwishes_PersonalWeeknesses": { "type": "text" },
      "contactwishes_ProfessionalStrengths": { "type": "text" },
      "contactwishes_PersonalStrengths": { "type": "text" },
      "contactwishes_ProfessionalWeeknesses": { "type": "text" },
      "contactwishes_InterviewNotes": { "type": "text" },
      "contactwishes_Professions": { "type": "text" },
      "contactassessmentitems_question": { "type": "text" },
      "contactassessmentitems_answer": { "type": "text" },
      "contactassessmentitems_comment": { "type": "text" },
      "contactskills_skillname": { "type": "text" },
      "contactskills_description": { "type": "text" },
      "contactskills_comments": { "type": "text" },
      "contacteducations_institution": { "type": "text" },
      "contacteducations_fieldofstudy": { "type": "text" },
      "contacteducations_adctivitiesandsocieties": { "type": "text" },
      "contacteducations_description": { "type": "text" },
      "contacteducations_additionalnotes": { "type": "text" },
      "interviewdata_value": { "type": "text" },
      "contactlanguages_iso": { "type": "keyword" },
      "contactprofessionalexperience_institution": { "type": "text" },
      "contactprofessionalexperience_institutioncity": { "type": "text" },
      "contactprofessionalexperience_institutionweb": { "type": "text" },
      "contactprofessionalexperience_worktitle": { "type": "text" },
      "contactprofessionalexperience_description": { "type": "text" },
      "contactprofessionalexperience_reasonjobchange": { "type": "text" },
      "contactprofessionalexperience_interviewnotes": { "type": "text" },
      "contactnotes": { "type": "text" },
      "contactcertifications_certificationname": { "type": "text" },
      "contactcertifications_issuingauthority": { "type": "text" },
      "contactcertifications_licensenumber": { "type": "text" },
      "contactcertifications_url": { "type": "text" },
      "contactqualifications": { "type": "text" },
      "contactcustoms": { "type": "text" },
      "clientjobapplicants_applicantcomments": { "type": "text" },
      "contactreference": { "type": "text" },
      "contactwarnings": { "type": "text" },
      "campaignemails_emailsubject": { "type": "text" },
      "incident": { "type": "text" },
      "jobproposal": { "type": "text" },
      "contactdocuments_all": { "type": "text" },
      "contactdocuments_cv": { "type": "text" },
      "campaignemails_emailcontent": { "type": "text" },
      "contactjobhistory_jobposition": { "type": "text" },
      "contactjobhistory_company": { "type": "text" },
      "contactjobhistory_notesclient": { "type": "text" },
      "contactjobhistory_notes": { "type": "text" }
    }
  }


