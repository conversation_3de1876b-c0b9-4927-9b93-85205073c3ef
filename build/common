builddir = $out
disabled = false

# Each compiler has enabled all the warnings it can.
# Here we make them errors, and disable a few we don't want bothering us.
warnings = -Werror $
           -Wno-double-promotion $
           -Wno-float-equal $
           -Wno-padded $

warnings_cc = $warnings $
           -Wno-c++98-compat-pedantic $
           -Wno-gnu-anonymous-struct $
           -Wno-old-style-cast $


rule compile_c
    command = $disabled && touch $out || $cc -std=c11 -g -Os $warnings $cflags $extra_cflags $
             -MD -MF $out.d -c $in -o $out
    depfile = $out.d
    deps    = gcc
    description = compile $out

rule compile_cc
    command = $disabled && touch $out || $cxx -std=c++11 -g -Os $warnings_cc $cflags $extra_cflags $
             -MD -MF $out.d -c $in -o $out
    depfile = $out.d
    deps    = gcc
    description = compile $out

rule link
    command = $disabled && touch $out || $cxx $ldflags $extra_ldflags $in -ldl -o $out
    description = link $out

include build/targets
