from datetime import datetime, timezone
from jobsitescraper.log_manager import CustomLogger
import scrapy
import re
from bs4 import BeautifulSoup, Comment
import traceback
import sys
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider


class consultandpepper_com_CrawlingManager(scrapy.Spider):
    name = "consultandpepper.com"
    close_down = False
    page_num = 0
    i = 0
    count = -1
    page_no = 0
    available_pages = 1
    isLive = env("PRODUCTION")

    def __init__(self, _config=None, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "consultandpepper.com"
        config["BaseUrl"] = "https://consultandpepper.com"
        config["StartUrl"] = "https://consultandpepper.com/jobs/?_sf_s=%20"
        config["SourceCountry"] = "ch"
        config["LangCode"] = "de"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config["MaxPagesToCrawl"] = 10
        config["MaxJobsToCrawl"] = 500
        config["RecentJobs"] = True
        config['DeleteAllJobsOnStart'] = True
        return config

    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(
                    self.config["SourceKey"], "Crawler Started")
                if self.config["RecentJobs"]:
                    CustomLogger.LogEvent(
                        self.config["SourceKey"], "Recent Data thread started")
                    yield scrapy.Request(self.config["StartUrl"], method='GET', callback=self.parse_recent)
                else:
                    CustomLogger.LogEvent(
                        self.config["SourceKey"], "All Data thread started")
                    yield scrapy.Request(self.config["StartUrl"], method='GET')
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], f"{str(e)}")
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_recent(self, response):
        try:
            for i in response.xpath('//div[@id="s-jobs"]/div/a'):
                url = i.xpath(".//@href").get()
                title = i.xpath('.//h3/text()').get()
                loc = i.xpath('.//span/text()').get()
                logo = i.xpath('.//div/img/@src').get()
                meta = {'title': title, 'loc': loc, 'logo': logo}
                yield response.follow(url, callback=self.parse_job, meta=meta)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], f"{str(e)}")
            print(traceback.format_exc())
            sys.stdout.flush()

    def parse_job(self, response):
        try:
            jobTitle = response.meta['title']
            if jobTitle is not None:
                jobTitle = jobTitle.strip()
            else:
                jobTitle = ''

            jobLocation = "Switzerland"
            if jobLocation is not None:
                jobLocation = jobLocation
            else:
                jobLocation = ''

            logo = response.meta['logo']
            if logo is not None:
                logo = logo
            else:
                logo = 'https://consultandpepper.com/wp-content/uploads/2020/06/cropped-Logo-CP-mit-PRS-Claim-1-1-240x62.png'

            website_text = response.body.decode("UTF-8")
            jobs_soup = BeautifulSoup(website_text.replace("<", " <"), "html.parser")
            description = jobs_soup.find('div', {"class": "site-content"})
            if description is not None:
                for tag in description.find_all(['img', 'a', 'script', 'svg', 'path', 'circle', 'source', 'button', 'footer',
                        'picture', 'style', 'iframe', 'noscript', 'meta', 'link', 'source']):
                    tag.decompose()
                for comment in description.find_all(string=lambda text: isinstance(text, Comment)):
                    comment.extract()
                cleanContent = re.sub('\s+', ' ', description.get_text())
                rawContent = re.sub('\s+', ' ', description.decode_contents())
            else:
                cleanContent = ''
                rawContent = ''

            ad = {}
            ad['JobTitle'] = jobTitle
            ad['JobLocation'] = jobLocation
            ad['CompanyName'] = "Consultandpepper"
            ad['CompanyLogoFileURL'] = logo
            ad["SourceURL"] = response.url
            ad['SourceCountry'] = self.config["SourceCountry"]
            ad['SourceKey'] = self.config["SourceKey"]
            ad['SourceLangCode'] = self.config["LangCode"]
            ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
            ad['SourceUID'] = response.url
            ad['CleanContent'] = cleanContent
            ad['RawContent'] = rawContent
            ad['PostedDate'] = datetime.now(timezone.utc).astimezone().isoformat()
            emailList = re.findall(
                '\S+@\S+', cleanContent.strip("\n"))
            phoneList = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]',
                                   cleanContent.strip("\n").replace('\u00a0', ' '))
            if len(emailList) > 0:
                _email = emailList[0]
                ad['JobContactEmails'] = _email
            if len(phoneList) > 0:
                for i in range(len(phoneList)):
                    phone = phoneList[i].strip().strip('(').strip(')')
                    if len(phone) > 0:
                        ad['JobContactPhone'] = phone
            if self.config["Upload"] is True:
                self.count += 1
                if self.count >= self.config["MaxJobsToCrawl"]:
                    self.crawler.engine.close_spider(self, 'Scraped jobs.')
                else:
                    yield ad
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
