import json
import spacy

nlp = spacy.load("en_core_web_md")  # Use a model with word vectors

job_text = "We are looking for a software engineer with experience in ASP.NET"
job_doc = nlp(job_text)

with open("Data/cv.json", "r") as file:
    data = json.load(file)

results = []

for item in data:
    cv_text = item['CVText']
    cv_doc = nlp(cv_text)
    similarity = job_doc.similarity(cv_doc)
    skills = [ent.text for ent in cv_doc.ents]
    results.append({
        'UUID': item['UUID'],
        'Skills': ", ".join(skills),
        'Similarity': similarity,
        'CVText': cv_text
    })

for r in results:
    print(json.dumps(r, indent=2))
