<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <comment>
  Copyright (c) the JPEG XL Project Authors. All rights reserved.

  Use of this source code is governed by a BSD-style
  license that can be found in the LICENSE file.
 </comment>
 <class>ViewerWindow</class>
 <widget class="QMainWindow" name="ViewerWindow">
  <property name="windowTitle">
   <string>JPEG XL Viewer</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QScrollArea" name="scrollArea">
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="widgetResizable">
       <bool>true</bool>
      </property>
      <widget class="QLabel" name="image">
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>&amp;File</string>
    </property>
    <addaction name="actionOpen"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <addaction name="menuFile"/>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionOpen"/>
   <addaction name="actionPreviousImage"/>
   <addaction name="actionNextImage"/>
  </widget>
  <action name="actionOpen">
   <property name="icon">
    <iconset theme="document-open"/>
   </property>
   <property name="text">
    <string>&amp;Open…</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionExit">
   <property name="icon">
    <iconset theme="application-exit"/>
   </property>
   <property name="text">
    <string>E&amp;xit</string>
   </property>
   <property name="menuRole">
    <enum>QAction::QuitRole</enum>
   </property>
  </action>
  <action name="actionPreviousImage">
   <property name="icon">
    <iconset theme="go-previous"/>
   </property>
   <property name="text">
    <string>Previous image</string>
   </property>
   <property name="shortcut">
    <string>Left</string>
   </property>
  </action>
  <action name="actionNextImage">
   <property name="icon">
    <iconset theme="go-next"/>
   </property>
   <property name="text">
    <string>Next image</string>
   </property>
   <property name="shortcut">
    <string>Right</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>actionExit</sender>
   <signal>triggered()</signal>
   <receiver>ViewerWindow</receiver>
   <slot>close()</slot>
  </connection>
 </connections>
</ui>
