from ollama import Client

# Test LLM connection
def test_llm():
    try:
        client = Client(host="http://localhost:11434")
        
        # Simple test prompt
        prompt = "What is 2+2?"
        print(f"Testing with prompt: {prompt}")
        
        response = client.generate(
            model="deepseek-r1:latest", 
            prompt=prompt, 
            stream=False
        )
        
        print(f"Response: {response['response']}")
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print("Testing Ollama connection...")
    success = test_llm()
    if success:
        print("LLM test successful!")
    else:
        print("LLM test failed!")
