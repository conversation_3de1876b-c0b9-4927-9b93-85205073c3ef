.\"
.\" Copyright (c) 1991-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH SGI2TIFF 1 "November 2, 2005" "libtiff"
.SH NAME
sgi2tiff \- create a
.SM TIFF
file from an
.SM SGI
image file
.SH SYNOPSIS
.B sgi2tiff
[
.I options
]
.I input.rgb
.I output.tif
.SH DESCRIPTION
.I sgi2tiff
converts a file in the 
.SM SGI
image format to
.SM TIFF.
By default, the
.SM TIFF
image is created with data samples packed (\c
.IR PlanarConfiguration =1),
compressed with the Lempel-Ziv & Welch algorithm (\c
.IR Compression =5),
and with each strip no more than 8 kilobytes.
These characteristics can overridden, or explicitly specified
with the options described below.
.SH OPTIONS
.TP
.B \-c
Specify a compression scheme to use when writing image data:
.B "\-c none"
for no compression,
.B "\-c packbits"
for the PackBits compression algorithm),
.B "\-c jpeg"
for the baseline JPEG compression algorithm,
.B "\-c zip
for the Deflate compression algorithm,
and
.B "\-c lzw"
for Lempel-Ziv & Welch (the default).
.TP
.B \-p
Explicitly select the planar configuration used in organizing
data samples in the output image:
.B "\-p contig"
for samples packed contiguously, and
.B "\-p separate"
for samples stored separately.
By default samples are packed.
.TP
.B \-r
Write data with a specified number of rows per strip;
by default the number of rows/strip is selected so that each strip
is approximately 8 kilobytes.
.SH BUGS
Does not record colormap information.
.SH "SEE ALSO"
.BR tiffinfo (1),
.BR tiffcp (1),
.BR tiffmedian (1),
.BR libtiff (3)
.PP
Libtiff library home page:
.BR http://www.remotesensing.org/libtiff/
