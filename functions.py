import os
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords

try:
    nltk.download('punkt')
    nltk.download('stopwords')
except:
    print("Problem downloading NLTK stuff, but let's try anyway!")


# Function to read the chat file
def read_chat_file(file_name):
    if not os.path.exists(file_name):
        print("Oops! Can't find the file", file_name)
        return [], []
    user_msgs = []
    ai_msgs = []
    file = open(file_name, 'r')
    lines = file.readlines()
    file.close()
    for line in lines:
        line = line.strip()
        if line.startswith("User:"):
            user_msgs.append(line[5:].strip())
        elif line.startswith("AI:"):
            ai_msgs.append(line[3:].strip())
    return user_msgs, ai_msgs


# Function to count messages
def count_messages(user_msgs, ai_msgs):
    user_count = len(user_msgs)
    ai_count = len(ai_msgs)
    total = user_count + ai_count
    exchanges = user_count
    if ai_count < user_count:
        exchanges = ai_count
    return total, user_count, ai_count, exchanges


# Function to find top 5 words using NLTK
def find_top_words(messages):
    stop_words = stopwords.words('english')
    all_text = ""
    for msg in messages:
        all_text = all_text + " " + msg.lower()
    words = word_tokenize(all_text)
    good_words = []
    for word in words:
        is_letter = True
        for letter in word:
            if not letter.isalpha():
                is_letter = False
        if is_letter and word not in stop_words and len(word) > 2:
            good_words.append(word)
    word_counts = {}
    for word in good_words:
        if word in word_counts:
            word_counts[word] = word_counts[word] + 1
        else:
            word_counts[word] = 1
    sorted_words = []
    for word in word_counts:
        sorted_words.append([word, word_counts[word]])
    for i in range(len(sorted_words)):
        for j in range(i + 1, len(sorted_words)):
            if sorted_words[i][1] < sorted_words[j][1]:
                temp = sorted_words[i]
                sorted_words[i] = sorted_words[j]
                sorted_words[j] = temp
    top_words = []
    for i in range(min(5, len(sorted_words))):
        top_words.append(sorted_words[i])
    return top_words


# Function to guess the topic
def guess_topic(top_words):
    words = []
    for word, count in top_words:
        words.append(word)
    if 'python' in words or 'programming' in words or 'code' in words:
        return "programming or Python stuff"
    elif 'machine' in words or 'learning' in words or 'ai' in words or 'data' in words:
        return "AI or machine learning stuff"
    else:
        return "general stuff"


# Function to make the summary
def make_summary(file_name, user_msgs, ai_msgs):
    total_msgs, user_count, ai_count, exchanges = count_messages(user_msgs, ai_msgs)
    all_msgs = user_msgs + ai_msgs
    top_words = find_top_words(all_msgs)
    topic = guess_topic(top_words)
    words_str = ""
    for i in range(len(top_words)):
        word, count = top_words[i]
        words_str = words_str + word + " (" + str(count) + ")"
        if i < len(top_words) - 1:
            words_str = words_str + ", "

    summary = "Summary for " + file_name + ":\n"
    summary += "- The conversation had " + str(exchanges) + " exchanges.\n"
    summary += "- Total messages: " + str(total_msgs) + " (User: " + str(user_count) + ", AI: " + str(ai_count) + ").\n"
    summary += "- The user asked mainly about " + topic + ".\n"
    summary += "- Most common keywords: " + words_str + ".\n"
    output_file = open("Data/summary.txt", "w")
    output_file.write(summary)
    output_file.close()
    print("Summary saved to summary.txt")
