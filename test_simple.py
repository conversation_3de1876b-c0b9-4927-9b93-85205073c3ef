import fitz  # PyMuPDF
import re
from ollama import Client

# === Step 1: Extract text from PDF ===
def extract_text_from_pdf(pdf_path: str) -> str:
    doc = fitz.open(pdf_path)
    text = ""
    for page in doc:
        text += page.get_text()
    return text

# === Step 2: Preprocess and Chunk ===
def preprocess_text(text: str) -> list:
    # Clean text
    text = re.sub(r"\n+", "\n", text)
    text = re.sub(r"\s+", " ", text)

    # Split into sentences and group them into chunks
    sentences = re.split(r'[।!?]', text)
    chunks = []
    current_chunk = ""

    for sentence in sentences:
        sentence = sentence.strip()
        if len(sentence) < 10:  # Skip very short sentences
            continue

        # If adding this sentence would make chunk too long, start new chunk
        if len(current_chunk) + len(sentence) > 500:
            if current_chunk:
                chunks.append(current_chunk.strip())
            current_chunk = sentence
        else:
            current_chunk += " " + sentence if current_chunk else sentence

    # Add the last chunk
    if current_chunk:
        chunks.append(current_chunk.strip())

    return [chunk for chunk in chunks if len(chunk) > 30]

# === Step 3: Simple keyword-based search ===
def simple_search(chunks: list, query: str, k=3):
    query_words = query.lower().split()
    scored_chunks = []
    
    for chunk in chunks:
        score = 0
        chunk_lower = chunk.lower()
        for word in query_words:
            score += chunk_lower.count(word)
        if score > 0:
            scored_chunks.append((score, chunk))
    
    # Sort by score and return top k
    scored_chunks.sort(reverse=True, key=lambda x: x[0])
    return [chunk for score, chunk in scored_chunks[:k]]

# === Step 4: Use Open Source LLM via Ollama ===
class LLM:
    def __init__(self, model_name="llama2:latest"):
        self.client = Client(host="http://localhost:11434")
        self.model = model_name

    def generate(self, query: str, context: list) -> str:
        prompt = f"""
Answer the following question based only on the context below. If answer not found, say "Not found in document".

Context:
{' '.join(context)}

Question: {query}
Answer:
"""
        response = self.client.generate(model=self.model, prompt=prompt, stream=False)
        return response['response'].strip()

# Test the system
if __name__ == "__main__":
    print("Extracting text from PDF...")
    pdf_path = "Data/HSC26-Bangla1st-Paper.pdf"
    raw_text = extract_text_from_pdf(pdf_path)
    print(f"Extracted {len(raw_text)} characters")
    
    print("Preprocessing text...")
    chunks = preprocess_text(raw_text)
    print(f"Created {len(chunks)} chunks")
    
    # Test query
    query = "অনপুেমর ভাষায় সুপুরুষ কাকে বলা হেয়েছ?"
    print(f"\nQuery: {query}")
    
    print("Searching for relevant chunks...")
    top_chunks = simple_search(chunks, query)
    print(f"Found {len(top_chunks)} relevant chunks")
    
    for i, chunk in enumerate(top_chunks):
        print(f"Chunk {i+1}: {chunk[:100]}...")
    
    print("\nGenerating answer with LLM...")
    llm = LLM()
    answer = llm.generate(query, top_chunks)
    print(f"Answer: {answer}")
