import fitz  # PyMuPDF
import re
import os
import faiss
import torch
import numpy as np
from fastapi import FastAPI, Request
from pydantic import BaseModel
from sentence_transformers import SentenceTransformer
from typing import List
from ollama import Client

# === Step 1: Extract text from PDF ===
def extract_text_from_pdf(pdf_path: str) -> str:
    doc = fitz.open(pdf_path)
    text = ""
    for page in doc:
        text += page.get_text()
    return text

# === Step 2: Preprocess and Chunk ===
def preprocess_text(text: str) -> List[str]:
    text = re.sub(r"\n+", "\n", text)
    text = re.sub(r"\s+", " ", text)
    paragraphs = [p.strip() for p in text.split("\n") if len(p.strip()) > 30]
    return paragraphs

# === Step 3: Embed & Store in FAISS ===
class VectorStore:
    def __init__(self, model_name="all-MiniLM-L6-v2"):
        self.model = SentenceTransformer(model_name)
        self.index = None
        self.text_chunks = []

    def add_documents(self, chunks: List[str]):
        self.text_chunks = chunks
        embeddings = self.model.encode(chunks, convert_to_numpy=True, show_progress_bar=True)
        dim = embeddings.shape[1]
        self.index = faiss.IndexFlatL2(dim)
        self.index.add(embeddings)

    def search(self, query: str, k=3):
        q_embedding = self.model.encode([query], convert_to_numpy=True)
        D, I = self.index.search(q_embedding, k)
        return [self.text_chunks[i] for i in I[0]]

# === Step 4: Use Open Source LLM via Ollama ===
class LLM:
    def __init__(self, model_name="deepseek-r1:latest"):
        self.client = Client(host="http://localhost:11434")  # Make sure ollama is running
        self.model = model_name

    def generate(self, query: str, context: List[str]) -> str:
        prompt = f"""
Answer the following question based only on the context below. If answer not found, say "Not found in document".

Context:
{''.join(context)}

Question: {query}
Answer:
"""
        response = self.client.generate(model=self.model, prompt=prompt, stream=False)
        return response['response'].strip()

# === Step 5: Setup FastAPI ===
class QueryInput(BaseModel):
    query: str

app = FastAPI()
pdf_path = "Data/HSC26-Bangla1st-Paper.pdf"
raw_text = extract_text_from_pdf(pdf_path)
chunks = preprocess_text(raw_text)

# Init vector store and embed
vs = VectorStore()
vs.add_documents(chunks)

# Init LLM
llm = LLM("llama2:latest")

@app.post("/ask")
def ask_question(data: QueryInput):
    top_chunks = vs.search(data.query)
    answer = llm.generate(data.query, top_chunks)
    return {
        "query": data.query,
        "answer": answer,
        "top_chunks": top_chunks
    }

# Example Run (optional for CLI)
if __name__ == "__main__":
    q = "অনপুেমর ভাষায় সুপুরুষ কাকে বলা হেয়েছ?"
    top = vs.search(q)
    print("Top Chunks:", top)
    print("Answer:", llm.generate(q, top))
