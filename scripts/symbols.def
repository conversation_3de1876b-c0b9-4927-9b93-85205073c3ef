;--------------------------------------------------------------
; LIBPNG symbol list as a Win32 DEF file
; Contains all the symbols that can be exported from libpng
;--------------------------------------------------------------
LIBRARY

EXPORTS
 png_access_version_number @1
 png_set_sig_bytes @2
 png_sig_cmp @3
 png_create_read_struct @4
 png_create_write_struct @5
 png_get_compression_buffer_size @6
 png_set_compression_buffer_size @7
 png_set_longjmp_fn @8
 png_longjmp @9
 png_reset_zstream @10
 png_create_read_struct_2 @11
 png_create_write_struct_2 @12
 png_write_sig @13
 png_write_chunk @14
 png_write_chunk_start @15
 png_write_chunk_data @16
 png_write_chunk_end @17
 png_create_info_struct @18
 png_info_init_3 @19
 png_write_info_before_PLTE @20
 png_write_info @21
 png_read_info @22
 png_convert_to_rfc1123 @23
 png_convert_from_struct_tm @24
 png_convert_from_time_t @25
 png_set_expand @26
 png_set_expand_gray_1_2_4_to_8 @27
 png_set_palette_to_rgb @28
 png_set_tRNS_to_alpha @29
 png_set_bgr @30
 png_set_gray_to_rgb @31
 png_set_rgb_to_gray @32
 png_set_rgb_to_gray_fixed @33
 png_get_rgb_to_gray_status @34
 png_build_grayscale_palette @35
 png_set_strip_alpha @36
 png_set_swap_alpha @37
 png_set_invert_alpha @38
 png_set_filler @39
 png_set_add_alpha @40
 png_set_swap @41
 png_set_packing @42
 png_set_packswap @43
 png_set_shift @44
 png_set_interlace_handling @45
 png_set_invert_mono @46
 png_set_background @47
 png_set_strip_16 @48
 png_set_quantize @49
 png_set_gamma @50
 png_set_flush @51
 png_write_flush @52
 png_start_read_image @53
 png_read_update_info @54
 png_read_rows @55
 png_read_row @56
 png_read_image @57
 png_write_row @58
 png_write_rows @59
 png_write_image @60
 png_write_end @61
 png_read_end @62
 png_destroy_info_struct @63
 png_destroy_read_struct @64
 png_destroy_write_struct @65
 png_set_crc_action @66
 png_set_filter @67
 png_set_filter_heuristics @68
 png_set_compression_level @69
 png_set_compression_mem_level @70
 png_set_compression_strategy @71
 png_set_compression_window_bits @72
 png_set_compression_method @73
 png_init_io @74
 png_set_error_fn @75
 png_get_error_ptr @76
 png_set_write_fn @77
 png_set_read_fn @78
 png_get_io_ptr @79
 png_set_read_status_fn @80
 png_set_write_status_fn @81
 png_set_mem_fn @82
 png_get_mem_ptr @83
 png_set_read_user_transform_fn @84
 png_set_write_user_transform_fn @85
 png_set_user_transform_info @86
 png_get_user_transform_ptr @87
 png_set_read_user_chunk_fn @88
 png_get_user_chunk_ptr @89
 png_set_progressive_read_fn @90
 png_get_progressive_ptr @91
 png_process_data @92
 png_progressive_combine_row @93
 png_malloc @94
 png_calloc @95
 png_malloc_warn @96
 png_free @97
 png_free_data @98
 png_data_freer @99
 png_malloc_default @100
 png_free_default @101
 png_error @102
 png_chunk_error @103
 png_err @104
 png_warning @105
 png_chunk_warning @106
 png_benign_error @107
 png_chunk_benign_error @108
 png_set_benign_errors @109
 png_get_valid @110
 png_get_rowbytes @111
 png_get_rows @112
 png_set_rows @113
 png_get_channels @114
 png_get_image_width @115
 png_get_image_height @116
 png_get_bit_depth @117
 png_get_color_type @118
 png_get_filter_type @119
 png_get_interlace_type @120
 png_get_compression_type @121
 png_get_pixels_per_meter @122
 png_get_x_pixels_per_meter @123
 png_get_y_pixels_per_meter @124
 png_get_pixel_aspect_ratio @125
 png_get_x_offset_pixels @126
 png_get_y_offset_pixels @127
 png_get_x_offset_microns @128
 png_get_y_offset_microns @129
 png_get_signature @130
 png_get_bKGD @131
 png_set_bKGD @132
 png_get_cHRM @133
 png_get_cHRM_fixed @134
 png_set_cHRM @135
 png_set_cHRM_fixed @136
 png_get_gAMA @137
 png_get_gAMA_fixed @138
 png_set_gAMA @139
 png_set_gAMA_fixed @140
 png_get_hIST @141
 png_set_hIST @142
 png_get_IHDR @143
 png_set_IHDR @144
 png_get_oFFs @145
 png_set_oFFs @146
 png_get_pCAL @147
 png_set_pCAL @148
 png_get_pHYs @149
 png_set_pHYs @150
 png_get_PLTE @151
 png_set_PLTE @152
 png_get_sBIT @153
 png_set_sBIT @154
 png_get_sRGB @155
 png_set_sRGB @156
 png_set_sRGB_gAMA_and_cHRM @157
 png_get_iCCP @158
 png_set_iCCP @159
 png_get_sPLT @160
 png_set_sPLT @161
 png_get_text @162
 png_set_text @163
 png_get_tIME @164
 png_set_tIME @165
 png_get_tRNS @166
 png_set_tRNS @167
 png_get_sCAL @168
 png_get_sCAL_s @169
 png_set_sCAL @170
 png_set_sCAL_s @171
 png_set_keep_unknown_chunks @172
 png_handle_as_unknown @173
 png_set_unknown_chunks @174
 png_set_unknown_chunk_location @175
 png_get_unknown_chunks @176
 png_set_invalid @177
 png_read_png @178
 png_write_png @179
 png_get_copyright @180
 png_get_header_ver @181
 png_get_header_version @182
 png_get_libpng_ver @183
 png_permit_mng_features @184
 png_set_strip_error_numbers @185
 png_set_user_limits @186
 png_get_user_width_max @187
 png_get_user_height_max @188
 png_set_chunk_cache_max @189
 png_get_chunk_cache_max @190
 png_set_chunk_malloc_max @191
 png_get_chunk_malloc_max @192
 png_get_pixels_per_inch @193
 png_get_x_pixels_per_inch @194
 png_get_y_pixels_per_inch @195
 png_get_x_offset_inches @196
 png_get_y_offset_inches @197
 png_get_pHYs_dpi @198
 png_get_io_state @199
 png_get_uint_32 @201
 png_get_uint_16 @202
 png_get_int_32 @203
 png_get_uint_31 @204
 png_save_uint_32 @205
 png_save_int_32 @206
 png_save_uint_16 @207
 png_set_gamma_fixed @208
 png_set_filter_heuristics_fixed @209
 png_get_pixel_aspect_ratio_fixed @210
 png_get_x_offset_inches_fixed @211
 png_get_y_offset_inches_fixed @212
 png_set_sCAL_fixed @213
 png_get_sCAL_fixed @214
 png_set_background_fixed @215
 png_get_io_chunk_type @216
 png_get_current_row_number @217
 png_get_current_pass_number @218
 png_process_data_pause @219
 png_process_data_skip @220
 png_set_expand_16 @221
 png_set_text_compression_level @222
 png_set_text_compression_mem_level @223
 png_set_text_compression_strategy @224
 png_set_text_compression_window_bits @225
 png_set_text_compression_method @226
 png_set_alpha_mode @227
 png_set_alpha_mode_fixed @228
 png_set_scale_16 @229
 png_get_cHRM_XYZ @230
 png_get_cHRM_XYZ_fixed @231
 png_set_cHRM_XYZ @232
 png_set_cHRM_XYZ_fixed @233
 png_image_begin_read_from_file @234
 png_image_begin_read_from_stdio @235
 png_image_begin_read_from_memory @236
 png_image_finish_read @237
 png_image_free @238
 png_image_write_to_file @239
 png_image_write_to_stdio @240
 png_convert_to_rfc1123_buffer @241
 png_set_check_for_invalid_index @242
 png_get_palette_max @243
 png_set_option @244
 png_image_write_to_memory @245
 png_get_eXIf @246
 png_set_eXIf @247
 png_get_eXIf_1 @248
 png_set_eXIf_1 @249
