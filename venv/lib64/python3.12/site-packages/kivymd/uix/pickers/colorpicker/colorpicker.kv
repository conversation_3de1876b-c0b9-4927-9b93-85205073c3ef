#:import STANDARD_INCREMENT kivymd.material_resources.STANDARD_INCREMENT
#:import images_path kivymd.images_path
#:import colors kivymd.color_definitions.colors
#:import Window kivy.core.window.Window


<SelectAlphaChannelWidget>
    orientation: "vertical"
    adaptive_height: True
    spacing: "12dp"
    padding: 0, 0, 0, "8dp"

    FitImage:
        size_hint_y: None
        height: "36dp"
        source: f"{images_path}/alpha_layer.png"
        radius: [8,]

        canvas.after:
            Color:
                rgba:
                    root._rgb[:-1] + [root._opacity_value_selected_color]
            RoundedRectangle:
                pos: self.pos
                size: self.size
                radius: [8,]

    MDSlider:
        id: slider
        size_hint_y: None
        height: "12dp"
        hint: False
        max: 1
        value: root._opacity_value_selected_color
        on_value:
            root._opacity_value_selected_color = self.value
            if root.color_picker: \
            root.color_picker._opacity_value_selected_color = self.value


<SliderItem@MDBoxLayout>
    spacing: "12dp"
    color_slider: "Red"
    max: 255
    adaptive_height: True

    MDSlider:
        id: slider
        size_hint_y: None
        height: "36dp"
        color: colors[root.color_slider]["500"]
        max: root.max
        value: 1 if root.max == 1 else 0
        on_value:
            root.parent.dispatch("on_slide_value", root.parent.get_color())

    MDLabel:
        adaptive_size: True
        -text_size: None, None
        pos_hint: {"center_y": .5}
        text:
            str(int(slider.value)) \
            if root.max != 1 \
            else str(round(slider.value, 1))


<SliderTab>
    orientation: "vertical"
    padding: "12dp", "24dp", "12dp", 0
    spacing: "24dp"

    SliderItem:
        id: slider_red
        color_slider: "Red"

    SliderItem:
        id: slider_green
        color_slider: "Green"

    SliderItem:
        id: slider_blue
        color_slider: "Blue"

    Widget:

    SelectAlphaChannelWidget:
        id: select_alpha_channel_widget
        color_picker: root.color_picker


<GradientTab>
    orientation: "vertical"
    padding: "12dp", "12dp", "12dp", 0
    spacing: "8dp"

    MDBoxLayout:
        id: color_selection_box
        spacing: "12dp"

        Widget:
            id: gradient_widget

        MDBoxLayout:
            orientation: "vertical"
            size_hint_x: None
            width: "24dp"

            canvas.before:
                StencilPush
                RoundedRectangle:
                    size: self.size
                    pos: self.pos
                    radius: root.color_picker.radius_color_scale
                StencilUse

            canvas.after:
                StencilUnUse
                RoundedRectangle:
                    size: self.size
                    pos: self.pos
                    radius: root.color_picker.radius_color_scale
                StencilPop

            Image:
                source: f"{images_path}/blue.png"
                allow_stretch: True
                keep_ratio: False
                on_touch_down:
                    if self.collide_point(*args[1].pos): \
                    root.updated_canvas(self, args[1])

            Image:
                source: f"{images_path}/green.png"
                allow_stretch: True
                keep_ratio: False
                on_touch_down:
                    if self.collide_point(*args[1].pos): \
                    root.updated_canvas(self, args[1])

            Image:
                source: f"{images_path}/yellow.png"
                allow_stretch: True
                keep_ratio: False
                on_touch_down:
                    if self.collide_point(*args[1].pos): \
                    root.updated_canvas(self, args[1])

            Image:
                source: f"{images_path}/red.png"
                allow_stretch: True
                keep_ratio: False
                on_touch_down:
                    if self.collide_point(*args[1].pos): \
                    root.updated_canvas(self, args[1])

            Image:
                source: f"{images_path}/black.png"
                allow_stretch: True
                keep_ratio: False
                on_touch_down:
                    if self.collide_point(*args[1].pos): \
                    root.updated_canvas(self, args[1])

    SelectAlphaChannelWidget:
        id: select_alpha_channel_widget
        color_picker: root.color_picker


<TabColorList>
    rv: rv

    RecycleView:
        id: rv
        key_viewclass: "viewclass"
        key_size: "height"

        RecycleBoxLayout:
            orientation: "vertical"
            size_hint_y: None
            height: self.minimum_height
            padding: "8dp"
            spacing: "8dp"
            default_size_hint: 1, None
            default_size: None, dp(48)


<ColorListItem>
    size_hint_y: None
    padding: "12dp"
    md_bg_color: root.color
    radius: [8,]

    MDLabel:
        text: root.hue_code
        theme_text_color: "Custom"
        text_color: root.text_color
        halign: "center"


<MDColorPicker>
    # These are the sums of the widths of the `TypeColorButton` buttons in the
    # `type_color_button_box` box.
    size_hint_min_x: dp(264)

    MDBoxLayout:
        orientation: "vertical"

        MDBoxLayout:
            id: header
            orientation: "vertical"
            padding: 0, "8dp", 0, 0
            spacing: "8dp"
            radius: root.radius[:2] + [0, 0]
            size_hint_y: None
            height: STANDARD_INCREMENT
            md_bg_color:
                app.theme_cls.primary_color \
                if not root.default_color \
                else root.default_color

            MDLabel:
                id: lbl_color_value
                halign: "center"
                shorten: True
                bold: True
                markup: True

            MDBoxLayout:
                id: type_color_button_box
                adaptive_height: True

                TypeColorButton:
                    text: "HEX"
                    group: "x"
                    size_hint_x: 1
                    on_release: root.type_color = self.text

                TypeColorButton:
                    text: "RGB"
                    group: "x"
                    size_hint_x: 1
                    on_release: root.type_color = self.text

                TypeColorButton:
                    text: "RGBA"
                    group: "x"
                    size_hint_x: 1
                    on_release: root.type_color = self.text

        MDBottomNavigation:
            id: bottom_navigation
            use_text: False
            on_switch_tabs: root.dispatch("on_switch_tabs", *args)

            MDBottomNavigationItem:
                id: bottom_navigation_gradient
                name: "bottom navigation gradient"
                icon: "gradient-vertical"

            MDBottomNavigationItem:
                id: view_headline
                name: "view headline"
                icon: "view-headline"

                ColorListTab:
                    id: color_list_tabs
                    text_color_normal: 0, 0, 0, 1
                    on_tab_switch: self.generates_list_colors(*args)
                    color_picker: root

            MDBottomNavigationItem:
                id: tune
                name: "tune"
                icon: "tune"

                SliderTab:
                    color_picker: root
                    on_slide_value:
                        root.dispatch("on_select_color", args[1])

        MDBoxLayout:
            size_hint_y: None
            height: "48dp"
            md_bg_color: app.theme_cls.bg_dark
            radius: [0, 0] + root.radius[2:]

            MDFlatButton:
                text: root.text_button_ok
                size_hint: 1, 1
                on_release:
                    root.dispatch( \
                    "on_release", \
                    root.type_color, \
                    root._get_selected_color(root.selected_color))

            MDFlatButton:
                text: root.text_button_cancel
                size_hint: 1, 1
                on_release: root.dismiss()
