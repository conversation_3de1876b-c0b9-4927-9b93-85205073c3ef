#:import SNACK_BAR_ELEVATION kivymd.material_resources.SNACK_BAR_ELEVATION
#:import SNACK_BAR_OFFSET kivymd.material_resources.SNACK_BAR_OFFSET


<MDSnackbar>
    padding: 0, 0, "8dp", 0
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: "#323232"
    elevation: SNACK_BAR_ELEVATION
    shadow_offset: SNACK_BAR_OFFSET

    SnackbarLabelContainer:
        id: label_container
        padding: "16dp", "15dp", 0, "15dp"
        orientation: "vertical"
        adaptive_height: True
        pos_hint: {"center_y": .5}
        spacing: "4dp"

    SnackbarActionButtonContainer:
        id: action_container
        size_hint_x: None

    SnackbarCloseButtonContainer:
        id: close_container
        size_hint_x: None
        width: "38dp"
