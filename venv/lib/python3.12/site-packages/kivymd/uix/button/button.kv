<BaseButton>
    canvas:
        Clear
        Color:
            group: "bg-color"
            rgba:
                self._md_bg_color \
                if not self.disabled else \
                self._md_bg_color_disabled
        RoundedRectangle:
            size: self.size
            pos: self.pos
            source: self.source if hasattr(self, "source") else ""
            radius: [root._radius, ]
        Color:
            group: "outline-color"
            rgba:
                root._line_color \
                if not root.disabled else \
                (root._line_color_disabled or self._disabled_color)
        Line:
            width: root.line_width
            rounded_rectangle:
                ( \
                self.x, self.y, self.width, self.height, \
                root._radius, root._radius, root._radius, root._radius, \
                self.height \
                )

    size_hint: None, None
    anchor_x: root.halign
    anchor_y: root.valign
    _round_rad: [self._radius] * 4


<ButtonContentsText>
    lbl_txt: lbl_txt
    width:
        max( \
        root._min_width, \
        root.padding[0] + lbl_txt.texture_size[0] + root.padding[2] \
        )
    size_hint_min_x:
        max( \
        root._min_width, \
        root.padding[0] + lbl_txt.texture_size[0] + root.padding[2] \
        )
    height:
        max( \
        root._min_height, \
        root.padding[1] + lbl_txt.texture_size[1] + root.padding[3] \
        )
    size_hint_min_y:
        max( \
        root._min_height, \
        root.padding[1] + lbl_txt.texture_size[1] + root.padding[3] \
        )

    MDLabel:
        id: lbl_txt
        text: root.text
        font_size: root.font_size
        font_style: root.font_style
        halign: 'center'
        valign: 'middle'
        adaptive_size: True
        -text_size: None, None
        theme_text_color: root._theme_text_color
        text_color: root._text_color
        markup: True
        disabled: root.disabled
        opposite_colors: root.opposite_colors
        font_name: root.font_name if root.font_name else self.font_name


<ButtonContentsIcon>
    lbl_ic: lbl_ic
    size: "48dp", "48dp"
    padding: "12dp" if root.icon in md_icons else (0, 0, 0, 0)
    # Backwards compatibility.
    theme_icon_color: root.theme_icon_color or root.theme_text_color

    MDIcon:
        id: lbl_ic
        icon: root.icon
        font_size: root.icon_size if root.icon_size else self.font_size
        font_name: root.font_name if root.font_name else self.font_name
        opposite_colors: root.opposite_colors
        text_color:
            # FIXME: ValueError: None is not allowed for MDIcon.text_color.
            #  This is only a temporary fix and does not fix the cause of the error.
            (root._icon_color if root._icon_color else root.theme_cls.text_color) \
            if not root.disabled else \
            root.theme_cls.disabled_hint_text_color \
            if not root.disabled_color else \
            root.disabled_color
        # Fix https://github.com/kivymd/KivyMD/issues/1448
        # TODO: Perhaps this change may affect other widgets.
        #  You need to create tests.
        # on_icon:
        #     if self.icon not in md_icons.keys(): self.size_hint = (1, 1)
        theme_text_color: root._theme_icon_color


<ButtonContentsIconText>
    lbl_txt: lbl_txt
    lbl_ic: lbl_ic

    width:
        max( \
        root._min_width, \
        root.padding[0] \
        + lbl_ic.texture_size[0] \
        + box.spacing \
        + lbl_txt.texture_size[0] \
        + root.padding[2] \
        )
    size_hint_min_x:
        max( \
        root._min_width, \
        root.padding[0] \
        + lbl_ic.texture_size[0] \
        + box.spacing \
        + lbl_txt.texture_size[0] \
        + root.padding[2] \
        )
    height:
        max( \
        root._min_height, \
        root.padding[1] \
        + max(lbl_ic.texture_size[1], lbl_txt.texture_size[1]) \
        + root.padding[3] \
        )
    size_hint_min_y:
        max( \
        root._min_height, \
        root.padding[1] \
        + max(lbl_ic.texture_size[1], lbl_txt.texture_size[1]) \
        + root.padding[3] \
        )

    MDBoxLayout:
        id: box
        adaptive_size: True
        padding: 0
        spacing: "8dp"

        MDIcon:
            id: lbl_ic
            size_hint_x: None
            pos_hint: {"center_y": .5}
            icon: root.icon
            opposite_colors: root.opposite_colors
            font_size:
                root.icon_size \
                if root.icon_size else \
                (18 / 14 * lbl_txt.font_size)
            text_color:
                root._icon_color \
                if not root.disabled else \
                root.theme_cls.disabled_hint_text_color
            theme_text_color: root._theme_icon_color

        MDLabel:
            id: lbl_txt
            adaptive_size: True
            -text_size: None, None
            pos_hint: {"center_y": .5}
            halign: 'center'
            valign: 'middle'
            text: root.text
            font_size: root.font_size
            font_style: root.font_style
            font_name: root.font_name if root.font_name else self.font_name
            theme_text_color: root._theme_text_color
            text_color: root._text_color
            markup: True
            disabled: root.disabled
            opposite_colors: root.opposite_colors


<MDTextButton>
    adaptive_size: True
    color: root.theme_cls.primary_color if not root.color else root.color
    opacity: 1


<BaseFloatingBottomButton>
    theme_text_color: "Custom"
    md_bg_color: self.theme_cls.primary_color

    canvas.before:
        Color:
            rgba:
                self.theme_cls.primary_color \
                if not self._bg_color else \
                self._bg_color
        RoundedRectangle:
            pos:
                (self.x - self._canvas_width + dp(1.5)) + self._padding_right / 2, \
                self.y - self._padding_right / 2 + dp(1.5)
            size:
                self.width + self._canvas_width - dp(3), \
                self.height + self._padding_right - dp(3)
            radius: [self.height / 2]


<MDFloatingRootButton>
    theme_text_color: "Custom"
    md_bg_color: self.theme_cls.primary_color


<MDFloatingLabel>
    padding_x: "8dp"
    padding_y: "8dp"
    adaptive_size: True
    theme_text_color: "Custom"

    canvas.before:
        Color:
            rgba: self.bg_color
        RoundedRectangle:
            size: self.size
            pos: self.pos
            radius: self.radius
