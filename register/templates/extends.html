{% extends "home.html" %}
{% block home %}
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 p-4">
        {% for product in products %}
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-full h-48 object-cover">
            <div class="p-4">
                <h5 class="text-lg font-semibold mb-2">{{ product.name }}</h5>
                <p class="text-gray-600 mb-2">{{ product.description|truncatewords:20 }}</p>
                <p class="text-indigo-600 font-bold mb-4">${{ product.price }}</p>
                <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition">
                    View Details
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
{% endblock %}
