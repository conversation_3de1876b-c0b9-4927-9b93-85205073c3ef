from django.contrib import messages
from django.contrib.auth.models import User
from django.shortcuts import render, redirect


def register(request):
    if request.method == 'POST':
        name = request.POST['name']
        email = request.POST['email']
        password = request.POST['password']
        cpassword = request.POST['cpassword']
        if password == cpassword:
            if User.objects.filter(email=email).exists():
                messages.info(request, "Email already used.")
                return redirect('register')
            elif User.objects.filter(name=name).exists():
                messages.info(request, "Name already used.")
                return redirect('register')
            else:
                user = User.objects.create_user(name=name, password=password, cpassword=cpassword, email=email)
                user.save()
                return redirect('redirect')
        else:
            messages.info(request, "Password Not the Same")
            return redirect('register')
    else:
        return render(request, 'index.html')