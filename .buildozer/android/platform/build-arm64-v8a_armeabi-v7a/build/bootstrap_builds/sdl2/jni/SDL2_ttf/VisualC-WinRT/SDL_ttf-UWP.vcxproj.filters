<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Sources">
      <UniqueIdentifier>{e074d5df-046d-48ae-b45a-f472f0bdd965}</UniqueIdentifier>
    </Filter>
    <Filter Include="Public Headers">
      <UniqueIdentifier>{9c43d0b0-edae-4dea-bb19-5bd7972e58bc}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\FreeType">
      <UniqueIdentifier>{91971ab8-76cd-4d99-812d-9d86b0e5ab10}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\HarfBuzz">
      <UniqueIdentifier>{5dd68c94-3ded-4c03-9c2a-294fcec22a3e}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\SDL_ttf.c">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\autofit\autofit.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftbase.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftbbox.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftbdf.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftbitmap.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftcid.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftdebug.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftfstype.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftgasp.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftgxval.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftinit.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftmm.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftotval.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftpatent.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftpfr.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftstroke.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftsynth.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftsystem.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\fttype1.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftwinfnt.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\bdf\bdf.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\cff\cff.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\cache\ftcache.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftglyph.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\gzip\ftgzip.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\lzw\ftlzw.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\pcf\pcf.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\pfr\pfr.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\psaux\psaux.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\pshinter\pshinter.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\psnames\psmodule.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\raster\raster.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\sdf\sdf.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\sfnt\sfnt.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\smooth\smooth.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\svg\svg.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\truetype\truetype.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\type1\type1.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\cid\type1cid.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\type42\type42.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\winfonts\winfnt.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-aat-layout.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-aat-map.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-blob.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-buffer.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-buffer-serialize.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-common.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-face.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-fallback-shape.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-font.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ft.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ms-feature-ranges.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-number.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-cff1-table.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-cff2-table.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-face.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-font.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-layout.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-map.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-math.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-metrics.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-arabic.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-default.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-hangul.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-indic.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-indic-table.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-khmer.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-myanmar.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-syllabic.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-thai.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-use.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-vowel-constraints.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-fallback.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-tag.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-var.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-set.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-shape.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-shape-plan.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-shaper.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-static.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ucd.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-unicode.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-complex-hebrew.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\hb-ot-shape-normalize.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\SDL_ttf.h">
      <Filter>Public Headers</Filter>
    </ClInclude>
  </ItemGroup>
</Project>