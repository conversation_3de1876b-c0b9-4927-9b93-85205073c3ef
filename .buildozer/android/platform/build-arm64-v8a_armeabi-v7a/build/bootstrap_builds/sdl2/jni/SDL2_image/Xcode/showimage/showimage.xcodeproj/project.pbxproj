// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		F35474502828CAF5007E9EDA /* jxl.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F354744B2828CADB007E9EDA /* jxl.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F35474552828CDDB007E9EDA /* jxl.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F354744B2828CADB007E9EDA /* jxl.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F35474562828CDE0007E9EDA /* jxl.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F354744B2828CADB007E9EDA /* jxl.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F35475F92829BAC7007E9EDA /* avif.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F35475ED2829BAB1007E9EDA /* avif.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F35475FA2829BACC007E9EDA /* avif.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F35475ED2829BAB1007E9EDA /* avif.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F35475FB2829BAD0007E9EDA /* avif.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F35475ED2829BAB1007E9EDA /* avif.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F39CD44A281DC6A4006CF638 /* SDL2.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED810E281DC09600C33C5B /* SDL2.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F39CD44B281DC6C8006CF638 /* SDL2_image.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80D4281D9ED600C33C5B /* SDL2_image.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F39CD452281DC9CE006CF638 /* SDL2_image.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80D4281D9ED600C33C5B /* SDL2_image.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3E949DB281EAC3500B8F4EA /* webp.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F3E949DA281EAC1B00B8F4EA /* webp.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3E949DC281EAC9600B8F4EA /* webp.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F3E949DA281EAC1B00B8F4EA /* webp.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3E949DD281EAC9C00B8F4EA /* webp.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F3E949DA281EAC1B00B8F4EA /* webp.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3ED80CB281D9ECB00C33C5B /* showimage.c in Sources */ = {isa = PBXBuildFile; fileRef = F3ED80CA281D9ECB00C33C5B /* showimage.c */; };
		F3ED80CC281D9ECB00C33C5B /* showimage.c in Sources */ = {isa = PBXBuildFile; fileRef = F3ED80CA281D9ECB00C33C5B /* showimage.c */; };
		F3ED80DB281D9F2100C33C5B /* showimage.c in Sources */ = {isa = PBXBuildFile; fileRef = F3ED80CA281D9ECB00C33C5B /* showimage.c */; };
		F3ED80E3281DA16500C33C5B /* SDL2_image.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80D4281D9ED600C33C5B /* SDL2_image.framework */; };
		F3ED80E5281DA17800C33C5B /* SDL2.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80E4281DA17800C33C5B /* SDL2.framework */; };
		F3ED80F3281DA3BD00C33C5B /* SDL_uikit_main.c in Sources */ = {isa = PBXBuildFile; fileRef = F3ED80EF281DA3BD00C33C5B /* SDL_uikit_main.c */; };
		F3ED80F4281DA3BD00C33C5B /* SDL_uikit_main.c in Sources */ = {isa = PBXBuildFile; fileRef = F3ED80EF281DA3BD00C33C5B /* SDL_uikit_main.c */; };
		F3ED80F5281DA3F600C33C5B /* SDL2_image.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80D4281D9ED600C33C5B /* SDL2_image.framework */; };
		F3ED80F7281DA40300C33C5B /* SDL2.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80F6281DA40300C33C5B /* SDL2.framework */; };
		F3ED80FF281DA63000C33C5B /* SDL2_image.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80D4281D9ED600C33C5B /* SDL2_image.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3ED810C281DBEF000C33C5B /* SDL2.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80F6281DA40300C33C5B /* SDL2.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3ED8110281DC09600C33C5B /* SDL2.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED810E281DC09600C33C5B /* SDL2.framework */; };
		F3ED8112281DC11700C33C5B /* SDL2.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80E4281DA17800C33C5B /* SDL2.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		F3ED8113281DC13D00C33C5B /* SDL2_image.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F3ED80D4281D9ED600C33C5B /* SDL2_image.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		F354744A2828CADB007E9EDA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F35474462828CADB007E9EDA /* jxl.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F307EEDC28288CCF003915D7;
			remoteInfo = jxl;
		};
		F35475EC2829BAB1007E9EDA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F35475E52829BAB1007E9EDA /* avif.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F35474682829A8B3007E9EDA;
			remoteInfo = avif;
		};
		F35475EE2829BAB1007E9EDA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F35475E52829BAB1007E9EDA /* avif.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F354757D2829B82B007E9EDA;
			remoteInfo = dav1d;
		};
		F35475F02829BAB1007E9EDA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F35475E52829BAB1007E9EDA /* avif.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F354758A2829B839007E9EDA;
			remoteInfo = "dav1d-8bit";
		};
		F35475F22829BAB1007E9EDA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F35475E52829BAB1007E9EDA /* avif.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F35475972829B847007E9EDA;
			remoteInfo = "dav1d-16bit";
		};
		F3E949D9281EAC1B00B8F4EA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F3E949D5281EAC1B00B8F4EA /* webp.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F3D87D21281EA9C3005DA540;
			remoteInfo = webp;
		};
		F3ED80D3281D9ED600C33C5B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F3ED80CD281D9ED600C33C5B /* SDL_image.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BE1FA72E07AF4C45004B6283;
			remoteInfo = Framework;
		};
		F3ED80D5281D9ED600C33C5B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F3ED80CD281D9ED600C33C5B /* SDL_image.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BE1FA74107AF4C45004B6283;
			remoteInfo = "Static Library";
		};
		F3ED80D7281D9ED600C33C5B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F3ED80CD281D9ED600C33C5B /* SDL_image.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BE1FA74507AF4C45004B6283;
			remoteInfo = "Create DMG";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		F39CD449281DC695006CF638 /* Copy Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F39CD44A281DC6A4006CF638 /* SDL2.framework in Copy Frameworks */,
				F39CD44B281DC6C8006CF638 /* SDL2_image.framework in Copy Frameworks */,
				F35475F92829BAC7007E9EDA /* avif.framework in Copy Frameworks */,
				F35474562828CDE0007E9EDA /* jxl.framework in Copy Frameworks */,
				F3E949DB281EAC3500B8F4EA /* webp.framework in Copy Frameworks */,
			);
			name = "Copy Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3ED80EC281DA29B00C33C5B /* Copy Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3ED8112281DC11700C33C5B /* SDL2.framework in Copy Frameworks */,
				F39CD452281DC9CE006CF638 /* SDL2_image.framework in Copy Frameworks */,
				F35475FA2829BACC007E9EDA /* avif.framework in Copy Frameworks */,
				F35474552828CDDB007E9EDA /* jxl.framework in Copy Frameworks */,
				F3E949DC281EAC9600B8F4EA /* webp.framework in Copy Frameworks */,
			);
			name = "Copy Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3ED80FA281DA44400C33C5B /* Copy Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				F3ED810C281DBEF000C33C5B /* SDL2.framework in Copy Frameworks */,
				F3ED80FF281DA63000C33C5B /* SDL2_image.framework in Copy Frameworks */,
				F35475FB2829BAD0007E9EDA /* avif.framework in Copy Frameworks */,
				F35474502828CAF5007E9EDA /* jxl.framework in Copy Frameworks */,
				F3E949DD281EAC9C00B8F4EA /* webp.framework in Copy Frameworks */,
			);
			name = "Copy Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		F35474462828CADB007E9EDA /* jxl.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = jxl.xcodeproj; path = ../jxl/jxl.xcodeproj; sourceTree = "<group>"; };
		F35475E52829BAB1007E9EDA /* avif.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = avif.xcodeproj; path = ../avif/avif.xcodeproj; sourceTree = "<group>"; };
		F3E949D5281EAC1B00B8F4EA /* webp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = webp.xcodeproj; path = ../webp/webp.xcodeproj; sourceTree = "<group>"; };
		F3ED80B3281D9E8900C33C5B /* showimage.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = showimage.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F3ED80B9281D9E8900C33C5B /* showimage.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = showimage.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F3ED80BB281D9E8900C33C5B /* macOS.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = macOS.entitlements; sourceTree = "<group>"; };
		F3ED80CA281D9ECB00C33C5B /* showimage.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = showimage.c; path = ../../examples/showimage.c; sourceTree = SOURCE_ROOT; };
		F3ED80CD281D9ED600C33C5B /* SDL_image.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = SDL_image.xcodeproj; path = ../SDL_image.xcodeproj; sourceTree = "<group>"; };
		F3ED80E1281D9F2100C33C5B /* showimage.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = showimage.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F3ED80E4281DA17800C33C5B /* SDL2.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SDL2.framework; path = ../macOS/SDL2.framework; sourceTree = "<group>"; };
		F3ED80EF281DA3BD00C33C5B /* SDL_uikit_main.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SDL_uikit_main.c; sourceTree = SOURCE_ROOT; };
		F3ED80F6281DA40300C33C5B /* SDL2.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SDL2.framework; path = ../iOS/SDL2.framework; sourceTree = "<group>"; };
		F3ED810E281DC09600C33C5B /* SDL2.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SDL2.framework; path = ../tvOS/SDL2.framework; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		F3ED80B0281D9E8900C33C5B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3ED80F7281DA40300C33C5B /* SDL2.framework in Frameworks */,
				F3ED80F5281DA3F600C33C5B /* SDL2_image.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3ED80B6281D9E8900C33C5B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3ED80E5281DA17800C33C5B /* SDL2.framework in Frameworks */,
				F3ED80E3281DA16500C33C5B /* SDL2_image.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3ED80DC281D9F2100C33C5B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3ED8110281DC09600C33C5B /* SDL2.framework in Frameworks */,
				F3ED8113281DC13D00C33C5B /* SDL2_image.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		F35474472828CADB007E9EDA /* Products */ = {
			isa = PBXGroup;
			children = (
				F354744B2828CADB007E9EDA /* jxl.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F35475E62829BAB1007E9EDA /* Products */ = {
			isa = PBXGroup;
			children = (
				F35475ED2829BAB1007E9EDA /* avif.framework */,
				F35475EF2829BAB1007E9EDA /* libdav1d.a */,
				F35475F12829BAB1007E9EDA /* libdav1d-8bit.a */,
				F35475F32829BAB1007E9EDA /* libdav1d-16bit.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F3E949D6281EAC1B00B8F4EA /* Products */ = {
			isa = PBXGroup;
			children = (
				F3E949DA281EAC1B00B8F4EA /* webp.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F3ED80A6281D9E8800C33C5B = {
			isa = PBXGroup;
			children = (
				F3ED80CD281D9ED600C33C5B /* SDL_image.xcodeproj */,
				F35475E52829BAB1007E9EDA /* avif.xcodeproj */,
				F35474462828CADB007E9EDA /* jxl.xcodeproj */,
				F3E949D5281EAC1B00B8F4EA /* webp.xcodeproj */,
				F3ED80AB281D9E8800C33C5B /* Shared */,
				F3ED80BA281D9E8900C33C5B /* macOS */,
				F3ED80B4281D9E8900C33C5B /* Products */,
				F3ED80E2281DA16500C33C5B /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		F3ED80AB281D9E8800C33C5B /* Shared */ = {
			isa = PBXGroup;
			children = (
				F3ED80CA281D9ECB00C33C5B /* showimage.c */,
				F3ED80EF281DA3BD00C33C5B /* SDL_uikit_main.c */,
			);
			name = Shared;
			sourceTree = SOURCE_ROOT;
		};
		F3ED80B4281D9E8900C33C5B /* Products */ = {
			isa = PBXGroup;
			children = (
				F3ED80B3281D9E8900C33C5B /* showimage.app */,
				F3ED80B9281D9E8900C33C5B /* showimage.app */,
				F3ED80E1281D9F2100C33C5B /* showimage.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F3ED80BA281D9E8900C33C5B /* macOS */ = {
			isa = PBXGroup;
			children = (
				F3ED80BB281D9E8900C33C5B /* macOS.entitlements */,
			);
			path = macOS;
			sourceTree = "<group>";
		};
		F3ED80CE281D9ED600C33C5B /* Products */ = {
			isa = PBXGroup;
			children = (
				F3ED80D4281D9ED600C33C5B /* SDL2_image.framework */,
				F3ED80D6281D9ED600C33C5B /* libSDL2_image.a */,
				F3ED80D8281D9ED600C33C5B /* Create DMG */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F3ED80E2281DA16500C33C5B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F3ED8104281DADB900C33C5B /* macOS */,
				F3ED8105281DADC900C33C5B /* iOS */,
				F3ED810D281DC07200C33C5B /* tvOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F3ED8104281DADB900C33C5B /* macOS */ = {
			isa = PBXGroup;
			children = (
				F3ED80E4281DA17800C33C5B /* SDL2.framework */,
			);
			name = macOS;
			sourceTree = "<group>";
		};
		F3ED8105281DADC900C33C5B /* iOS */ = {
			isa = PBXGroup;
			children = (
				F3ED80F6281DA40300C33C5B /* SDL2.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		F3ED810D281DC07200C33C5B /* tvOS */ = {
			isa = PBXGroup;
			children = (
				F3ED810E281DC09600C33C5B /* SDL2.framework */,
			);
			name = tvOS;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F3ED80B2281D9E8900C33C5B /* showimage (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3ED80C4281D9E8900C33C5B /* Build configuration list for PBXNativeTarget "showimage (iOS)" */;
			buildPhases = (
				F3ED80AF281D9E8900C33C5B /* Sources */,
				F3ED80B0281D9E8900C33C5B /* Frameworks */,
				F3ED80B1281D9E8900C33C5B /* Resources */,
				F3ED80FA281DA44400C33C5B /* Copy Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "showimage (iOS)";
			productName = "showimage (iOS)";
			productReference = F3ED80B3281D9E8900C33C5B /* showimage.app */;
			productType = "com.apple.product-type.application";
		};
		F3ED80B8281D9E8900C33C5B /* showimage (macOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3ED80C7281D9E8900C33C5B /* Build configuration list for PBXNativeTarget "showimage (macOS)" */;
			buildPhases = (
				F3ED80B5281D9E8900C33C5B /* Sources */,
				F3ED80B6281D9E8900C33C5B /* Frameworks */,
				F3ED80B7281D9E8900C33C5B /* Resources */,
				F3ED80EC281DA29B00C33C5B /* Copy Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "showimage (macOS)";
			productName = "showimage (macOS)";
			productReference = F3ED80B9281D9E8900C33C5B /* showimage.app */;
			productType = "com.apple.product-type.application";
		};
		F3ED80D9281D9F2100C33C5B /* showimage (tvOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F3ED80DE281D9F2100C33C5B /* Build configuration list for PBXNativeTarget "showimage (tvOS)" */;
			buildPhases = (
				F3ED80DA281D9F2100C33C5B /* Sources */,
				F3ED80DC281D9F2100C33C5B /* Frameworks */,
				F3ED80DD281D9F2100C33C5B /* Resources */,
				F39CD449281DC695006CF638 /* Copy Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "showimage (tvOS)";
			productName = "showimage (iOS)";
			productReference = F3ED80E1281D9F2100C33C5B /* showimage.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F3ED80A7281D9E8800C33C5B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1330;
				LastUpgradeCheck = 1330;
				TargetAttributes = {
					F3ED80B2281D9E8900C33C5B = {
						CreatedOnToolsVersion = 13.3.1;
					};
					F3ED80B8281D9E8900C33C5B = {
						CreatedOnToolsVersion = 13.3.1;
					};
				};
			};
			buildConfigurationList = F3ED80AA281D9E8800C33C5B /* Build configuration list for PBXProject "showimage" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = F3ED80A6281D9E8800C33C5B;
			productRefGroup = F3ED80B4281D9E8900C33C5B /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = F35475E62829BAB1007E9EDA /* Products */;
					ProjectRef = F35475E52829BAB1007E9EDA /* avif.xcodeproj */;
				},
				{
					ProductGroup = F35474472828CADB007E9EDA /* Products */;
					ProjectRef = F35474462828CADB007E9EDA /* jxl.xcodeproj */;
				},
				{
					ProductGroup = F3ED80CE281D9ED600C33C5B /* Products */;
					ProjectRef = F3ED80CD281D9ED600C33C5B /* SDL_image.xcodeproj */;
				},
				{
					ProductGroup = F3E949D6281EAC1B00B8F4EA /* Products */;
					ProjectRef = F3E949D5281EAC1B00B8F4EA /* webp.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				F3ED80B2281D9E8900C33C5B /* showimage (iOS) */,
				F3ED80B8281D9E8900C33C5B /* showimage (macOS) */,
				F3ED80D9281D9F2100C33C5B /* showimage (tvOS) */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		F354744B2828CADB007E9EDA /* jxl.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = jxl.framework;
			remoteRef = F354744A2828CADB007E9EDA /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F35475ED2829BAB1007E9EDA /* avif.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = avif.framework;
			remoteRef = F35475EC2829BAB1007E9EDA /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F35475EF2829BAB1007E9EDA /* libdav1d.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libdav1d.a;
			remoteRef = F35475EE2829BAB1007E9EDA /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F35475F12829BAB1007E9EDA /* libdav1d-8bit.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libdav1d-8bit.a";
			remoteRef = F35475F02829BAB1007E9EDA /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F35475F32829BAB1007E9EDA /* libdav1d-16bit.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libdav1d-16bit.a";
			remoteRef = F35475F22829BAB1007E9EDA /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F3E949DA281EAC1B00B8F4EA /* webp.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = webp.framework;
			remoteRef = F3E949D9281EAC1B00B8F4EA /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F3ED80D4281D9ED600C33C5B /* SDL2_image.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2_image.framework;
			remoteRef = F3ED80D3281D9ED600C33C5B /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F3ED80D6281D9ED600C33C5B /* libSDL2_image.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2_image.a;
			remoteRef = F3ED80D5281D9ED600C33C5B /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F3ED80D8281D9ED600C33C5B /* Create DMG */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.executable";
			path = "Create DMG";
			remoteRef = F3ED80D7281D9ED600C33C5B /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		F3ED80B1281D9E8900C33C5B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3ED80B7281D9E8900C33C5B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3ED80DD281D9F2100C33C5B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F3ED80AF281D9E8900C33C5B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3ED80CB281D9ECB00C33C5B /* showimage.c in Sources */,
				F3ED80F3281DA3BD00C33C5B /* SDL_uikit_main.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3ED80B5281D9E8900C33C5B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3ED80CC281D9ECB00C33C5B /* showimage.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F3ED80DA281D9F2100C33C5B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3ED80DB281D9F2100C33C5B /* showimage.c in Sources */,
				F3ED80F4281DA3BD00C33C5B /* SDL_uikit_main.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		F3ED80C2281D9E8900C33C5B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				FRAMEWORK_SEARCH_PATHS = "\"$(SRCROOT)/../$(PLATFORM)\"";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				HEADER_SEARCH_PATHS = (
					"\"$(SRCROOT)/../$(PLATFORM)/SDL2.framework/Headers\"",
					"\"$(SRCROOT)/../../include\"",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				ONLY_ACTIVE_ARCH = YES;
				PLATFORM = "";
				"PLATFORM[sdk=appletvos*]" = tvOS;
				"PLATFORM[sdk=iphoneos*]" = iOS;
				"PLATFORM[sdk=macosx*]" = macOS;
				TVOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Debug;
		};
		F3ED80C3281D9E8900C33C5B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				FRAMEWORK_SEARCH_PATHS = "\"$(SRCROOT)/../$(PLATFORM)\"";
				HEADER_SEARCH_PATHS = (
					"\"$(SRCROOT)/../$(PLATFORM)/SDL2.framework/Headers\"",
					"\"$(SRCROOT)/../../include\"",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				PLATFORM = "";
				"PLATFORM[sdk=appletvos*]" = tvOS;
				"PLATFORM[sdk=iphoneos*]" = iOS;
				"PLATFORM[sdk=macosx*]" = macOS;
				TVOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Release;
		};
		F3ED80C5281D9E8900C33C5B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = EH385AYQ6F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.libsdl.showimage;
				PRODUCT_NAME = showimage;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F3ED80C6281D9E8900C33C5B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = EH385AYQ6F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.libsdl.showimage;
				PRODUCT_NAME = showimage;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F3ED80C8281D9E8900C33C5B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = macOS/macOS.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.libsdl.showimage;
				PRODUCT_NAME = showimage;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		F3ED80C9281D9E8900C33C5B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = macOS/macOS.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.libsdl.showimage;
				PRODUCT_NAME = showimage;
				SDKROOT = macosx;
			};
			name = Release;
		};
		F3ED80DF281D9F2100C33C5B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = EH385AYQ6F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.libsdl.showimage;
				PRODUCT_NAME = showimage;
				SDKROOT = appletvos;
			};
			name = Debug;
		};
		F3ED80E0281D9F2100C33C5B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = EH385AYQ6F;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.libsdl.showimage;
				PRODUCT_NAME = showimage;
				SDKROOT = appletvos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F3ED80AA281D9E8800C33C5B /* Build configuration list for PBXProject "showimage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3ED80C2281D9E8900C33C5B /* Debug */,
				F3ED80C3281D9E8900C33C5B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3ED80C4281D9E8900C33C5B /* Build configuration list for PBXNativeTarget "showimage (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3ED80C5281D9E8900C33C5B /* Debug */,
				F3ED80C6281D9E8900C33C5B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3ED80C7281D9E8900C33C5B /* Build configuration list for PBXNativeTarget "showimage (macOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3ED80C8281D9E8900C33C5B /* Debug */,
				F3ED80C9281D9E8900C33C5B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F3ED80DE281D9F2100C33C5B /* Build configuration list for PBXNativeTarget "showimage (tvOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F3ED80DF281D9F2100C33C5B /* Debug */,
				F3ED80E0281D9F2100C33C5B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F3ED80A7281D9E8800C33C5B /* Project object */;
}
