Name: zlib
Short Name: zlib
URL: http://zlib.net/
Version: 1.2.12
License: see `zlib.h`

Description:
"A massively spiffy yet delicately unobtrusive compression library."

'zlib' is a free, general-purpose, legally unencumbered lossless
data-compression library.  'zlib' implements the "deflate" compression
algorithm described by RFC 1951, which combines the LZ77 (Lempel-Ziv)
algorithm with <PERSON><PERSON>man coding.  zlib also implements the zlib (RFC 1950) and
gzip (RFC 1952) wrapper formats.

Local Modifications:
The files in this directory have been prepared as follows.

 - Take the unmodified source code files from the zlib distribution that are
   included by `ftgzip.c`.
 - Run zlib's `zlib2ansi` script on all `.c` files.
 - Apply the diff file(s) in the `patches` folder.
