# Open Watcom makefile to build SDL2mix.dll for OS/2
# wmake -f Makefile.os2
#
# Remember to edit DEPS_INC and DEPS_LIB below to meet
# your own environment!.

LIBNAME = SDL2mix
MAJOR_VERSION = 2
MINOR_VERSION = 6
MICRO_VERSION = 3
VERSION = $(MAJOR_VERSION).$(MINOR_VERSION).$(MICRO_VERSION)

TITLENAME = $(LIBNAME) $(VERSION)

# change DEPS_INC in order to point to the dependency headers.
DEPS_INC=-IC:\SDL2DEV\h\SDL2 -IC:\SDL2DEV\h
# change DEPS_LIB in order to point to the dependency libraries.
DEPS_LIB=C:\SDL2DEV\lib

# wav music support
USE_WAV=yes
# ogg/vorbis music support
USE_OGG=yes
# use integer-only Tremor (libvorbisidec) instead of libvorbis?
USE_TREMOR=no
# use stb_vorbis instead of libvorbis?
USE_STBVORBIS=no
# flac music support (using libflac)
USE_FLAC=yes
# flac music support (using dr_flac)
USE_DRFLAC=no
# opus music support
USE_OPUS=yes
# mp3 music support (using mpg123)
USE_MPG123=yes
# mp3 music support (using dr_mp3)
USE_DRMP3=no
# midi music support (using timidity)
USE_TIMIDITY=yes
# midi music support (using fluidsynth)
USE_FLUIDSYNTH=no
# tracker music support (using libxmp)
USE_XMP=yes
# tracker music support (using libmodplug)
USE_MODPLUG=no

LIBFILE = $(LIBNAME).lib
DLLFILE = $(LIBNAME).dll
LNKFILE = $(LIBNAME).lnk
TIMILIB = timidity.lib

SRCS = utils.c effect_position.c effects_internal.c effect_stereoreverse.c mixer.c music.c
# codec sources:
SRCS+= load_aiff.c load_voc.c music_wav.c &
       music_ogg.c music_ogg_stb.c music_opus.c &
       music_flac.c music_drflac.c mp3utils.c &
       music_mpg123.c music_drmp3.c &
       music_xmp.c music_modplug.c &
       music_fluidsynth.c music_timidity.c
# timidity sources:
TIMISRCS = common.c instrum.c mix.c output.c playmidi.c readmidi.c resample.c tables.c timidity.c

OBJS = $(SRCS:.c=.obj)
TIMIOBJS = $(TIMISRCS:.c=.obj)

LIBS = SDL2.lib

CFLAGS_BASE = -bt=os2 -d0 -q -bm -5s -fp5 -fpi87 -sg -oeatxh -ei -j
# warnings:
CFLAGS_BASE+= -wx
# newer OpenWatcom versions enable W303 by default
CFLAGS_BASE+= -wcd=303
# include paths:
CFLAGS_BASE+= -Iinclude -Isrc -I"src/codecs" -I"src/codecs/timidity" $(DEPS_INC)
CFLAGS_BASE+= -I"$(%WATCOM)/h/os2" -I"$(%WATCOM)/h"
CFLAGS = $(CFLAGS_BASE)
# to build a dll:
CFLAGS+= -bd
# for DECLSPEC:
CFLAGS+= -DBUILD_SDL

!ifeq USE_TIMIDITY yes
CFLAGS+= -DMUSIC_MID_TIMIDITY
LIBS+= $(TIMILIB)
!endif
!ifeq USE_FLUIDSYNTH yes
CFLAGS+= -DMUSIC_MID_FLUIDSYNTH
LIBS+= fluidsyn.lib
!endif

!ifeq USE_WAV yes
CFLAGS+= -DMUSIC_WAV
!endif

!ifeq USE_TREMOR yes
VORBIS_LIBS=vorbisidec.lib
!else
VORBIS_LIBS=vorbisfile.lib vorbis.lib
!endif
!ifeq USE_OGG yes
CFLAGS+= -DMUSIC_OGG
!ifeq USE_STBVORBIS yes
CFLAGS+= -DOGG_USE_STB
!else
!ifeq USE_TREMOR yes
CFLAGS+= -DOGG_USE_TREMOR
!endif
LIBS+= $(VORBIS_LIBS)
NEED_LIBOGG=yes
!endif
!endif

!ifeq USE_FLAC yes
CFLAGS+= -DMUSIC_FLAC_LIBFLAC
LIBS+= FLAC.lib
!endif
!ifeq USE_DRFLAC yes
CFLAGS+= -DMUSIC_FLAC_DRFLAC
!endif

!ifeq USE_OPUS yes
CFLAGS+= -DMUSIC_OPUS
LIBS+= opusfile.lib opus.lib
NEED_LIBOGG=yes
!endif

!ifeq USE_MPG123 yes
CFLAGS+= -DMUSIC_MP3_MPG123
LIBS+= mpg123.lib
!endif
!ifeq USE_DRMP3 yes
CFLAGS+= -DMUSIC_MP3_DRMP3
!endif

!ifeq USE_XMP yes
CFLAGS+= -DMUSIC_MOD_XMP
LIBS+= libxmp.lib
!endif
!ifeq USE_MODPLUG yes
CFLAGS+= -DMUSIC_MOD_MODPLUG
LIBS+= modplug.lib
!endif

!ifeq NEED_LIBOGG yes
LIBS+= ogg.lib
!endif

# For the static assertions in mixer.c
CFLAGS+= -DSDL_BUILD_MAJOR_VERSION=$(MAJOR_VERSION)
CFLAGS+= -DSDL_BUILD_MINOR_VERSION=$(MINOR_VERSION)
CFLAGS+= -DSDL_BUILD_MICRO_VERSION=$(MICRO_VERSION)

all: $(DLLFILE) playwave.exe playmus.exe

$(LIBFILE): $(DLLFILE)
  @echo * Create library: $@...
  wlib -b -n -q -c -pa -s -t -zld -ii -io $@ $(DLLFILE)

$(DLLFILE): $(OBJS) $(TIMILIB) $(LNKFILE)
  @echo * Link: $@
  wlink @$(LNKFILE)

$(LNKFILE):
  @%create $@
  @%append $@ SYSTEM os2v2_dll INITINSTANCE TERMINSTANCE
  @%append $@ NAME $(LIBNAME)
  @for %i in ($(OBJS)) do @%append $@ FILE %i
  @%append $@ OPTION QUIET
  @%append $@ OPTION DESCRIPTION '@$#libsdl org:$(VERSION)$#@Simple DirectMedia Layer Mixer Library'
  @%append $@ LIBPATH $(DEPS_LIB)
  @for %i in ($(LIBS)) do @%append $@ LIB %i
  @%append $@ OPTION MAP=$*
  @%append $@ OPTION ELIMINATE
  @%append $@ OPTION MANYAUTODATA
  @%append $@ OPTION OSNAME='OS/2 and eComStation'
  @%append $@ OPTION SHOWDEAD

.c: ./src;./src/codecs;

.c.obj:
  wcc386 $(CFLAGS) -fo=$^@ $<

playmus.obj: playmus.c
  wcc386 $(CFLAGS_BASE) -fo=$^@ $<

playwave.obj: playwave.c
  wcc386 $(CFLAGS_BASE) -fo=$^@ $<

playmus.exe: $(LIBFILE) playmus.obj
  wlink SYS os2v2 OP q LIBPATH $(DEPS_LIB) LIBR {$(LIBFILE) SDL2.lib} F {playmus.obj}  N playmus.exe

playwave.exe: $(LIBFILE) playwave.obj
  wlink SYS os2v2 OP q LIBPATH $(DEPS_LIB) LIBR {$(LIBFILE) SDL2.lib} F {playwave.obj} N playwave.exe

.c: ./src/codecs/timidity;
timidity.lib: $(TIMIOBJS)
  wlib -b -n -q -c -pa -s -t -zld -ii -io $@ $(TIMIOBJS)

clean: .SYMBOLIC
  @echo * Clean: $(TITLENAME)
  @if exist *.obj rm *.obj
  @if exist *.err rm *.err
  @if exist $(TIMILIB) rm $(TIMILIB)
  @if exist $(LNKFILE) rm $(LNKFILE)

distclean: .SYMBOLIC clean
  @if exist $(DLLFILE) rm $(DLLFILE)
  @if exist $(LIBFILE) rm $(LIBFILE)
  @if exist *.map rm *.map
  @if exist *.exe rm *.exe
