<?xml version="1.0" encoding="UTF-8"?>
<project name="CustomRules">
    <target name="-pre-build">
        <copy todir="tmp-src">
            {% if args.launcher %}
            <fileset dir="src/main/java" includes="**" />
            {% else %}
            <fileset dir="src/main/java">
                <exclude name="org/kivy/android/ProjectAdapter.java" />
                <exclude name="org/kivy/android/ProjectChooser.java" />
            </fileset>
            {% endif %}
            {% for dir, includes in args.extra_source_dirs %}
            <fileset dir="{{ dir }}" includes="{{ includes }}" />
            {% endfor %}
        </copy>
    </target>
    <target name="-post-build">
        <delete dir="tmp-src" />
    </target>
</project>
