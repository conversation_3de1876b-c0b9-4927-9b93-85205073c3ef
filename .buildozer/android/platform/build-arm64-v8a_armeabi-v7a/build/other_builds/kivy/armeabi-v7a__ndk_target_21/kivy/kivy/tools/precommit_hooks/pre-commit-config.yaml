# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
default_language_version:
    python: python3
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
    -   id: no-commit-to-branch
    -   id: check-executables-have-shebangs
    -   id: check-ast
    -   id: check-merge-conflict
    -   id: check-toml
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
    -   id: check-case-conflict
    -   id: check-json
    -   id: check-symlinks
    -   id: pretty-format-json
        args:
        - --autofix
-   repo: https://github.com/pycqa/flake8
    rev: 3.8.4
    hooks:
    -   id: flake8
-   repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.1.7
    hooks:
    -   id: forbid-crlf
    -   id: remove-crlf
    -   id: forbid-tabs
    -   id: remove-tabs
