#:kivy 1.4

GridLayout:
    cols: 2
    But<PERSON>:
        text: "Button 1"
    Button:
        text: "Button 2"
        font_size: 24
    But<PERSON>:
        text: "Button 3"
        background_color: .7, .7, 1, 1
    Button:
        text: "Button 4"
        on_press: self.text = 'pressed'
        on_release: self.text = 'Button 4'
    ToggleButton:
        text: "A toggle button"
    ToggleButton:
        text: "a toggle button in a group"
        group: "money"
    ToggleButton:
        text: "A toggle in the down state"
        state: "down"
    ToggleButton:
        text: "another toggle button in a group"
        group: "money"
