<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   width="475.98499"
   height="237.2879"
   viewBox="0 0 125.93768 62.782421"
   version="1.1"
   id="svg5"
   xml:space="preserve"
   sodipodi:docname="boxshadow_border_radius_inset.svg"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg"><sodipodi:namedview
     id="namedview14"
     pagecolor="#505050"
     bordercolor="#eeeeee"
     borderopacity="1"
     inkscape:showpageshadow="0"
     inkscape:pageopacity="0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#505050"
     showgrid="false" /><defs
     id="defs2"><clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath16371-8"><rect
         style="opacity:1;fill:#ff0000;fill-opacity:1;stroke:#ff0000;stroke-width:0.770726;stroke-linejoin:bevel;stroke-opacity:1"
         id="rect16373-0"
         width="166.40285"
         height="67.036255"
         x="1067.9857"
         y="202.73888" /></clipPath><clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath8932"><rect
         style="fill:none;stroke:#ff5656;stroke-width:0.264583;stroke-dasharray:3.06517, 3.06517;paint-order:fill markers stroke"
         id="rect8934"
         width="53.367771"
         height="40.654716"
         x="1791.8401"
         y="287.54468"
         rx="0.80335051"
         ry="0.80335051" /></clipPath></defs><g
     id="layer1"
     transform="translate(-1752.4092,-193.47479)"><rect
       style="fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:0.329634;stroke-linejoin:bevel;stroke-opacity:1"
       id="rect21757"
       width="125.93768"
       height="62.782417"
       x="1752.4092"
       y="193.47479" /><text
       xml:space="preserve"
       style="font-weight:bold;font-size:4.93532px;font-family:sans-serif;-inkscape-font-specification:'sans-serif Bold';text-align:end;text-anchor:end;fill:#e4e4e4;fill-opacity:1;stroke:none;stroke-width:0.396588;stroke-linejoin:bevel;stroke-dasharray:none;stroke-opacity:1"
       x="1206.0496"
       y="264.76849"
       id="text8997-9-9-4-6-1"
       transform="matrix(0.99927722,0,0,1.0007233,668.57655,-13.713061)"
       clip-path="url(#clipPath16371-8)"><tspan
         style="font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-size:4.93532px;font-family:'Fira Code';-inkscape-font-specification:'Fira Code Bold';fill:#1a1a1a;stroke-width:0.396588"
         x="1206.0496"
         y="264.76849"
         id="tspan7679-6-2">border_radius: [0.0, 25.0, 50.0, 100.0]</tspan><tspan
         style="font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-size:4.93532px;font-family:'Fira Code';-inkscape-font-specification:'Fira Code Bold';fill:#1a1a1a;stroke-width:0.396588"
         x="1206.0496"
         y="271.12189"
         id="tspan12511-7-6-2-8-1" /></text><image
       width="98.954163"
       height="84.402077"
       preserveAspectRatio="none"
       xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAXYAAAE/CAYAAABSCejBAAAABHNCSVQICAgIfAhkiAAAE99JREFU eJzt3flSW0fawOHWAp6Ly3gJgjjxktzaxJPUTApPbI9zb2MW6fvjq6aaQ59NaOPV81SphMQSIOhH +z2tw2S1Wq0SAGFM9/0JALBZwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPs AMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLAD BCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4Q jLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Aw wg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMHM9/0JHJN///vf6fLyMp2c nKTZbJam02maTCb7/rSg02q1Ssvl8t5lsVik5XKZfvjhh31/elRMVqvVat+fxLG4uLhIs9ksnZ6e pvl8fhd3ODRlFmphv729vXc5Pz9PP/744x4/Y0rCvkNv3rxJ8/k8PXv27C7uws4hyllYrVZ3lxz0 Zthvbm7uXb58+bLnzx6jmB06OTm5C/uzZ8/uRjJwaLrC3rzkoF9fX6ebm5v06tWrdHNzk/773//u +as4XsK+Q9PpNE2n0zSbzdLJyUn629/+lubz//9fYNbOIRka9jLq8/k8XV9fp9lslm5ubtLLly/T p0+f9vyVHCdh37HJZJImk0maTqfp5OQknZ6e3gUfDkUt7M0xTDl+KaM+m83S9fV1mk6n6dWrV+nq 6ir99ddfe/6Kjouw79lsNkvPnj27W7nDIWiGvXbQtBn2vCEgbwrIu74mk0n67rvv0tevX/f8VR0P NdmzyWRyN5qZzWZGMhyMMu7NA6fNA6Zl1Gthn0wm6e9//7u5+44I+wHIcT89PRV3DkYz7G1jmHIE U0a9jHtm5b4bwn4gJpNJms/nVu4cjHK+3jx4WhvDlM/NKFfrzY8p7tsn7AciPwiMZTgUXSv2HPfa aj1vDijD3vwlwXYJ+4EpV+5OOcA+da3Yc9jLqJdhr63Uy4/z4sWL9Pnz5z19ZfEJ+4HK2yE9gYl9 aca4uWpvO1ha+zj5fcuXHUzdHmE/YNPp9O60A+UDZrVaWcmzVW3bHctdMW3jl+bH6TrPDNsh7Aeq 3CbmnDLsS1vY87NLm1saa+/bNqO/vb1Nz58/d26ZLRD2A5dXQvlgqpU6u1QLe22PettKve/kYZ6Y tx2+q09EGfeUnFuG7Wo7V0wZ9ra5ettMvrlN8uTkJN3e3qZ//etf6fXr1/v4MsMS9ieka38wbFpz V0w+X0z+V+Rjol7ugf/jjz+EfcOE/YnJe92FnW1r7mPPB+0nk0laLpet71MLe/OJTM1zy7BZwv7E lGeHFHe2La/Up9NpWi6Xdz9/eUdL8wlItR0weexSnt63uf/9jz/+SBcXF/v8UkMR9icqxz2/DNtQ jmH6DpQul8s0n8/vrdSbK/O2FbtdX5sl7E9Y2wMNNqUcx9R+3vJqfjab3cW9uRovg958UlN++ePH j2mxWOzrywxH2J+45oOt7Zl/4s8YzV0xKaV7q+p8/2w2ezCGqUW9FvvyGavm7Jsl7AH0rdxFnbH6 Tt5Vhr+5/TGHulyRl7dr9xvFbJawB2EswyaVEZ9MJncr8hzg8qBq36V8kl35fIzm27E5wh6QuPNY Oeb5Ot+Xr7suzXPHtIW+65mrPI6wB+NBwjbUIt8X92bMuy5slrAH5cHCJoxdrTdDPfTt/bxulrAH 5wHDOpo7qYb81aPm7qzaz56I74awB+aEYayjOXbpMyTWtZU82yPsR8IDiSH8PdIY7DEKzj99GcPP SwzCDhCMUcwRsRKjizFMHFbsAMEIO0AwRjFHzGjmuBm9xGXFDhCMsAMEI+wAwZixc8fMPTYz9eNh xQ4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEI O0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPsAMEIO0Awwg4QjLADBCPs AMEIO0Awwg4QjLADBCPsAMEI+x6tVqt7L5e3AdYl7HvWFvTy/vyy8ANDCPsedEW6+brm2z0m8H45 wHEQ9h1ri/a2oivmcHyEfYdqo5XapXyb5tuXH2tMsMUdjoew79CLFy8eRHy5XKblclmNe0r1UUzX 7ccQf4hB2Hesb7beFvgh77vNzw94OoR9h/pGMGNGNEN20wDHSdh3qBy9NC9tMR8zqhkbdL8EICZh 36Hvv/++M+BDV+0p1Q+ulkQbjpew71jbinzsSr5rDi/ocNyEfceGBHxI1LO+mTtwfIR9x5qr9NrK fWzga3P2rrfzCwBiE/Yde/XqVe/4ZWzgU3o4c28aE3erf3jahH3HhszVx87YHxtiIYdYhH3HFotF Wi6X6fb29t4l3zcm9lnbqt3sHY6TsO9BM9TNuNcC37U1MqX+/ezrBN4vBXiahH0PXr58eS/qXavz tsgPHdV0EW6ISdj3IIe5uUqvXdbd19725KWumIs8xCDse7BYLKrz9b6oj9kxk1L93DSZuENcwr4n Xav0dQK/6Z0yJbGHp0XY9yTvZ99E4Ic+eSnf7tvT3rUPHprWOZ7j52m7hH1Pzs7O7oJ9c3Nz73pM 4Lt2y9RGMtm6cfeAjO0x/49rP3P55fI+P0PbN9/3J3DMxqzW26KeX55MJmkymbSu3CeTyZ6/Wg5V 2y/3Me/bdl9X3D3XYnus2PfoxYsX6ebmpvMydrfM0AOp+b7adebBRpu21Xnz9eK9H8K+R+XumHIc UwZ9ncCvuy2yvM6MZJ6+TY1Xyvu63nbdC5sj7HtWhrtcqV9fX6+9an/sg6c2j3dANY51Qtr2MzQ2 3l0/s2yOsO/Zq1evHkS9FvpNrNpTetzpfMX9ePSt1Nt++a8TemHfPGHfs8ViUZ2tX19f31u1Dwl8 GfnHjmTaGM08HY9ZmffdXmd13vbzmO9nc4T9ALx8+bL3IGpf3NseRF3PTE1p2IGvpr77PUifhnXG c23xbrvdF/p8efny5Za/2uMi7Afg7OysM+K1eXst8Os+M7UvyGPv73sd+9P1/7LtF/46K/Z1zl7K 5gj7gchbH/MIpryUcS/vG/MM1X3F3QN297r+RdX29s33bfv5aHubrtV47UylzZ9TNkvYD8TZ2Vl1 pl6LfNczVfseXF1xT6n/xGFjRjNDPiabMWZ0tokRzNC4d/1MNo8JsTnCfkBevHhRXbHXVut9pyFo m7UPXb3XrrN1Vu+PeVu6PWYkVluRt71u6Cq97cylba9bLpfp/Pz8kd8FSk4pcEDOzs7S58+f0/X1 dZrP52k+n6fZbPbg5Rz56XT64JJPLdC8ZJPJ5O6fvtNp/fd6PjVB7Tpr3u67v0bct2tM1Mv7+sYu Y+PetvAoFyBslhX7gXn+/HnrSv36+jpdXV2lq6ur6nhm6Ny9fECO+af22NGMsct+rDMyGzKSq8W8 ebv589YV9XxZLBZb+14cK2E/MHlfexny8rrr0hf42jimazvkY0czQ1/P5gwdk7WNYIas0Gsr9SEn sGuOD83Xt0fYD9CHDx+qMS9vd63am4EfckB1E3EfsiK0kt+8Id/72u2xQR8zcqltz207JvT69evt fXOOlBn7gbq+vr6bqc9ms3uXPE9v3tc2X++aeU+n09ZYj5X/O/nj9M3axX27+oI+dI7eF/m+uHed KuP29na335QjYcV+oH777bd7M/XabH3IzL25chqy33js6j0bO6JhezYR9a5Z+tigt40J7YbZDiv2 A/b8+fP0119/PVip59V5eV17Oa+Ymy+n9PCBX75+Op2m5XLZutpv7pZpftzm7pna+7N5faOYoVGv /WIfMksfe1qMm5sbO2K2RNgP2GKxSH/++eeD0Ut5uwx8V4hLq9UqzWaz6tuUo5lNa/ulwmaNWa2X b9M3U+/a7bLOuY4Wi4X5+pYI+4H77bff0vv376tRb67c15mxp/T/D+rmnvZ1Pk5+v+aqvfzvWK1v 19gRTPlyGfTydts2xa6gtz2prrz4Bb89wv4E/Prrr+mXX35pfRJSbcXeFeLmSm02mz1YsZUfsxn9 cgxT/q3VLmNW6+I/zLq7YIaMYGrHY4asztuef1EL/I8//rjdb9ARE/Yn4urq6kFsu2bpWe3BXz7A myOZ8m3yxy8/9tCV+zqMasbZVNibW16H7k9vO59RV8zzbQdNt0vYn4jff/89vX37tnf0krWtpGu7 XJqvK0czbWF/bODbRjU8Ttv8vHldi3rz5SEHSMtwt4W93LXloOluCPsT8s9//jO9e/eudbae0v1V e37wNm/XtjF2jWTKj7vuHJ/d6DtQ2retcej4pW/kUtuam9/3/PzcGGbLhP2J+fDhQ3r//n1KKbWu 1Eu18UbXbDUfmG2+zZCDtOXnlP97gr8b687Va6OX5mp9yMil6/kWzfe1Wt8+YX+Cfv311/Tzzz9X V8q1PeRDHtz5AZ6j3ox73gbZF/fygGr+b9Z2yjRv8zhDRy9DVut9u176niDXFfqLiwur9R0Q9ifq H//4R/rll1+qc/WU1nuaePO+ZtzLQHcFvox2eVvct2fI6CXfbnsmaRn25tP/x6zWu1buor4bwv6E fffdd+nr16/37qs9kMv72x7U8/m8Gvzm+d6bkR4ymqldZ8K+GWNn6s3r5gimDHtbxL99+5a+ffvW GvRm2O2E2R1hf8IWi0X6z3/+0/r6ISvz2hNS8snHlsvlvSdDlZf8uqF76duuzeIfZ+zB0q7Vei3q tVNI11bpOfJl7Mv3ccB0t4T9ifv999+rD5iueWp5u/k08fzXmnLgZ7PZ3ctdcW9bwadUD3wm6JvR FvT88ti96m3z9Ovr6wer9NrKvXnbWRx3a7Iqj7rwZF1eXqavX7+m09PT9OzZs87rk5OTu+vykqNe /im+ttMFt510bOxoJhP49YwZwQw9WNoMe7n6rq3Q//e//z24zi9/+/YtLRaL9ObNm719j46RsAfz 888/V2PefLkW9/l8fi/wzbDnoNdW720jmSFhF/XHGTtX71qt9+1+6Qp7W9R/+umnfX57jpKwB/T+ /fsHIa/dzkE/PT29F/Vm3PN1GfW2uA/d6167Zj3NsOeIly/XnlXat7WxK+pl3MuQ59tXV1fp7OzM XH1PhD2od+/e3Yt47dI2kmkLe9dYpnaO+LFhF/hxhoxhulbqbVsbx6zWa5f8Nl++fNnb9+bYOXga 1IcPH9Lbt297ny7edvCsnLXf3t7eBb1cvW965d58mXblemzsCKYv6l2r9dqKvbbl8eLiYl/fGpIV e3g//fTTvdV5bRTTNm+vHUxtjmVqYX9s3Blunbn60Kg3D5yWQe+6Pj8/T2/fvt3zd+a4CfuReP/+ /b2A1+LedSC1byzT96f7+sKeCfw4fWF/zAimLey1lXu+T9QPg7AfkTdv3vTO1tvinqPedUC1ayvk 0CcwZQJf13y4Dn0i0tC/gDTmdAHNi6gfDmE/MpeXl+nLly/3wp4D3nUQdZ1V+5CxTEpm7Otom7H3 nS5guVzerdT7VutDzwWTZ+qifjiE/Ui9ffu2Neq1MUzbk5faVu7rzNtTEvahmmHvG8M0/25p81ww tb9T2vbkpPL+xWJhS+MBEvYj9+7duweBf0zY22btfSOZ8prhuk4dMPS0AW1Rr8W9ef4XTz46TMJO +vjxY/r8+fO9mA8Ne9uzU8eu2lMS9nX07Ybp2tI6NOy10F9cXDhNwAETdu7kwHeFvVylNwPf9czU tl0yKVmxP0a5Yh9y5sauP6AxJPDn5+fp9vbWPP3ACTsPlIEfGvWxB1HzdUrCvq6hzzStzda7/jpS 7W+a5r9VapX+NAg7rS4vL+8FvjlX7xrFjNnXnpK4j1U7jUAz6qvVqnebY9uWx/Lan7N7eoSdXpeX l+nTp0+9Ya+NYsqDp8K+OX1hHzpj7xrLnJ+fp+VyaZX+BAk7o+TIt+2GGbJir41ihH2cdcPeNpIp Ay/oT5+ws7ZyJd/2DNR1zx1Dt3XC3rZyv729TWdnZ2m1Whm5BCHsbMTl5WWaTCbp06dPGzkpGN2G nCOmnLG3nVrg/Pxc0AMSdrYih/7PP/8cdOBU2Mdphj2/XDuAWgZ9sVik1WqVXr9+vc9Pny0Tdnbq 48eP90Kew5+SqI/VNY75/vvv7933ww8/7O3zZPeEHSCY6b4/AQA2S9gBghF2gGCEHSAYYQcIRtgB ghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcI RtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAY YQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCE HSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2 gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgB ghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcIRtgBghF2gGCEHSAYYQcI RtgBghF2gGD+D9Aw5FK3Zj0YAAAAAElFTkSuQmCC "
       id="image8508"
       x="1768.0612"
       y="269.95374"
       clip-path="url(#clipPath8932)"
       transform="translate(-3.1459522,-88.533238)" /></g></svg>
