#! /usr/bin/env perl
# Copyright 2017-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

use strict;
use OpenSSL::Test qw/:DEFAULT cmdstr srctop_file bldtop_dir/;
use OpenSSL::Test::Utils;
use TLSProxy::Proxy;

my $test_name = "test_tls13hrr";
setup($test_name);

plan skip_all => "TLSProxy isn't usable on $^O"
    if $^O =~ /^(VMS)$/;

plan skip_all => "$test_name needs the dynamic engine feature enabled"
    if disabled("engine") || disabled("dynamic-engine");

plan skip_all => "$test_name needs the sock feature enabled"
    if disabled("sock");

plan skip_all => "$test_name needs TLS1.3 enabled"
    if disabled("tls1_3");

$ENV{OPENSSL_ia32cap} = '~0x200000200000000';

my $proxy = TLSProxy::Proxy->new(
    undef,
    cmdstr(app(["openssl"]), display => 1),
    srctop_file("apps", "server.pem"),
    (!$ENV{HARNESS_ACTIVE} || $ENV{HARNESS_VERBOSE})
);

use constant {
    CHANGE_HRR_CIPHERSUITE => 0,
    CHANGE_CH1_CIPHERSUITE => 1,
    DUPLICATE_HRR => 2
};

#Test 1: A client should fail if the server changes the ciphersuite between the
#        HRR and the SH
$proxy->filter(\&hrr_filter);
$proxy->serverflags("-curves P-256");
my $testtype = CHANGE_HRR_CIPHERSUITE;
$proxy->start() or plan skip_all => "Unable to start up Proxy for tests";
plan tests => 3;
ok(TLSProxy::Message->fail(), "Server ciphersuite changes");

#Test 2: It is an error if the client changes the offered ciphersuites so that
#        we end up selecting a different ciphersuite between HRR and the SH
$proxy->clear();
$proxy->serverflags("-curves P-256");
$proxy->ciphersuitess("TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384");
$testtype = CHANGE_CH1_CIPHERSUITE;
$proxy->start();
ok(TLSProxy::Message->fail(), "Client ciphersuite changes");

#Test 3: A client should fail with unexpected_message alert if the server
#        sends more than 1 HRR
my $fatal_alert = 0;
$proxy->clear();
if (disabled("ec")) {
    $proxy->serverflags("-curves ffdhe3072");
} else {
    $proxy->serverflags("-curves P-256");
}
$testtype = DUPLICATE_HRR;
$proxy->start();
ok($fatal_alert, "Server duplicated HRR");

sub hrr_filter
{
    my $proxy = shift;

    if ($testtype == CHANGE_HRR_CIPHERSUITE) {
        # We're only interested in the HRR
        if ($proxy->flight != 1) {
            return;
        }

        my $hrr = ${$proxy->message_list}[1];

        # We will normally only ever select CIPHER_TLS13_AES_128_GCM_SHA256
        # because that's what Proxy tells s_server to do. Setting as below means
        # the ciphersuite will change will we get the ServerHello
        $hrr->ciphersuite(TLSProxy::Message::CIPHER_TLS13_AES_256_GCM_SHA384);
        $hrr->repack();
        return;
    }

    if ($testtype == DUPLICATE_HRR) {
        # We're only interested in the HRR
        # and the unexpected_message alert from client
        if ($proxy->flight == 4) {
            $fatal_alert = 1
                if @{$proxy->record_list}[-1]->is_fatal_alert(0) == 10;
            return;
        }
        if ($proxy->flight != 3) {
            return;
        }

        # Find ServerHello record (HRR actually) and insert after that
        my $i;
        for ($i = 0; ${$proxy->record_list}[$i]->flight() < 1; $i++) {
            next;
        }
        my $hrr_record = ${$proxy->record_list}[$i];
        my $dup_hrr = TLSProxy::Record->new(3,
            $hrr_record->content_type(),
            $hrr_record->version(),
            $hrr_record->len(),
            $hrr_record->sslv2(),
            $hrr_record->len_real(),
            $hrr_record->decrypt_len(),
            $hrr_record->data(),
            $hrr_record->decrypt_data());

        $i++;
        splice @{$proxy->record_list}, $i, 0, $dup_hrr;
        return;
    }

    # CHANGE_CH1_CIPHERSUITE
    if ($proxy->flight != 0) {
        return;
    }

    my $ch1 = ${$proxy->message_list}[0];

    # The server will always pick TLS_AES_256_GCM_SHA384
    my @ciphersuites = (TLSProxy::Message::CIPHER_TLS13_AES_128_GCM_SHA256);
    $ch1->ciphersuite_len(2 * scalar @ciphersuites);
    $ch1->ciphersuites(\@ciphersuites);
    $ch1->repack();
}
