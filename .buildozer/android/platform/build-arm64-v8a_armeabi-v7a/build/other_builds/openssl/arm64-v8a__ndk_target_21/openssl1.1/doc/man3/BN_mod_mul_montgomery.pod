=pod

=head1 NAME

BN_mod_mul_montgomery, BN_MONT_CTX_new,
BN_MONT_CTX_free, BN_MONT_CTX_set, BN_MONT_CTX_copy,
BN_from_montgomery, BN_to_montgomery - Montgomery multiplication

=head1 SYNOPSIS

 #include <openssl/bn.h>

 BN_MONT_CTX *BN_MONT_CTX_new(void);
 void BN_MONT_CTX_free(BN_MONT_CTX *mont);

 int BN_MONT_CTX_set(BN_MONT_CTX *mont, const BIGNUM *m, BN_CTX *ctx);
 BN_MONT_CTX *BN_MONT_CTX_copy(BN_MONT_CTX *to, BN_MONT_CTX *from);

 int BN_mod_mul_montgomery(BIGNUM *r, BIGNUM *a, BIGNUM *b,
                           BN_MONT_CTX *mont, BN_CTX *ctx);

 int BN_from_montgomery(BIGNUM *r, BIGNUM *a, BN_MONT_CTX *mont,
                        BN_CTX *ctx);

 int BN_to_montgomery(BIGNUM *r, BIGNUM *a, BN_MONT_CTX *mont,
                      BN_CTX *ctx);

=head1 DESCRIPTION

These functions implement Montgomery multiplication. They are used
automatically when L<BN_mod_exp(3)> is called with suitable input,
but they may be useful when several operations are to be performed
using the same modulus.

BN_MONT_CTX_new() allocates and initializes a B<BN_MONT_CTX> structure.

BN_MONT_CTX_set() sets up the I<mont> structure from the modulus I<m>
by precomputing its inverse and a value R.

BN_MONT_CTX_copy() copies the B<BN_MONT_CTX> I<from> to I<to>.

BN_MONT_CTX_free() frees the components of the B<BN_MONT_CTX>, and, if
it was created by BN_MONT_CTX_new(), also the structure itself.
If B<mont> is NULL, nothing is done.

BN_mod_mul_montgomery() computes Mont(I<a>,I<b>):=I<a>*I<b>*R^-1 and places
the result in I<r>.

BN_from_montgomery() performs the Montgomery reduction I<r> = I<a>*R^-1.

BN_to_montgomery() computes Mont(I<a>,R^2), i.e. I<a>*R.
Note that I<a> must be nonnegative and smaller than the modulus.

For all functions, I<ctx> is a previously allocated B<BN_CTX> used for
temporary variables.

=head1 RETURN VALUES

BN_MONT_CTX_new() returns the newly allocated B<BN_MONT_CTX>, and NULL
on error.

BN_MONT_CTX_free() has no return value.

For the other functions, 1 is returned for success, 0 on error.
The error codes can be obtained by L<ERR_get_error(3)>.

=head1 WARNINGS

The inputs must be reduced modulo B<m>, otherwise the result will be
outside the expected range.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<BN_add(3)>,
L<BN_CTX_new(3)>

=head1 HISTORY

BN_MONT_CTX_init() was removed in OpenSSL 1.1.0

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
