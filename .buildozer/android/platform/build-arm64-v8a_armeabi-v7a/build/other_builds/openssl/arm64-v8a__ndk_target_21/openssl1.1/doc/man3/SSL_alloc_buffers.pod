=pod

=head1 NAME

SSL_free_buffers, SSL_alloc_buffers - manage SSL structure buffers

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_free_buffers(SSL *ssl);
 int SSL_alloc_buffers(SSL *ssl);

=head1 DESCRIPTION

SSL_free_buffers() frees the read and write buffers of the given B<ssl>.
SSL_alloc_buffers() allocates the read and write buffers of the given B<ssl>.

The B<SSL_MODE_RELEASE_BUFFERS> mode releases read or write buffers whenever
the buffers have been drained. These functions allow applications to manually
control when buffers are freed and allocated.

After freeing the buffers, the buffers are automatically reallocated upon a
new read or write. The SSL_alloc_buffers() does not need to be called, but
can be used to make sure the buffers are preallocated. This can be used to
avoid allocation during data processing or with CRYPTO_set_mem_functions()
to control where and how buffers are allocated.

=head1 RETURN VALUES

The following return values can occur:

=over 4

=item 0 (Failure)

The SSL_free_buffers() function returns 0 when there is pending data to be
read or written. The SSL_alloc_buffers() function returns 0 when there is
an allocation failure.

=item 1 (Success)

The SSL_free_buffers() function returns 1 if the buffers have been freed. This
value is also returned if the buffers had been freed before calling
SSL_free_buffers().
The SSL_alloc_buffers() function returns 1 if the buffers have been allocated.
This value is also returned if the buffers had been allocated before calling
SSL_alloc_buffers().

=back

=head1 SEE ALSO

L<SSL_free(3)>, L<SSL_clear(3)>,
L<SSL_new(3)>, L<SSL_CTX_set_mode(3)>,
L<CRYPTO_set_mem_functions>

=head1 COPYRIGHT

Copyright 2017-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
