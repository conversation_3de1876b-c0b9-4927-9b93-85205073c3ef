=pod

=head1 NAME

BN_new, BN_secure_new, BN_clear, BN_free, BN_clear_free - allocate and free BIGNUMs

=head1 SYNOPSIS

 #include <openssl/bn.h>

 BIGNUM *BN_new(void);

 BIGNUM *BN_secure_new(void);

 void BN_clear(BIGNUM *a);

 void BN_free(BIGNUM *a);

 void BN_clear_free(BIGNUM *a);

=head1 DESCRIPTION

BN_new() allocates and initializes a B<BIGNUM> structure.
BN_secure_new() does the same except that the secure heap
L<OPENSSL_secure_malloc(3)> is used to store the value.

BN_clear() is used to destroy sensitive data such as keys when they
are no longer needed. It erases the memory used by B<a> and sets it
to the value 0.
If B<a> is NULL, nothing is done.

BN_free() frees the components of the B<BIGNUM>, and if it was created
by BN_new(), also the structure itself. BN_clear_free() additionally
overwrites the data before the memory is returned to the system.
If B<a> is NULL, nothing is done.

=head1 RETURN VALUES

BN_new() and BN_secure_new()
return a pointer to the B<BIGNUM> initialised to the value 0.
If the allocation fails,
they return B<NULL> and set an error code that can be obtained
by L<ERR_get_error(3)>.

BN_clear(), BN_free() and BN_clear_free() have no return values.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<OPENSSL_secure_malloc(3)>

=head1 HISTORY

BN_init() was removed in OpenSSL 1.1.0; use BN_new() instead.

=head1 COPYRIGHT

Copyright 2000-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
