=pod

=head1 NAME

DSA_size, DSA_bits, DSA_security_bits - get DSA signature size, key bits or security bits

=head1 SYNOPSIS

 #include <openssl/dsa.h>

 int DSA_size(const DSA *dsa);
 int DSA_bits(const DSA *dsa);
 int DSA_security_bits(const DSA *dsa);

=head1 DESCRIPTION

DSA_size() returns the maximum size of an ASN.1 encoded DSA signature
for key B<dsa> in bytes. It can be used to determine how much memory must
be allocated for a DSA signature.

B<dsa-E<gt>q> must not be B<NULL>.

DSA_bits() returns the number of bits in key B<dsa>: this is the number
of bits in the B<p> parameter.

DSA_security_bits() returns the number of security bits of the given B<dsa>
key. See L<BN_security_bits(3)>.

=head1 RETURN VALUES

DSA_size() returns the signature size in bytes.

DSA_bits() returns the number of bits in the key.

=head1 SEE ALSO

L<DSA_new(3)>, L<DSA_sign(3)>

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
