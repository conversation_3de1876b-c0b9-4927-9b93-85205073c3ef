=pod

=head1 NAME

DH_size, DH_bits, DH_security_bits - get <PERSON><PERSON><PERSON>-<PERSON> prime size and
security bits

=head1 SYNOPSIS

 #include <openssl/dh.h>

 int DH_size(const DH *dh);

 int DH_bits(const DH *dh);

 int DH_security_bits(const DH *dh);

=head1 DESCRIPTION

DH_size() returns the <PERSON><PERSON><PERSON>-<PERSON><PERSON> prime size in bytes. It can be used
to determine how much memory must be allocated for the shared secret
computed by L<DH_compute_key(3)>.

DH_bits() returns the number of significant bits.

B<dh> and B<dh-E<gt>p> must not be B<NULL>.

DH_security_bits() returns the number of security bits of the given B<dh>
key. See L<BN_security_bits(3)>.

=head1 RETURN VALUES

DH_size() returns the prime size of <PERSON>ff<PERSON>-<PERSON><PERSON> in bytes.

DH_bits() returns the number of bits in the key.

DH_security_bits() returns the number of security bits.

=head1 SEE ALSO

L<DH_new(3)>, L<DH_generate_key(3)>,
L<BN_num_bits(3)>

=head1 HISTORY

The DH_bits() function was added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
