=pod

=head1 NAME

DSA_new, DSA_free - allocate and free DSA objects

=head1 SYNOPSIS

 #include <openssl/dsa.h>

 DSA* DSA_new(void);

 void DSA_free(DSA *dsa);

=head1 DESCRIPTION

DSA_new() allocates and initializes a B<DSA> structure. It is equivalent to
calling DSA_new_method(NULL).

DSA_free() frees the B<DSA> structure and its components. The values are
erased before the memory is returned to the system.
If B<dsa> is NULL nothing is done.

=head1 RETURN VALUES

If the allocation fails, DSA_new() returns B<NULL> and sets an error
code that can be obtained by
L<ERR_get_error(3)>. Otherwise it returns a pointer
to the newly allocated structure.

DSA_free() returns no value.

=head1 SEE ALSO

L<DSA_new(3)>, L<ERR_get_error(3)>,
L<DSA_generate_parameters(3)>,
L<DSA_generate_key(3)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
