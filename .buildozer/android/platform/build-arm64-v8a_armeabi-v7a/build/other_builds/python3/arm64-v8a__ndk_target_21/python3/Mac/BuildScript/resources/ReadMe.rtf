{\rtf1\ansi\ansicpg1252\cocoartf2709
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;\f1\fswiss\fcharset0 Helvetica-Bold;\f2\fswiss\fcharset0 Helvetica-Oblique;
\f3\fmodern\fcharset0 CourierNewPSMT;\f4\fmodern\fcharset0 Courier;}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\margl1440\margr1440\vieww13380\viewh14580\viewkind0
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f0\fs24 \cf0 This package will install Python $FULL_VERSION for macOS $MACOSX_DEPLOYMENT_TARGET for the following architecture(s): $ARCHITECTURES.\
\
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f1\b \cf0 \ul \ulc0 Certificate verification and OpenSSL\

\f0\b0 \ulnone \
This package includes its own private copy of OpenSSL 3.0.   The trust certificates in system and user keychains managed by the 
\f2\i Keychain Access 
\f0\i0 application and the 
\f2\i security
\f0\i0  command line utility are not used as defaults by the Python 
\f3 ssl
\f0  module.  A sample command script is included in 
\f3 /Applications/Python 3.11
\f0  to install a curated bundle of default root certificates from the third-party 
\f3 certifi
\f0  package ({\field{\*\fldinst{HYPERLINK "https://pypi.org/project/certifi/"}}{\fldrslt https://pypi.org/project/certifi/}}).  Double-click on 
\f3 Install Certificates
\f0  to run it.\
\
The bundled 
\f3 pip
\f0  has its own default certificate store for verifying download connections.\
\

\f1\b \ul Install Options\

\f0\b0 \ulnone \
You can control some aspects of what is installed by this package. To see the options, click on the 
\f4 Customize
\f0  button in the 
\f4 Installation Type
\f0  step of the macOS installer app.  Click on a package name in the list shown to see more information about that option,\
  \

\f1\b \ul Using IDLE or other Tk applications 
\f0\b0 \ulnone \
\
This package includes its own private version of Tcl/Tk 8.6. It does not use any system-supplied or third-party supplied versions of Tcl/Tk.\
\
Due to new security checks on macOS 10.15 Catalina, when launching IDLE macOS may open a window with a message 
\f1\b "Python" would like to access files in your Documents folder
\f0\b0 .  This is normal as IDLE uses your 
\f1\b Documents
\f0\b0  folder as its default when opening and saving files; you can still choose other locations in the 
\f1\b Open
\f0\b0  and 
\f1\b Save
\f0\b0  file dialog windows.  Click on the 
\f1\b OK
\f0\b0  button to proceed.\
\

\f1\b \ul Apple Silicon Mac support\

\f0\b0 \ulnone \
On Apple Silicon Macs, it is possible to run Python either with native ARM64 code or under Intel 64 emulation using Rosetta2. This option might be useful for testing or if binary wheels are not yet available with native ARM64 binaries.  To  easily force Python to run in emulation mode, invoke it from a command line shell with the 
\f4 python3-intel64
\f0  command instead of just 
\f4 python3
\f0 .\

\f1\b \ul \
Other changes\

\f0\b0 \ulnone \
For other changes in this release, see the 
\f2\i What's new
\f0\i0  section in the {\field{\*\fldinst{HYPERLINK "https://www.python.org/doc/"}}{\fldrslt Documentation Set}} for this release and its 
\f2\i Release Notes
\f0\i0  link at {\field{\*\fldinst{HYPERLINK "https://www.python.org/downloads/"}}{\fldrslt https://www.python.org/downloads/}}.\
}