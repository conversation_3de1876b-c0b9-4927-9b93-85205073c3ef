<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{8A4A1162-4BF9-4FF6-9A98-315F01E44932}</ProjectGuid>
        <OutputName>python</OutputName>
        <OutputSuffix></OutputSuffix>
    </PropertyGroup>

    <Import Project="..\msi.props" />

    <PropertyGroup>
        <DefineConstants Condition="'$(Pack)' != 'true'">
            $(DefineConstants);CompressMSI=no;
        </DefineConstants>
        <DefineConstants Condition="'$(Pack)' == 'true'">
            $(DefineConstants);CompressMSI=yes;
        </DefineConstants>
        <DefineConstants>
            $(DefineConstants);
            CompressPDB=no;
            CompressMSI_D=no;
        </DefineConstants>
    </PropertyGroup>
    
    <Import Project="bundle.targets" />
</Project>