unittestgui.py is GUI framework and application for use with Python unit
testing framework. It executes tests written using the framework provided
by the 'unittest' module.

Based on the original by <PERSON>, from:

  http://pyunit.sourceforge.net/

Updated for unittest test discovery by <PERSON> and Python 3
support by <PERSON>.

For details on how to make your tests work with test discovery,
and for explanations of the configuration options, see the unittest
documentation:

    http://docs.python.org/library/unittest.html#test-discovery
