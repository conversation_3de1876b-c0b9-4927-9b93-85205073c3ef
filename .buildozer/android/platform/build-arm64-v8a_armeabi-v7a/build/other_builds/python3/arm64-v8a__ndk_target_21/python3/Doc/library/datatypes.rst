.. _datatypes:

**********
Data Types
**********

The modules described in this chapter provide a variety of specialized data
types such as dates and times, fixed-type arrays, heap queues, double-ended
queues, and enumerations.

Python also provides some built-in data types, in particular,
:class:`dict`, :class:`list`, :class:`set` and :class:`frozenset`, and
:class:`tuple`.  The :class:`str` class is used to hold
Unicode strings, and the :class:`bytes` and :class:`bytearray` classes are used
to hold binary data.

The following modules are documented in this chapter:


.. toctree::

   datetime.rst
   zoneinfo.rst
   calendar.rst
   collections.rst
   collections.abc.rst
   heapq.rst
   bisect.rst
   array.rst
   weakref.rst
   types.rst
   copy.rst
   pprint.rst
   reprlib.rst
   enum.rst
   graphlib.rst
