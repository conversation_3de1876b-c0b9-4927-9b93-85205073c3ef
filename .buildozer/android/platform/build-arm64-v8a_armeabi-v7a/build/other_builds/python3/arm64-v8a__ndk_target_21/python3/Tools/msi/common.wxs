<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Fragment>
        <Property Id="ROOTREGISTRYKEY" Value="Software\Python\PythonCore" />
    </Fragment>
    
    <Fragment>
        <Property Id="REGISTRYKEY" Value="Software\Python\PythonCore\$(var.ShortVersion)$(var.PyArchExt)$(var.PyTestExt)" />
    </Fragment>
    
    <Fragment>
        <Component Id="OptionalFeature" Guid="*" Directory="InstallDirectory">
            <Condition>OPTIONALFEATURESREGISTRYKEY</Condition>
            <RegistryKey Root="HKMU" Key="[OPTIONALFEATURESREGISTRYKEY]">
                <RegistryValue Type="string" Name="$(var.OptionalFeatureName)" Value="$(var.Version)" KeyPath="yes" />
            </RegistryKey>
        </Component>
    </Fragment>
    
    <Fragment>
        <Property Id="UpgradeTable" Value="1" />
        
        <?ifndef SuppressUpgradeTable ?>
        <Upgrade Id="$(var.UpgradeCode)">
            <UpgradeVersion Property="DOWNGRADE" Minimum="$(var.Version)" IncludeMinimum="no" OnlyDetect="yes" />
            <UpgradeVersion Property="UPGRADE" Minimum="$(var.UpgradeMinimumVersion)" IncludeMinimum="yes" Maximum="$(var.Version)" IncludeMaximum="no" />
        </Upgrade>
        
        <?ifdef CoreUpgradeCode ?>
        <?if $(var.UpgradeCode)!=$(var.CoreUpgradeCode) ?>
        <Upgrade Id="$(var.CoreUpgradeCode)">
            <UpgradeVersion Property="MISSING_CORE" Minimum="$(var.Version)" IncludeMinimum="yes" Maximum="$(var.Version)" IncludeMaximum="yes" OnlyDetect="yes" />
        </Upgrade>
        <Condition Message="!(loc.IncorrectCore)">Installed OR NOT MISSING_CORE</Condition>
        <?endif ?>
        <?endif ?>
        
        <Condition Message="!(loc.NoDowngrade)">Installed OR NOT DOWNGRADE</Condition>
        <Condition Message="!(loc.NoTargetDir)">Installed OR TARGETDIR OR Suppress_TARGETDIR_Check</Condition>

        <InstallExecuteSequence>
            <RemoveExistingProducts After="InstallInitialize" Overridable="yes">UPGRADE</RemoveExistingProducts>
        </InstallExecuteSequence>
        <?endif ?>
    </Fragment>
    
    <Fragment>
        <!-- Include an icon for the Programs and Features dialog -->
        <Icon Id="ARPIcon" SourceFile="!(bindpath.src)PC\icons\python.ico" />
        <Property Id="ARPPRODUCTICON" Value="ARPIcon" />
        <Property Id="ARPNOMODIFY" Value="1" />
        <Property Id="DISABLEADVTSHORTCUTS" Value="1" />
    </Fragment>
    
    <Fragment>
        <Directory Id="TARGETDIR" Name="SourceDir">
        <?ifdef InstallDirectoryGuidSeed ?>
            <Directory Id="InstallDirectory" ComponentGuidGenerationSeed="$(var.InstallDirectoryGuidSeed)" />
        <?endif ?>
        </Directory>
    </Fragment>

    <Fragment>
        <?ifdef PythonExeComponentGuid ?>
        <!-- Locate TARGETDIR automatically assuming we have executables installed -->
        <Property Id="TARGETDIR">
            <ComponentSearch Id="PythonExe_Directory" Guid="$(var.PythonExeComponentGuid)">
                <DirectorySearch Id="PythonExe_Directory" AssignToProperty="yes" Path=".">
                    <FileSearch Id="PythonExe_DirectoryFile" Name="python.exe" />
                </DirectorySearch>
            </ComponentSearch>
        </Property>
        <?endif ?>
        <Property Id="DetectTargetDir" Value="1" />
    </Fragment>
    
    <!-- Top-level directories -->
    <Fragment>
        <DirectoryRef Id="InstallDirectory">
            <Directory Id="DLLs" Name="DLLs">
                <Directory Id="Catalogs" />
            </Directory>
        </DirectoryRef>
    </Fragment>

    <Fragment>
        <DirectoryRef Id="InstallDirectory">
            <Directory Id="Doc" Name="Doc">
                <Directory Id="Doc_html" Name="html" />
            </Directory>
        </DirectoryRef>
    </Fragment>

    <Fragment>
        <DirectoryRef Id="InstallDirectory">
            <Directory Id="include" Name="include" />
        </DirectoryRef>
    </Fragment>

    <Fragment>
        <DirectoryRef Id="InstallDirectory">
            <Directory Id="Lib" Name="Lib" />
        </DirectoryRef>
    </Fragment>

    <Fragment>
        <DirectoryRef Id="InstallDirectory">
            <Directory Id="libs" Name="libs" />
        </DirectoryRef>
    </Fragment>

    <Fragment>
        <DirectoryRef Id="InstallDirectory">
            <Directory Id="Scripts" Name="Scripts" />
        </DirectoryRef>
    </Fragment>

    <Fragment>
        <DirectoryRef Id="InstallDirectory">
            <Directory Id="tcl" Name="tcl" />
        </DirectoryRef>
    </Fragment>

    <Fragment>
        <DirectoryRef Id="InstallDirectory">
            <Directory Id="Tools" Name="Tools" />
        </DirectoryRef>
    </Fragment>
    
    <!-- Start Menu folder -->
    <Fragment>
        <DirectoryRef Id="TARGETDIR">
            <Directory Id="ProgramMenuFolder">
                <Directory Id="MenuDir" Name="!(loc.ProductName)" />
            </Directory>
        </DirectoryRef>
    </Fragment>
</Wix>
