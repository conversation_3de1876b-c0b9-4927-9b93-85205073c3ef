.. _reference-index:

#################################
  The Python Language Reference
#################################

This reference manual describes the syntax and "core semantics" of the
language. It is terse, but attempts to be exact and complete. The semantics of
non-essential built-in object types and of the built-in functions and modules
are described in :ref:`library-index`. For an informal introduction to the
language, see :ref:`tutorial-index`. For C or C++ programmers, two additional
manuals exist: :ref:`extending-index` describes the high-level picture of how to
write a Python extension module, and the :ref:`c-api-index` describes the
interfaces available to C/C++ programmers in detail.

.. toctree::
   :maxdepth: 2
   :numbered:

   introduction.rst
   lexical_analysis.rst
   datamodel.rst
   executionmodel.rst
   import.rst
   expressions.rst
   simple_stmts.rst
   compound_stmts.rst
   toplevel_components.rst
   grammar.rst
