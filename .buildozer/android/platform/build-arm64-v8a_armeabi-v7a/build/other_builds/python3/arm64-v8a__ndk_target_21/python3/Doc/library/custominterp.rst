.. _custominterp:

**************************
Custom Python Interpreters
**************************

The modules described in this chapter allow writing interfaces similar to
Python's interactive interpreter.  If you want a Python interpreter that
supports some special feature in addition to the Python language, you should
look at the :mod:`code` module.  (The :mod:`codeop` module is lower-level, used
to support compiling a possibly incomplete chunk of Python code.)

The full list of modules described in this chapter is:


.. toctree::

   code.rst
   codeop.rst
