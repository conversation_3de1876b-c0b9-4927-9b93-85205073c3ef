
This documentation tries to help people who intend to use Python on
AIX.

There used to be many issues with Python on AIX, but the major ones
have been corrected for version 3.2, so that Python should now work
rather well on this platform. The remaining known issues are listed in
this document.


======================================================================
			   Compiling Python
----------------------------------------------------------------------

You can compile Python with gcc or the native AIX compiler. The native
compiler used to give better performances on this system with older
versions of Python.  With Python 3.2 it may not be the case anymore,
as this compiler does not allow compiling Python with computed gotos.
Some benchmarks need to be done.

Compiling with gcc:

cd Python-3.2
CC=gcc OPT="-O2" ./configure --enable-shared
make

There are various aliases for the native compiler.  The recommended
alias for compiling Python is 'xlc_r', which provides a better level of
compatibility and handles thread initialization properly.

It is a good idea to add the '-qmaxmem=70000' option, otherwise the
compiler considers various files too complex to optimize.

Compiling with xlc:

cd Python-3.2
CC=xlc_r OPT="-O2 -qmaxmem=70000" ./configure --without-computed-gotos --enable-shared
make


======================================================================
			  Memory Limitations
----------------------------------------------------------------------

Note: this section may not apply when compiling Python as a 64 bit
application.

By default on AIX each program gets one segment register for its data
segment. As each segment register covers 256 MiB, a Python program that
would use more than 256 MiB will raise a MemoryError.  The standard
Python test suite is one such application.

To allocate more segment registers to Python, you must use the linker
option -bmaxdata or the ldedit tool to specify the number of bytes you
need in the data segment.

For example, if you want to allow 512 MiB of memory for Python (this is
enough for the test suite to run without MemoryErrors), you should run
the following command at the end of compilation:

ldedit -b maxdata:0x20000000 ./python

You can allow up to 2 GiB of memory for Python by using the value
0x80000000 for maxdata.

It is also possible to go beyond 2 GiB of memory by activating Large
Page Use. You should consult the IBM documentation if you need to use
this option. You can also follow the discussion of this problem
in issue 11212 at bugs.python.org.

http://publib.boulder.ibm.com/infocenter/aix/v6r1/index.jsp?topic=/com.ibm.aix.cmds/doc/aixcmds3/ldedit.htm


======================================================================
			     Known issues
----------------------------------------------------------------------

Those issues are currently affecting Python on AIX:

* Python has not been fully tested on AIX when compiled as a 64 bit
  application.

* issue 3526: the memory used by a Python process will never be
  released to the system. If you have a Python application on AIX that
  uses a lot of memory, you should read this issue and you may
  consider using the provided patch that implements a custom malloc
  implementation

* issue 11192: test_socket fails

* issue 11190: test_locale fails

* issue 11193: test_subprocess fails

* issue 9920: minor arithmetic issues in cmath

* issue 11215: test_fileio fails



======================================================================
		Implementation details for developers
----------------------------------------------------------------------

Python and python modules can now be built as shared libraries on AIX
as usual.

AIX shared libraries require that an "export" and "import" file be
provided at compile time to list all extern symbols which may be
shared between modules.  The "export" file (named python.exp) for the
modules and the libraries that belong to the Python core is created by
the "makexp_aix" script before performing the link of the python
binary. It lists all global symbols (exported during the link) of the
modules and the libraries that make up the python executable.

When shared library modules (.so files) are made, a second shell
script is invoked.  This script is named "ld_so_aix" and is also
provided with the distribution in the Modules subdirectory.  This
script acts as an "ld" wrapper which hides the explicit management of
"export" and "import" files; it adds the appropriate arguments (in the
appropriate order) to the link command that creates the shared module.
Among other things, it specifies that the "python.exp" file is an
"import" file for the shared module.

This mechanism should be transparent.
