{\rtf1\ansi\ansicpg1252\cocoartf2709
\cocoascreenfonts1\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;\f1\fswiss\fcharset0 Helvetica-Bold;\f2\fmodern\fcharset0 CourierNewPSMT;
}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\margl1440\margr1440\vieww12200\viewh10880\viewkind0
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\partightenfactor0

\f0\fs24 \cf0 This package will install 
\f1\b Python $FULL_VERSION
\f0\b0  for 
\f1\b macOS $MACOSX_DEPLOYMENT_TARGET
\f0\b0 .\
\
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\partightenfactor0

\f1\b \cf0 Python for macOS
\f0\b0  consists of the {\field{\*\fldinst{HYPERLINK "https://www.python.org"}}{\fldrslt Python}} programming language interpreter and its batteries-included standard library to allow easy access to macOS features.  It also includes the Python integrated development environment, 
\f1\b IDLE
\f0\b0 .  You can also use the included 
\f1\b pip
\f0\b0  to download and install third-party packages from the {\field{\*\fldinst{HYPERLINK "https://pypi.org"}}{\fldrslt Python Package Index}}. \
\
At the end of this install, click on 
\f2 Install Certificates
\f0  to install a set of current SSL root certificates.\
\
}