<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" TreatAsLocalProperty="ReleaseUri">
    <PropertyGroup>
        <TargetName>$(OutputName)</TargetName>
        <DefineSolutionProperties>false</DefineSolutionProperties>
        <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
        <SuppressIces>$(SuppressIces);ICE03;ICE57;ICE61</SuppressIces>
        <CompilerSuppressSpecificWarnings>1026</CompilerSuppressSpecificWarnings>
        <BuildForRelease Condition="'$(BuildForRelease)' == ''">false</BuildForRelease>
        <SignOutput Condition="'$(SigningCertificate)' != ''">true</SignOutput>
        <Configuration Condition="'$(Configuration)' == ''">Release</Configuration>
        <Platform Condition="'$(Platform)' == ''">x86</Platform>
        <InstallScope Condition="'$(InstallScope)' != 'perMachine'">perUser</InstallScope>
        <_MakeCatCommand Condition="'$(_MakeCatCommand)' == ''">makecat</_MakeCatCommand>
        <SkipCopySSLDLL>true</SkipCopySSLDLL>
    </PropertyGroup>

    <Import Project="wix.props" />
    <Import Project="..\..\PCbuild\openssl.props" />
    <Import Project="..\..\PCbuild\tcltk.props" />

    <PropertyGroup>
        <!--
        This URI is used to generate the various GUIDs used by the installer.
        Installers built with the same URI will upgrade each other or block
        when attempting to downgrade.
        
        By default, this is the local computer name, which will produce
        installers that do not interfere with other installers. Products
        that intend to bundle Python should rebuild these modules with their
        own URI to avoid conflicting with the official releases.
        
        The official releases use "https://www.python.org/$(ArchName)"
        
        This is not the same as the DownloadUrl property used in the bundle
        projects.
        -->
        <ReleaseUri Condition="'$(ReleaseUri)' == ''">$(ComputerName)/$(ArchName)/</ReleaseUri>
        <ReleaseUri Condition="!$(ReleaseUri.EndsWith(`/`))">$(ReleaseUri)/</ReleaseUri>
    </PropertyGroup>

    
    <ItemGroup>
        <Compile Include="$(MSBuildThisFileDirectory)common.wxs" />
        <WxlTemplate Include="$(MSBuildThisFileDirectory)\*.wxl_template" Condition="$(IgnoreCommonWxlTemplates) != 'true'" />
        <WixExtension Include="WixUtilExtension">
            <HintPath>WixUtilExtension</HintPath>
            <Name>WixUtilExtension</Name>
        </WixExtension>
    </ItemGroup>

    <PropertyGroup>
        <IntermediateOutputPath>$(Py_IntDir)\$(MajorVersionNumber)$(MinorVersionNumber)$(ArchName)_$(Configuration)\msi_$(OutputName)</IntermediateOutputPath>
        <IntermediateOutputPath Condition="'$(OutputSuffix)' != ''">$(IntermediateOutputPath)_$(OutputSuffix)</IntermediateOutputPath>
        <OutputPath Condition="'$(OutputPath)' == ''">$(BuildPath)</OutputPath>
        <OutputPath Condition="!HasTrailingSlash($(OutputPath))">$(OutputPath)\</OutputPath>
        <OutDir>$(OutputPath)</OutDir>
        <ReuseCabinetCache>true</ReuseCabinetCache>
        <CRTRedist Condition="'$(CRTRedist)' == ''">$(ExternalsDir)\windows-installer\redist-1\$(Platform)</CRTRedist>
        <CRTRedist>$([System.IO.Path]::GetFullPath($(CRTRedist)))</CRTRedist>
        <TclTkLibraryDir Condition="$(TclTkLibraryDir) == ''">$(tcltkDir)lib</TclTkLibraryDir>
        <DocFilename>python$(MajorVersionNumber)$(MinorVersionNumber)$(MicroVersionNumber)$(ReleaseLevelName).chm</DocFilename>

        <InstallerVersion>$(MajorVersionNumber).$(MinorVersionNumber).$(Field3Value).0</InstallerVersion>
    </PropertyGroup>
    
    <PropertyGroup Condition="!$(BuildForRelease)">
        <RevisionNumber Condition="'$(RevisionNumber)' == ''">$([System.Math]::Floor($([System.DateTime]::Now.Subtract($([System.DateTime]::new(2001, 1, 1))).TotalDays)))</RevisionNumber>
        <PythonVersion>$(MajorVersionNumber).$(MinorVersionNumber).$(MicroVersionNumber)dev$(RevisionNumber)</PythonVersion>
        <InstallerVersion>$(MajorVersionNumber).$(MinorVersionNumber).$(RevisionNumber).0</InstallerVersion>
    </PropertyGroup>
    
    <PropertyGroup>
        <Bitness>32-bit</Bitness>
        <Bitness Condition="$(Platform) == 'x64'">64-bit</Bitness>
        <Bitness Condition="$(Platform) == 'ARM64'">ARM64</Bitness>
        <PlatformArchitecture>32bit</PlatformArchitecture>
        <PlatformArchitecture Condition="$(Platform) == 'x64'">64bit</PlatformArchitecture>
        <PlatformArchitecture Condition="$(Platform) == 'ARM64'">ARM64</PlatformArchitecture>
        <DefineConstants>
            $(DefineConstants);
            Version=$(InstallerVersion);
            ShortVersion=$(MajorVersionNumber).$(MinorVersionNumber);
            LongVersion=$(PythonVersion);
            MajorVersionNumber=$(MajorVersionNumber);
            MinorVersionNumber=$(MinorVersionNumber);
            UpgradeMinimumVersion=$(MajorVersionNumber).$(MinorVersionNumber).0.0;
            NextMajorVersionNumber=$(MajorVersionNumber).$([msbuild]::Add($(MinorVersionNumber), 1)).0.0;
            Bitness=$(Bitness);
            PlatformArchitecture=$(PlatformArchitecture);
            PyDebugExt=$(PyDebugExt);
            PyArchExt=$(PyArchExt);
            PyTestExt=$(PyTestExt);
            OptionalFeatureName=$(OutputName);
            ssltag=$(OpenSSLDLLSuffix);
            Suffix32=$(PyArchExt);
        </DefineConstants>
        <DefineConstants Condition="'$(CRTRedist)' != ''">
            $(DefineConstants);CRTRedist=$(CRTRedist);
        </DefineConstants>
    </PropertyGroup>

    <ItemDefinitionGroup>
        <InstallFiles>
            <Group>generated_filelist</Group>
            <Condition></Condition>
            <DiskId></DiskId>
            <IncludeInCat>false</IncludeInCat>
        </InstallFiles>
        <LinkerBindInputPaths>
            <Visible>false</Visible>
        </LinkerBindInputPaths>
    </ItemDefinitionGroup>
    <ItemGroup>
        <LinkerBindInputPaths Include="$(PGOBuildPath);$(BuildPath)">
            <BindName></BindName>
        </LinkerBindInputPaths>
        <LinkerBindInputPaths Include="$(PySourcePath)">
            <BindName>src</BindName>
        </LinkerBindInputPaths>
        <LinkerBindInputPaths Include="$(TclTkLibraryDir)">
            <BindName>tcltk</BindName>
        </LinkerBindInputPaths>
        <LinkerBindInputPaths Include="$(CRTRedist)">
            <BindName>redist</BindName>
        </LinkerBindInputPaths>
        <LinkerBindInputPaths Include="$(BuildPath32)">
            <BindName>build32</BindName>
        </LinkerBindInputPaths>
        <LinkerBindInputPaths Include="$(BuildPath64)">
            <BindName>build64</BindName>
        </LinkerBindInputPaths>
        <LinkerBindInputPaths Include="$(BuildPathARM64)">
            <BindName>buildarm64</BindName>
        </LinkerBindInputPaths>
    </ItemGroup>

    <Target Name="_ValidateMsiProps" BeforeTargets="PrepareForBuild">
        <Error Text="Platform '$(Platform)' is not supported. Use 'x86', 'x64' or 'ARM64'."
               Condition="$(Platform) != 'x86' and $(Platform) != 'x64' and $(Platform) != 'ARM64'" />
    </Target>
    
    <ItemGroup>
        <_Uuid Include="CoreUpgradeCode">
            <Uri>upgradecode</Uri>
        </_Uuid>
        <_Uuid Include="UpgradeCode">
            <Uri>upgradecode/$(OutputName)</Uri>
        </_Uuid>
        <_Uuid Include="InstallDirectoryGuidSeed">
            <Uri>installdirectoryseed</Uri>
        </_Uuid>
        <_Uuid Include="PythonExeComponentGuid">
            <Uri>python.exe</Uri>
        </_Uuid>
        <_Uuid Include="PythonwExeComponentGuid">
            <Uri>pythonw.exe</Uri>
        </_Uuid>
        <_Uuid Include="RemoveLib2to3PickleComponentGuid">
            <Uri>lib2to3/pickles</Uri>
        </_Uuid>
        <_Uuid Include="CommonPythonRegComponentGuid">
            <Uri>registry</Uri>
        </_Uuid>
        <_Uuid Include="PythonRegComponentGuid">
            <Uri>registry/$(OutputName)</Uri>
        </_Uuid>
    </ItemGroup>
    <Target Name="_GenerateGuids"
            AfterTargets="PrepareForBuild"
            DependsOnTargets="FindPythonForBuild"
            Condition="$(TargetName) != 'launcher'">
        <PropertyGroup>
            <_Uuids>@(_Uuid->'("%(Identity)", "$(MajorVersionNumber).$(MinorVersionNumber)/%(Uri)")',',')</_Uuids>
            <_GenerateCommand>import uuid; print('\n'.join('{}={}'.format(i, uuid.uuid5(uuid.UUID('c8d9733e-a70c-43ff-ab0c-e26456f11083'), '$(ReleaseUri.Replace(`{arch}`, `$(ArchName)`))' + j)) for i,j in [$(_Uuids.Replace(`"`,`'`))]))</_GenerateCommand>
        </PropertyGroup>
        
        <Exec Command='$(PythonForBuild) -c "$(_GenerateCommand)" &gt; "$(IntermediateOutputPath)$(OutputName)guids.txt"'
              WorkingDirectory="$(MSBuildThisFileDirectory)"
              IgnoreExitCode="false" />
        
        <ReadLinesFromFile File="$(IntermediateOutputPath)$(OutputName)guids.txt">
            <Output TaskParameter="Lines" ItemName="_UuidValue" />
        </ReadLinesFromFile>
        
        <PropertyGroup>
            <DefineConstants>$(DefineConstants);@(_UuidValue,';');</DefineConstants>
        </PropertyGroup>
    </Target>
</Project>