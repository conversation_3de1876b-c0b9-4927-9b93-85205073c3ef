------------------------------------------------------------------------
-- dqRemainder.decTest -- decQuad remainder                           --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- sanity checks (as base, above)
dqrem001 remainder  1     1    ->  0
dqrem002 remainder  2     1    ->  0
dqrem003 remainder  1     2    ->  1
dqrem004 remainder  2     2    ->  0
dqrem005 remainder  0     1    ->  0
dqrem006 remainder  0     2    ->  0
dqrem007 remainder  1     3    ->  1
dqrem008 remainder  2     3    ->  2
dqrem009 remainder  3     3    ->  0

dqrem010 remainder  2.4   1    ->  0.4
dqrem011 remainder  2.4   -1   ->  0.4
dqrem012 remainder  -2.4  1    ->  -0.4
dqrem013 remainder  -2.4  -1   ->  -0.4
dqrem014 remainder  2.40  1    ->  0.40
dqrem015 remainder  2.400 1    ->  0.400
dqrem016 remainder  2.4   2    ->  0.4
dqrem017 remainder  2.400 2    ->  0.400
dqrem018 remainder  2.    2    ->  0
dqrem019 remainder  20    20   ->  0

dqrem020 remainder  187   187    ->  0
dqrem021 remainder  5     2      ->  1
dqrem022 remainder  5     2.0    ->  1.0
dqrem023 remainder  5     2.000  ->  1.000
dqrem024 remainder  5     0.200  ->  0.000
dqrem025 remainder  5     0.200  ->  0.000

dqrem030 remainder  1     2      ->  1
dqrem031 remainder  1     4      ->  1
dqrem032 remainder  1     8      ->  1

dqrem033 remainder  1     16     ->  1
dqrem034 remainder  1     32     ->  1
dqrem035 remainder  1     64     ->  1
dqrem040 remainder  1    -2      ->  1
dqrem041 remainder  1    -4      ->  1
dqrem042 remainder  1    -8      ->  1
dqrem043 remainder  1    -16     ->  1
dqrem044 remainder  1    -32     ->  1
dqrem045 remainder  1    -64     ->  1
dqrem050 remainder -1     2      ->  -1
dqrem051 remainder -1     4      ->  -1
dqrem052 remainder -1     8      ->  -1
dqrem053 remainder -1     16     ->  -1
dqrem054 remainder -1     32     ->  -1
dqrem055 remainder -1     64     ->  -1
dqrem060 remainder -1    -2      ->  -1
dqrem061 remainder -1    -4      ->  -1
dqrem062 remainder -1    -8      ->  -1
dqrem063 remainder -1    -16     ->  -1
dqrem064 remainder -1    -32     ->  -1
dqrem065 remainder -1    -64     ->  -1

dqrem066 remainder  999999999     1  -> 0
dqrem067 remainder  999999999.4   1  -> 0.4
dqrem068 remainder  999999999.5   1  -> 0.5
dqrem069 remainder  999999999.9   1  -> 0.9
dqrem070 remainder  999999999.999 1  -> 0.999
dqrem071 remainder  999999.999999 1  -> 0.999999
dqrem072 remainder  9             1  -> 0

dqrem080 remainder  0.            1  -> 0
dqrem081 remainder  .0            1  -> 0.0
dqrem082 remainder  0.00          1  -> 0.00
dqrem083 remainder  0.00E+9       1  -> 0
dqrem084 remainder  0.00E+3       1  -> 0
dqrem085 remainder  0.00E+2       1  -> 0
dqrem086 remainder  0.00E+1       1  -> 0.0
dqrem087 remainder  0.00E+0       1  -> 0.00
dqrem088 remainder  0.00E-0       1  -> 0.00
dqrem089 remainder  0.00E-1       1  -> 0.000
dqrem090 remainder  0.00E-2       1  -> 0.0000
dqrem091 remainder  0.00E-3       1  -> 0.00000
dqrem092 remainder  0.00E-4       1  -> 0.000000
dqrem093 remainder  0.00E-5       1  -> 0E-7
dqrem094 remainder  0.00E-6       1  -> 0E-8
dqrem095 remainder  0.0000E-50    1  -> 0E-54

-- Various flavours of remainder by 0
dqrem101 remainder  0       0   -> NaN Division_undefined
dqrem102 remainder  0      -0   -> NaN Division_undefined
dqrem103 remainder -0       0   -> NaN Division_undefined
dqrem104 remainder -0      -0   -> NaN Division_undefined
dqrem105 remainder  0.0E5   0   -> NaN Division_undefined
dqrem106 remainder  0.000   0   -> NaN Division_undefined
-- [Some think this next group should be Division_by_zero exception, but
-- IEEE 854 is explicit that it is Invalid operation .. for
-- remainder-near, anyway]
dqrem107 remainder  0.0001  0   -> NaN Invalid_operation
dqrem108 remainder  0.01    0   -> NaN Invalid_operation
dqrem109 remainder  0.1     0   -> NaN Invalid_operation
dqrem110 remainder  1       0   -> NaN Invalid_operation
dqrem111 remainder  1       0.0 -> NaN Invalid_operation
dqrem112 remainder 10       0.0 -> NaN Invalid_operation
dqrem113 remainder 1E+100   0.0 -> NaN Invalid_operation
dqrem114 remainder 1E+380   0   -> NaN Invalid_operation
dqrem115 remainder  0.0001 -0   -> NaN Invalid_operation
dqrem116 remainder  0.01   -0   -> NaN Invalid_operation
dqrem119 remainder  0.1    -0   -> NaN Invalid_operation
dqrem120 remainder  1      -0   -> NaN Invalid_operation
dqrem121 remainder  1      -0.0 -> NaN Invalid_operation
dqrem122 remainder 10      -0.0 -> NaN Invalid_operation
dqrem123 remainder 1E+100  -0.0 -> NaN Invalid_operation
dqrem124 remainder 1E+384  -0   -> NaN Invalid_operation
-- and zeros on left
dqrem130 remainder  0      1   ->  0
dqrem131 remainder  0     -1   ->  0
dqrem132 remainder  0.0    1   ->  0.0
dqrem133 remainder  0.0   -1   ->  0.0
dqrem134 remainder -0      1   -> -0
dqrem135 remainder -0     -1   -> -0
dqrem136 remainder -0.0    1   -> -0.0
dqrem137 remainder -0.0   -1   -> -0.0

-- 0.5ers
dqrem143 remainder   0.5  2     ->  0.5
dqrem144 remainder   0.5  2.1   ->  0.5
dqrem145 remainder   0.5  2.01  ->  0.50
dqrem146 remainder   0.5  2.001 ->  0.500
dqrem147 remainder   0.50 2     ->  0.50
dqrem148 remainder   0.50 2.01  ->  0.50
dqrem149 remainder   0.50 2.001 ->  0.500

-- steadies
dqrem150 remainder  1  1   -> 0
dqrem151 remainder  1  2   -> 1
dqrem152 remainder  1  3   -> 1
dqrem153 remainder  1  4   -> 1
dqrem154 remainder  1  5   -> 1
dqrem155 remainder  1  6   -> 1
dqrem156 remainder  1  7   -> 1
dqrem157 remainder  1  8   -> 1
dqrem158 remainder  1  9   -> 1
dqrem159 remainder  1  10  -> 1
dqrem160 remainder  1  1   -> 0
dqrem161 remainder  2  1   -> 0
dqrem162 remainder  3  1   -> 0
dqrem163 remainder  4  1   -> 0
dqrem164 remainder  5  1   -> 0
dqrem165 remainder  6  1   -> 0
dqrem166 remainder  7  1   -> 0
dqrem167 remainder  8  1   -> 0
dqrem168 remainder  9  1   -> 0
dqrem169 remainder  10 1   -> 0

-- some differences from remainderNear
dqrem171 remainder   0.4  1.020 ->  0.400
dqrem172 remainder   0.50 1.020 ->  0.500
dqrem173 remainder   0.51 1.020 ->  0.510
dqrem174 remainder   0.52 1.020 ->  0.520
dqrem175 remainder   0.6  1.020 ->  0.600

-- More flavours of remainder by 0
dqrem201 remainder  0      0   -> NaN Division_undefined
dqrem202 remainder  0.0E5  0   -> NaN Division_undefined
dqrem203 remainder  0.000  0   -> NaN Division_undefined
dqrem204 remainder  0.0001 0   -> NaN Invalid_operation
dqrem205 remainder  0.01   0   -> NaN Invalid_operation
dqrem206 remainder  0.1    0   -> NaN Invalid_operation
dqrem207 remainder  1      0   -> NaN Invalid_operation
dqrem208 remainder  1      0.0 -> NaN Invalid_operation
dqrem209 remainder 10      0.0 -> NaN Invalid_operation
dqrem210 remainder 1E+100  0.0 -> NaN Invalid_operation
dqrem211 remainder 1E+380  0   -> NaN Invalid_operation

-- some differences from remainderNear
dqrem231 remainder  -0.4  1.020 -> -0.400
dqrem232 remainder  -0.50 1.020 -> -0.500
dqrem233 remainder  -0.51 1.020 -> -0.510
dqrem234 remainder  -0.52 1.020 -> -0.520
dqrem235 remainder  -0.6  1.020 -> -0.600

-- high Xs
dqrem240 remainder  1E+2  1.00  ->  0.00

-- dqrem3xx are from DiagBigDecimal
dqrem301 remainder   1    3     ->  1
dqrem302 remainder   5    5     ->  0
dqrem303 remainder   13   10    ->  3
dqrem304 remainder   13   50    ->  13
dqrem305 remainder   13   100   ->  13
dqrem306 remainder   13   1000  ->  13
dqrem307 remainder   .13    1   ->  0.13
dqrem308 remainder   0.133  1   ->  0.133
dqrem309 remainder   0.1033 1   ->  0.1033
dqrem310 remainder   1.033  1   ->  0.033
dqrem311 remainder   10.33  1   ->  0.33
dqrem312 remainder   10.33 10   ->  0.33
dqrem313 remainder   103.3  1   ->  0.3
dqrem314 remainder   133   10   ->  3
dqrem315 remainder   1033  10   ->  3
dqrem316 remainder   1033  50   ->  33
dqrem317 remainder   101.0  3   ->  2.0
dqrem318 remainder   102.0  3   ->  0.0
dqrem319 remainder   103.0  3   ->  1.0
dqrem320 remainder   2.40   1   ->  0.40
dqrem321 remainder   2.400  1   ->  0.400
dqrem322 remainder   2.4    1   ->  0.4
dqrem323 remainder   2.4    2   ->  0.4
dqrem324 remainder   2.400  2   ->  0.400
dqrem325 remainder   1   0.3    ->  0.1
dqrem326 remainder   1   0.30   ->  0.10
dqrem327 remainder   1   0.300  ->  0.100
dqrem328 remainder   1   0.3000 ->  0.1000
dqrem329 remainder   1.0    0.3 ->  0.1
dqrem330 remainder   1.00   0.3 ->  0.10
dqrem331 remainder   1.000  0.3 ->  0.100
dqrem332 remainder   1.0000 0.3 ->  0.1000
dqrem333 remainder   0.5  2     ->  0.5
dqrem334 remainder   0.5  2.1   ->  0.5
dqrem335 remainder   0.5  2.01  ->  0.50
dqrem336 remainder   0.5  2.001 ->  0.500
dqrem337 remainder   0.50 2     ->  0.50
dqrem338 remainder   0.50 2.01  ->  0.50
dqrem339 remainder   0.50 2.001 ->  0.500

dqrem340 remainder   0.5   0.5000001    ->  0.5000000
dqrem341 remainder   0.5   0.50000001    ->  0.50000000
dqrem342 remainder   0.5   0.500000001    ->  0.500000000
dqrem343 remainder   0.5   0.5000000001    ->  0.5000000000
dqrem344 remainder   0.5   0.50000000001    ->  0.50000000000
dqrem345 remainder   0.5   0.4999999    ->  1E-7
dqrem346 remainder   0.5   0.49999999    ->  1E-8
dqrem347 remainder   0.5   0.499999999    ->  1E-9
dqrem348 remainder   0.5   0.4999999999    ->  1E-10
dqrem349 remainder   0.5   0.49999999999    ->  1E-11
dqrem350 remainder   0.5   0.499999999999    ->  1E-12

dqrem351 remainder   0.03  7  ->  0.03
dqrem352 remainder   5   2    ->  1
dqrem353 remainder   4.1   2    ->  0.1
dqrem354 remainder   4.01   2    ->  0.01
dqrem355 remainder   4.001   2    ->  0.001
dqrem356 remainder   4.0001   2    ->  0.0001
dqrem357 remainder   4.00001   2    ->  0.00001
dqrem358 remainder   4.000001   2    ->  0.000001
dqrem359 remainder   4.0000001   2    ->  1E-7

dqrem360 remainder   1.2   0.7345 ->  0.4655
dqrem361 remainder   0.8   12     ->  0.8
dqrem362 remainder   0.8   0.2    ->  0.0
dqrem363 remainder   0.8   0.3    ->  0.2
dqrem364 remainder   0.800   12   ->  0.800
dqrem365 remainder   0.800   1.7  ->  0.800
dqrem366 remainder   2.400   2    ->  0.400

dqrem371 remainder   2.400  2        ->  0.400

dqrem381 remainder 12345  1         ->  0
dqrem382 remainder 12345  1.0001    ->  0.7657
dqrem383 remainder 12345  1.001     ->  0.668
dqrem384 remainder 12345  1.01      ->  0.78
dqrem385 remainder 12345  1.1       ->  0.8
dqrem386 remainder 12355  4         ->  3
dqrem387 remainder 12345  4         ->  1
dqrem388 remainder 12355  4.0001    ->  2.6912
dqrem389 remainder 12345  4.0001    ->  0.6914
dqrem390 remainder 12345  4.9       ->  1.9
dqrem391 remainder 12345  4.99      ->  4.73
dqrem392 remainder 12345  4.999     ->  2.469
dqrem393 remainder 12345  4.9999    ->  0.2469
dqrem394 remainder 12345  5         ->  0
dqrem395 remainder 12345  5.0001    ->  4.7532
dqrem396 remainder 12345  5.001     ->  2.532
dqrem397 remainder 12345  5.01      ->  0.36
dqrem398 remainder 12345  5.1       ->  3.0

-- the nasty division-by-1 cases
dqrem401 remainder   0.5         1   ->  0.5
dqrem402 remainder   0.55        1   ->  0.55
dqrem403 remainder   0.555       1   ->  0.555
dqrem404 remainder   0.5555      1   ->  0.5555
dqrem405 remainder   0.55555     1   ->  0.55555
dqrem406 remainder   0.555555    1   ->  0.555555
dqrem407 remainder   0.5555555   1   ->  0.5555555
dqrem408 remainder   0.55555555  1   ->  0.55555555
dqrem409 remainder   0.555555555 1   ->  0.555555555

-- folddowns
dqrem421 remainder   1E+6144        1  ->   NaN Division_impossible
dqrem422 remainder   1E+6144  1E+6143  ->   0E+6111 Clamped
dqrem423 remainder   1E+6144  2E+6143  ->   0E+6111 Clamped
dqrem424 remainder   1E+6144  3E+6143  ->   1.00000000000000000000000000000000E+6143 Clamped
dqrem425 remainder   1E+6144  4E+6143  ->   2.00000000000000000000000000000000E+6143 Clamped
dqrem426 remainder   1E+6144  5E+6143  ->   0E+6111 Clamped
dqrem427 remainder   1E+6144  6E+6143  ->   4.00000000000000000000000000000000E+6143 Clamped
dqrem428 remainder   1E+6144  7E+6143  ->   3.00000000000000000000000000000000E+6143 Clamped
dqrem429 remainder   1E+6144  8E+6143  ->   2.00000000000000000000000000000000E+6143 Clamped
dqrem430 remainder   1E+6144  9E+6143  ->   1.00000000000000000000000000000000E+6143 Clamped
-- tinies
dqrem431 remainder   1E-6175  1E-6176  ->   0E-6176
dqrem432 remainder   1E-6175  2E-6176  ->   0E-6176
dqrem433 remainder   1E-6175  3E-6176  ->   1E-6176 Subnormal
dqrem434 remainder   1E-6175  4E-6176  ->   2E-6176 Subnormal
dqrem435 remainder   1E-6175  5E-6176  ->   0E-6176
dqrem436 remainder   1E-6175  6E-6176  ->   4E-6176 Subnormal
dqrem437 remainder   1E-6175  7E-6176  ->   3E-6176 Subnormal
dqrem438 remainder   1E-6175  8E-6176  ->   2E-6176 Subnormal
dqrem439 remainder   1E-6175  9E-6176  ->   1E-6176 Subnormal
dqrem440 remainder   1E-6175 10E-6176  ->   0E-6176
dqrem441 remainder   1E-6175 11E-6176  -> 1.0E-6175 Subnormal
dqrem442 remainder 100E-6175 11E-6176  -> 1.0E-6175 Subnormal
dqrem443 remainder 100E-6175 20E-6176  ->   0E-6176
dqrem444 remainder 100E-6175 21E-6176  -> 1.3E-6175 Subnormal
dqrem445 remainder 100E-6175 30E-6176  -> 1.0E-6175 Subnormal

-- zero signs
dqrem650 remainder  1  1 ->  0
dqrem651 remainder -1  1 -> -0
dqrem652 remainder  1 -1 ->  0
dqrem653 remainder -1 -1 -> -0
dqrem654 remainder  0  1 ->  0
dqrem655 remainder -0  1 -> -0
dqrem656 remainder  0 -1 ->  0
dqrem657 remainder -0 -1 -> -0
dqrem658 remainder  0.00  1  ->  0.00
dqrem659 remainder -0.00  1  -> -0.00

-- Specials
dqrem680 remainder  Inf  -Inf   ->  NaN Invalid_operation
dqrem681 remainder  Inf  -1000  ->  NaN Invalid_operation
dqrem682 remainder  Inf  -1     ->  NaN Invalid_operation
dqrem683 remainder  Inf   0     ->  NaN Invalid_operation
dqrem684 remainder  Inf  -0     ->  NaN Invalid_operation
dqrem685 remainder  Inf   1     ->  NaN Invalid_operation
dqrem686 remainder  Inf   1000  ->  NaN Invalid_operation
dqrem687 remainder  Inf   Inf   ->  NaN Invalid_operation
dqrem688 remainder -1000  Inf   -> -1000
dqrem689 remainder -Inf   Inf   ->  NaN Invalid_operation
dqrem691 remainder -1     Inf   -> -1
dqrem692 remainder  0     Inf   ->  0
dqrem693 remainder -0     Inf   -> -0
dqrem694 remainder  1     Inf   ->  1
dqrem695 remainder  1000  Inf   ->  1000
dqrem696 remainder  Inf   Inf   ->  NaN Invalid_operation

dqrem700 remainder -Inf  -Inf   ->  NaN Invalid_operation
dqrem701 remainder -Inf  -1000  ->  NaN Invalid_operation
dqrem702 remainder -Inf  -1     ->  NaN Invalid_operation
dqrem703 remainder -Inf  -0     ->  NaN Invalid_operation
dqrem704 remainder -Inf   0     ->  NaN Invalid_operation
dqrem705 remainder -Inf   1     ->  NaN Invalid_operation
dqrem706 remainder -Inf   1000  ->  NaN Invalid_operation
dqrem707 remainder -Inf   Inf   ->  NaN Invalid_operation
dqrem708 remainder -Inf  -Inf   ->  NaN Invalid_operation
dqrem709 remainder -1000  Inf   -> -1000
dqrem710 remainder -1    -Inf   -> -1
dqrem711 remainder -0    -Inf   -> -0
dqrem712 remainder  0    -Inf   ->  0
dqrem713 remainder  1    -Inf   ->  1
dqrem714 remainder  1000 -Inf   ->  1000
dqrem715 remainder  Inf  -Inf   ->  NaN Invalid_operation

dqrem721 remainder  NaN -Inf    ->  NaN
dqrem722 remainder  NaN -1000   ->  NaN
dqrem723 remainder  NaN -1      ->  NaN
dqrem724 remainder  NaN -0      ->  NaN
dqrem725 remainder -NaN  0      -> -NaN
dqrem726 remainder  NaN  1      ->  NaN
dqrem727 remainder  NaN  1000   ->  NaN
dqrem728 remainder  NaN  Inf    ->  NaN
dqrem729 remainder  NaN -NaN    ->  NaN
dqrem730 remainder -Inf  NaN    ->  NaN
dqrem731 remainder -1000 NaN    ->  NaN
dqrem732 remainder -1    NaN    ->  NaN
dqrem733 remainder -0   -NaN    -> -NaN
dqrem734 remainder  0    NaN    ->  NaN
dqrem735 remainder  1   -NaN    -> -NaN
dqrem736 remainder  1000 NaN    ->  NaN
dqrem737 remainder  Inf  NaN    ->  NaN

dqrem741 remainder  sNaN -Inf   ->  NaN  Invalid_operation
dqrem742 remainder  sNaN -1000  ->  NaN  Invalid_operation
dqrem743 remainder -sNaN -1     -> -NaN  Invalid_operation
dqrem744 remainder  sNaN -0     ->  NaN  Invalid_operation
dqrem745 remainder  sNaN  0     ->  NaN  Invalid_operation
dqrem746 remainder  sNaN  1     ->  NaN  Invalid_operation
dqrem747 remainder  sNaN  1000  ->  NaN  Invalid_operation
dqrem749 remainder  sNaN  NaN   ->  NaN  Invalid_operation
dqrem750 remainder  sNaN sNaN   ->  NaN  Invalid_operation
dqrem751 remainder  NaN  sNaN   ->  NaN  Invalid_operation
dqrem752 remainder -Inf  sNaN   ->  NaN  Invalid_operation
dqrem753 remainder -1000 sNaN   ->  NaN  Invalid_operation
dqrem754 remainder -1    sNaN   ->  NaN  Invalid_operation
dqrem755 remainder -0    sNaN   ->  NaN  Invalid_operation
dqrem756 remainder  0    sNaN   ->  NaN  Invalid_operation
dqrem757 remainder  1    sNaN   ->  NaN  Invalid_operation
dqrem758 remainder  1000 sNaN   ->  NaN  Invalid_operation
dqrem759 remainder  Inf -sNaN   -> -NaN  Invalid_operation

-- propaging NaNs
dqrem760 remainder  NaN1   NaN7   ->  NaN1
dqrem761 remainder sNaN2   NaN8   ->  NaN2 Invalid_operation
dqrem762 remainder  NaN3  sNaN9   ->  NaN9 Invalid_operation
dqrem763 remainder sNaN4  sNaN10  ->  NaN4 Invalid_operation
dqrem764 remainder    15   NaN11  ->  NaN11
dqrem765 remainder  NaN6   NaN12  ->  NaN6
dqrem766 remainder  Inf    NaN13  ->  NaN13
dqrem767 remainder  NaN14  -Inf   ->  NaN14
dqrem768 remainder    0    NaN15  ->  NaN15
dqrem769 remainder  NaN16   -0    ->  NaN16

-- edge cases of impossible
dqrem770  remainder  1234568888888887777777777890123456  10    ->  6
dqrem771  remainder  1234568888888887777777777890123456   1    ->  0
dqrem772  remainder  1234568888888887777777777890123456   0.1  ->  NaN Division_impossible
dqrem773  remainder  1234568888888887777777777890123456   0.01 ->  NaN Division_impossible

-- long operand checks
dqrem801 remainder 12345678000 100 -> 0
dqrem802 remainder 1 12345678000   -> 1
dqrem803 remainder 1234567800  10  -> 0
dqrem804 remainder 1 1234567800    -> 1
dqrem805 remainder 1234567890  10  -> 0
dqrem806 remainder 1 1234567890    -> 1
dqrem807 remainder 1234567891  10  -> 1
dqrem808 remainder 1 1234567891    -> 1
dqrem809 remainder 12345678901 100 -> 1
dqrem810 remainder 1 12345678901   -> 1
dqrem811 remainder 1234567896  10  -> 6
dqrem812 remainder 1 1234567896    -> 1

dqrem821 remainder 12345678000 100 -> 0
dqrem822 remainder 1 12345678000   -> 1
dqrem823 remainder 1234567800  10  -> 0
dqrem824 remainder 1 1234567800    -> 1
dqrem825 remainder 1234567890  10  -> 0
dqrem826 remainder 1 1234567890    -> 1
dqrem827 remainder 1234567891  10  -> 1
dqrem828 remainder 1 1234567891    -> 1
dqrem829 remainder 12345678901 100 -> 1
dqrem830 remainder 1 12345678901   -> 1
dqrem831 remainder 1234567896  10  -> 6
dqrem832 remainder 1 1234567896    -> 1

-- from divideint
dqrem840 remainder  100000000.0   1  ->  0.0
dqrem841 remainder  100000000.4   1  ->  0.4
dqrem842 remainder  100000000.5   1  ->  0.5
dqrem843 remainder  100000000.9   1  ->  0.9
dqrem844 remainder  100000000.999 1  ->  0.999
dqrem850 remainder  100000003     5  ->  3
dqrem851 remainder  10000003      5  ->  3
dqrem852 remainder  1000003       5  ->  3
dqrem853 remainder  100003        5  ->  3
dqrem854 remainder  10003         5  ->  3
dqrem855 remainder  1003          5  ->  3
dqrem856 remainder  103           5  ->  3
dqrem857 remainder  13            5  ->  3
dqrem858 remainder  1             5  ->  1

-- Vladimir's cases         1234567890123456
dqrem860 remainder 123.0e1 1000000000000000  -> 1230
dqrem861 remainder 1230    1000000000000000  -> 1230
dqrem862 remainder 12.3e2  1000000000000000  -> 1230
dqrem863 remainder 1.23e3  1000000000000000  -> 1230
dqrem864 remainder 123e1   1000000000000000  -> 1230
dqrem870 remainder 123e1    1000000000000000 -> 1230
dqrem871 remainder 123e1     100000000000000 -> 1230
dqrem872 remainder 123e1      10000000000000 -> 1230
dqrem873 remainder 123e1       1000000000000 -> 1230
dqrem874 remainder 123e1        100000000000 -> 1230
dqrem875 remainder 123e1         10000000000 -> 1230
dqrem876 remainder 123e1          1000000000 -> 1230
dqrem877 remainder 123e1           100000000 -> 1230
dqrem878 remainder 1230            100000000 -> 1230
dqrem879 remainder 123e1            10000000 -> 1230
dqrem880 remainder 123e1             1000000 -> 1230
dqrem881 remainder 123e1              100000 -> 1230
dqrem882 remainder 123e1               10000 -> 1230
dqrem883 remainder 123e1                1000 ->  230
dqrem884 remainder 123e1                 100 ->   30
dqrem885 remainder 123e1                  10 ->    0
dqrem886 remainder 123e1                   1 ->    0

dqrem890 remainder 123e1    2000000000000000 -> 1230
dqrem891 remainder 123e1     200000000000000 -> 1230
dqrem892 remainder 123e1      20000000000000 -> 1230
dqrem893 remainder 123e1       2000000000000 -> 1230
dqrem894 remainder 123e1        200000000000 -> 1230
dqrem895 remainder 123e1         20000000000 -> 1230
dqrem896 remainder 123e1          2000000000 -> 1230
dqrem897 remainder 123e1           200000000 -> 1230
dqrem899 remainder 123e1            20000000 -> 1230
dqrem900 remainder 123e1             2000000 -> 1230
dqrem901 remainder 123e1              200000 -> 1230
dqrem902 remainder 123e1               20000 -> 1230
dqrem903 remainder 123e1                2000 -> 1230
dqrem904 remainder 123e1                 200 ->   30
dqrem905 remainder 123e1                  20 ->   10
dqrem906 remainder 123e1                   2 ->    0

dqrem910 remainder 123e1    5000000000000000 -> 1230
dqrem911 remainder 123e1     500000000000000 -> 1230
dqrem912 remainder 123e1      50000000000000 -> 1230
dqrem913 remainder 123e1       5000000000000 -> 1230
dqrem914 remainder 123e1        500000000000 -> 1230
dqrem915 remainder 123e1         50000000000 -> 1230
dqrem916 remainder 123e1          5000000000 -> 1230
dqrem917 remainder 123e1           500000000 -> 1230
dqrem919 remainder 123e1            50000000 -> 1230
dqrem920 remainder 123e1             5000000 -> 1230
dqrem921 remainder 123e1              500000 -> 1230
dqrem922 remainder 123e1               50000 -> 1230
dqrem923 remainder 123e1                5000 -> 1230
dqrem924 remainder 123e1                 500 ->  230
dqrem925 remainder 123e1                  50 ->   30
dqrem926 remainder 123e1                   5 ->    0

dqrem930 remainder 123e1    9000000000000000 -> 1230
dqrem931 remainder 123e1     900000000000000 -> 1230
dqrem932 remainder 123e1      90000000000000 -> 1230
dqrem933 remainder 123e1       9000000000000 -> 1230
dqrem934 remainder 123e1        900000000000 -> 1230
dqrem935 remainder 123e1         90000000000 -> 1230
dqrem936 remainder 123e1          9000000000 -> 1230
dqrem937 remainder 123e1           900000000 -> 1230
dqrem939 remainder 123e1            90000000 -> 1230
dqrem940 remainder 123e1             9000000 -> 1230
dqrem941 remainder 123e1              900000 -> 1230
dqrem942 remainder 123e1               90000 -> 1230
dqrem943 remainder 123e1                9000 -> 1230
dqrem944 remainder 123e1                 900 ->  330
dqrem945 remainder 123e1                  90 ->   60
dqrem946 remainder 123e1                   9 ->    6

dqrem950 remainder 123e1   1000000000000000 -> 1230
dqrem961 remainder 123e1   2999999999999999 -> 1230
dqrem962 remainder 123e1   3999999999999999 -> 1230
dqrem963 remainder 123e1   4999999999999999 -> 1230
dqrem964 remainder 123e1   5999999999999999 -> 1230
dqrem965 remainder 123e1   6999999999999999 -> 1230
dqrem966 remainder 123e1   7999999999999999 -> 1230
dqrem967 remainder 123e1   8999999999999999 -> 1230
dqrem968 remainder 123e1   9999999999999999 -> 1230
dqrem969 remainder 123e1   9876543210987654 -> 1230

dqrem980 remainder 123e1 1000E299 -> 1.23E+3  -- 123E+1 internally

-- overflow and underflow tests [from divide]
dqrem1051 remainder  1e+277  1e-311 ->  NaN Division_impossible
dqrem1052 remainder  1e+277 -1e-311 ->  NaN Division_impossible
dqrem1053 remainder -1e+277  1e-311 ->  NaN Division_impossible
dqrem1054 remainder -1e+277 -1e-311 ->  NaN Division_impossible
dqrem1055 remainder  1e-277  1e+311 ->  1E-277
dqrem1056 remainder  1e-277 -1e+311 ->  1E-277
dqrem1057 remainder -1e-277  1e+311 -> -1E-277
dqrem1058 remainder -1e-277 -1e+311 -> -1E-277

-- Gyuris example
dqrem1070 remainder 8.336804418094040989630006819881709E-6143 8.336804418094040989630006819889000E-6143 -> 8.336804418094040989630006819881709E-6143

-- destructive subtract
dqrem1120  remainder  1234567890123456789012345678901234  1.000000000000000000000000000000001  ->  0.765432109876543210987654321098768
dqrem1121  remainder  1234567890123456789012345678901234   1.00000000000000000000000000000001  ->   0.65432109876543210987654321098779
dqrem1122  remainder  1234567890123456789012345678901234    1.0000000000000000000000000000001  ->    0.5432109876543210987654321098890
dqrem1123  remainder  1234567890123456789012345678901255  4.000000000000000000000000000000001  ->  2.691358027469135802746913580274687
dqrem1124  remainder  1234567890123456789012345678901234  4.000000000000000000000000000000001  ->  1.691358027469135802746913580274692
dqrem1125  remainder  1234567890123456789012345678901234    4.9999999999999999999999999999999  ->    3.6913578024691357802469135780251
dqrem1126  remainder  1234567890123456789012345678901234   4.99999999999999999999999999999999  ->   1.46913578024691357802469135780247
dqrem1127  remainder  1234567890123456789012345678901234  4.999999999999999999999999999999999  ->  4.246913578024691357802469135780246
dqrem1128  remainder  1234567890123456789012345678901234    5.0000000000000000000000000000001  ->    4.3086421975308642197530864219759

-- Null tests
dqrem1000 remainder 10  # -> NaN Invalid_operation
dqrem1001 remainder  # 10 -> NaN Invalid_operation

