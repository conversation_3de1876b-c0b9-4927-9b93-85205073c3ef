:mod:`uu` --- Encode and decode uuencode files
==============================================

.. module:: uu
   :synopsis: Encode and decode files in uuencode format.
   :deprecated:

.. moduleauthor:: <PERSON>

**Source code:** :source:`Lib/uu.py`

.. deprecated-removed:: 3.11 3.13
   The :mod:`uu` module is deprecated
   (see :pep:`PEP 594 <594#uu-and-the-uu-encoding>` for details).
   :mod:`base64` is a modern alternative.

--------------

This module encodes and decodes files in uuencode format, allowing arbitrary
binary data to be transferred over ASCII-only connections. Wherever a file
argument is expected, the methods accept a file-like object.  For backwards
compatibility, a string containing a pathname is also accepted, and the
corresponding file will be opened for reading and writing; the pathname ``'-'``
is understood to mean the standard input or output.  However, this interface is
deprecated; it's better for the caller to open the file itself, and be sure
that, when required, the mode is ``'rb'`` or ``'wb'`` on Windows.

.. index::
   single: <PERSON>, Jack
   single: <PERSON>, <PERSON> code was contributed by <PERSON>linghouse, and modified by <PERSON>.

The :mod:`uu` module defines the following functions:


.. function:: encode(in_file, out_file, name=None, mode=None, *, backtick=False)

   Uuencode file *in_file* into file *out_file*.  The uuencoded file will have
   the header specifying *name* and *mode* as the defaults for the results of
   decoding the file. The default defaults are taken from *in_file*, or ``'-'``
   and ``0o666`` respectively.  If *backtick* is true, zeros are represented by
   ``'`'`` instead of spaces.

   .. versionchanged:: 3.7
      Added the *backtick* parameter.


.. function:: decode(in_file, out_file=None, mode=None, quiet=False)

   This call decodes uuencoded file *in_file* placing the result on file
   *out_file*. If *out_file* is a pathname, *mode* is used to set the permission
   bits if the file must be created. Defaults for *out_file* and *mode* are taken
   from the uuencode header.  However, if the file specified in the header already
   exists, a :exc:`uu.Error` is raised.

   :func:`decode` may print a warning to standard error if the input was produced
   by an incorrect uuencoder and Python could recover from that error.  Setting
   *quiet* to a true value silences this warning.


.. exception:: Error()

   Subclass of :exc:`Exception`, this can be raised by :func:`uu.decode` under
   various situations, such as described above, but also including a badly
   formatted header, or truncated input file.


.. seealso::

   Module :mod:`binascii`
      Support module containing ASCII-to-binary and binary-to-ASCII conversions.
