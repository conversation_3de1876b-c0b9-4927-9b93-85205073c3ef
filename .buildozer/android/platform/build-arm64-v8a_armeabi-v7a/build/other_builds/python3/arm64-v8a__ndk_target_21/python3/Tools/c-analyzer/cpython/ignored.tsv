filename	funcname	name	reason
#???	-	somevar	???

##################################
# ignored by design

Python/pylifecycle.c	-	_PyRuntime	-


##################################
# forward/extern references
# XXX The analyzer should have ignored these.

Include/py_curses.h	-	PyCurses_API	-
Include/pydecimal.h	-	_decimal_api	-
Modules/_blake2/blake2module.c	-	blake2b_type_spec	-
Modules/_blake2/blake2module.c	-	blake2s_type_spec	-
Modules/_io/fileio.c	-	_Py_open_cloexec_works	-
Modules/_io/_iomodule.h	-	PyIOBase_Type	-
Modules/_io/_iomodule.h	-	PyRawIOBase_Type	-
Modules/_io/_iomodule.h	-	PyBufferedIOBase_Type	-
Modules/_io/_iomodule.h	-	PyTextIOBase_Type	-
Modules/_io/_iomodule.h	-	PyFileIO_Type	-
Modules/_io/_iomodule.h	-	PyBytesIO_Type	-
Modules/_io/_iomodule.h	-	PyStringIO_Type	-
Modules/_io/_iomodule.h	-	PyBufferedReader_Type	-
Modules/_io/_iomodule.h	-	PyBufferedWriter_Type	-
Modules/_io/_iomodule.h	-	PyBufferedRWPair_Type	-
Modules/_io/_iomodule.h	-	PyBufferedRandom_Type	-
Modules/_io/_iomodule.h	-	PyTextIOWrapper_Type	-
Modules/_io/_iomodule.h	-	PyIncrementalNewlineDecoder_Type	-
Modules/_io/_iomodule.h	-	_PyBytesIOBuffer_Type	-
Modules/_io/_iomodule.h	-	_PyIO_str_close	-
Modules/_io/_iomodule.h	-	_PyIO_str_closed	-
Modules/_io/_iomodule.h	-	_PyIO_str_decode	-
Modules/_io/_iomodule.h	-	_PyIO_str_encode	-
Modules/_io/_iomodule.h	-	_PyIO_str_fileno	-
Modules/_io/_iomodule.h	-	_PyIO_str_flush	-
Modules/_io/_iomodule.h	-	_PyIO_str_getstate	-
Modules/_io/_iomodule.h	-	_PyIO_str_isatty	-
Modules/_io/_iomodule.h	-	_PyIO_str_newlines	-
Modules/_io/_iomodule.h	-	_PyIO_str_nl	-
Modules/_io/_iomodule.h	-	_PyIO_str_peek	-
Modules/_io/_iomodule.h	-	_PyIO_str_read	-
Modules/_io/_iomodule.h	-	_PyIO_str_read1	-
Modules/_io/_iomodule.h	-	_PyIO_str_readable	-
Modules/_io/_iomodule.h	-	_PyIO_str_readall	-
Modules/_io/_iomodule.h	-	_PyIO_str_readinto	-
Modules/_io/_iomodule.h	-	_PyIO_str_readline	-
Modules/_io/_iomodule.h	-	_PyIO_str_reset	-
Modules/_io/_iomodule.h	-	_PyIO_str_seek	-
Modules/_io/_iomodule.h	-	_PyIO_str_seekable	-
Modules/_io/_iomodule.h	-	_PyIO_str_setstate	-
Modules/_io/_iomodule.h	-	_PyIO_str_tell	-
Modules/_io/_iomodule.h	-	_PyIO_str_truncate	-
Modules/_io/_iomodule.h	-	_PyIO_str_writable	-
Modules/_io/_iomodule.h	-	_PyIO_str_write	-
Modules/_io/_iomodule.h	-	_PyIO_empty_str	-
Modules/_io/_iomodule.h	-	_PyIO_empty_bytes	-
Modules/_multiprocessing/multiprocessing.h	-	_PyMp_SemLockType	-
Modules/_sqlite/module.c	-	_pysqlite_converters	-
Modules/_sqlite/module.c	-	_pysqlite_enable_callback_tracebacks	-
Modules/_sqlite/module.c	-	pysqlite_BaseTypeAdapted	-
Modules/_sqlite/module.h	-	pysqlite_global_state	-
Modules/_testcapimodule.c	-	_PyBytesIOBuffer_Type	-
Modules/posixmodule.c	-	_Py_open_cloexec_works	-
Python/importdl.h	-	_PyImport_DynLoadFiletab	-


##################################
# test code

Modules/_ctypes/_ctypes_test.c	-	_ctypes_test_slots	-
Modules/_ctypes/_ctypes_test.c	-	_ctypes_testmodule	-
Modules/_ctypes/_ctypes_test.c	-	_xxx_lib	-
Modules/_ctypes/_ctypes_test.c	-	an_integer	-
Modules/_ctypes/_ctypes_test.c	-	bottom	-
Modules/_ctypes/_ctypes_test.c	-	last_tf_arg_s	-
Modules/_ctypes/_ctypes_test.c	-	last_tf_arg_u	-
Modules/_ctypes/_ctypes_test.c	-	last_tfrsuv_arg	-
Modules/_ctypes/_ctypes_test.c	-	left	-
Modules/_ctypes/_ctypes_test.c	-	module_methods	-
Modules/_ctypes/_ctypes_test.c	-	my_eggs	-
Modules/_ctypes/_ctypes_test.c	-	my_spams	-
Modules/_ctypes/_ctypes_test.c	-	right	-
Modules/_ctypes/_ctypes_test.c	-	top	-
Modules/_testbuffer.c	-	NDArray_Type	-
Modules/_testbuffer.c	-	StaticArray_Type	-
Modules/_testbuffer.c	-	Struct	-
Modules/_testbuffer.c	-	_testbuffer_functions	-
Modules/_testbuffer.c	-	_testbuffermodule	-
Modules/_testbuffer.c	-	calcsize	-
Modules/_testbuffer.c	-	infobuf	-
Modules/_testbuffer.c	-	ndarray_as_buffer	-
Modules/_testbuffer.c	-	ndarray_as_mapping	-
Modules/_testbuffer.c	-	ndarray_as_sequence	-
Modules/_testbuffer.c	-	ndarray_getset	-
Modules/_testbuffer.c	-	ndarray_methods	-
Modules/_testbuffer.c	-	simple_fmt	-
Modules/_testbuffer.c	-	simple_format	-
Modules/_testbuffer.c	-	static_buffer	-
Modules/_testbuffer.c	-	static_mem	-
Modules/_testbuffer.c	-	static_shape	-
Modules/_testbuffer.c	-	static_strides	-
Modules/_testbuffer.c	-	staticarray_as_buffer	-
Modules/_testbuffer.c	-	structmodule	-
Modules/_testbuffer.c	ndarray_init	kwlist	-
Modules/_testbuffer.c	ndarray_memoryview_from_buffer	format	-
Modules/_testbuffer.c	ndarray_memoryview_from_buffer	info	-
Modules/_testbuffer.c	ndarray_memoryview_from_buffer	shape	-
Modules/_testbuffer.c	ndarray_memoryview_from_buffer	strides	-
Modules/_testbuffer.c	ndarray_memoryview_from_buffer	suboffsets	-
Modules/_testbuffer.c	ndarray_push	kwlist	-
Modules/_testbuffer.c	staticarray_init	kwlist	-
Modules/_testcapimodule.c	-	ContainerNoGC_members	-
Modules/_testcapimodule.c	-	ContainerNoGC_type	-
Modules/_testcapimodule.c	-	FmData	-
Modules/_testcapimodule.c	-	FmHook	-
Modules/_testcapimodule.c	-	GenericAlias_Type	-
Modules/_testcapimodule.c	-	Generic_Type	-
Modules/_testcapimodule.c	-	HeapCTypeSetattr_slots	-
Modules/_testcapimodule.c	-	HeapCTypeSetattr_spec	-
Modules/_testcapimodule.c	-	HeapCTypeSubclassWithFinalizer_slots	-
Modules/_testcapimodule.c	-	HeapCTypeSubclassWithFinalizer_spec	-
Modules/_testcapimodule.c	-	HeapCTypeSubclass_slots	-
Modules/_testcapimodule.c	-	HeapCTypeSubclass_spec	-
Modules/_testcapimodule.c	-	HeapCTypeWithBuffer_slots	-
Modules/_testcapimodule.c	-	HeapCTypeWithBuffer_spec	-
Modules/_testcapimodule.c	-	HeapCTypeWithDict_slots	-
Modules/_testcapimodule.c	-	HeapCTypeWithDict_spec	-
Modules/_testcapimodule.c	-	HeapCTypeWithNegativeDict_slots	-
Modules/_testcapimodule.c	-	HeapCTypeWithNegativeDict_spec	-
Modules/_testcapimodule.c	-	HeapCTypeWithWeakref_slots	-
Modules/_testcapimodule.c	-	HeapCTypeWithWeakref_spec	-
Modules/_testcapimodule.c	-	HeapCType_slots	-
Modules/_testcapimodule.c	-	HeapCType_spec	-
Modules/_testcapimodule.c	-	HeapDocCType_slots	-
Modules/_testcapimodule.c	-	HeapDocCType_spec	-
Modules/_testcapimodule.c	-	HeapGcCType_slots	-
Modules/_testcapimodule.c	-	HeapGcCType_spec	-
Modules/_testcapimodule.c	-	MethClass_Type	-
Modules/_testcapimodule.c	-	MethInstance_Type	-
Modules/_testcapimodule.c	-	MethStatic_Type	-
Modules/_testcapimodule.c	-	MethodDescriptor2_Type	-
Modules/_testcapimodule.c	-	MethodDescriptorBase_Type	-
Modules/_testcapimodule.c	-	MethodDescriptorDerived_Type	-
Modules/_testcapimodule.c	-	MethodDescriptorNopGet_Type	-
Modules/_testcapimodule.c	-	MyList_Type	-
Modules/_testcapimodule.c	-	PyRecursingInfinitelyError_Type	-
Modules/_testcapimodule.c	-	TestError	-
Modules/_testcapimodule.c	-	TestMethods	-
Modules/_testcapimodule.c	-	_HashInheritanceTester_Type	-
Modules/_testcapimodule.c	-	_testcapimodule	-
Modules/_testcapimodule.c	-	awaitType	-
Modules/_testcapimodule.c	-	awaitType_as_async	-
Modules/_testcapimodule.c	-	capsule_context	-
Modules/_testcapimodule.c	-	capsule_destructor_call_count	-
Modules/_testcapimodule.c	-	capsule_error	-
Modules/_testcapimodule.c	-	capsule_name	-
Modules/_testcapimodule.c	-	capsule_pointer	-
Modules/_testcapimodule.c	-	decimal_initialized	-
Modules/_testcapimodule.c	-	generic_alias_methods	-
Modules/_testcapimodule.c	-	generic_methods	-
Modules/_testcapimodule.c	-	heapctype_members	-
Modules/_testcapimodule.c	-	heapctypesetattr_members	-
Modules/_testcapimodule.c	-	heapctypesubclass_members	-
Modules/_testcapimodule.c	-	heapctypewithdict_getsetlist	-
Modules/_testcapimodule.c	-	heapctypewithdict_members	-
Modules/_testcapimodule.c	-	heapctypewithnegativedict_members	-
Modules/_testcapimodule.c	-	heapctypewithweakref_members	-
Modules/_testcapimodule.c	-	ipowType	-
Modules/_testcapimodule.c	-	ipowType_as_number	-
Modules/_testcapimodule.c	-	matmulType	-
Modules/_testcapimodule.c	-	matmulType_as_number	-
Modules/_testcapimodule.c	-	meth_class_methods	-
Modules/_testcapimodule.c	-	meth_instance_methods	-
Modules/_testcapimodule.c	-	meth_static_methods	-
Modules/_testcapimodule.c	-	ml	-
Modules/_testcapimodule.c	-	str1	-
Modules/_testcapimodule.c	-	str2	-
Modules/_testcapimodule.c	-	test_members	-
Modules/_testcapimodule.c	-	test_run_counter	-
Modules/_testcapimodule.c	-	test_structmembersType	-
Modules/_testcapimodule.c	-	thread_done	-
Modules/_testcapimodule.c	-	x	-
Modules/_testcapimodule.c	getargs_keyword_only	keywords	-
Modules/_testcapimodule.c	getargs_keywords	keywords	-
Modules/_testcapimodule.c	getargs_positional_only_and_keywords	keywords	-
Modules/_testcapimodule.c	make_exception_with_doc	kwlist	-
Modules/_testcapimodule.c	raise_SIGINT_then_send_None	PyId_send	-
Modules/_testcapimodule.c	slot_tp_del	PyId___tp_del__	-
Modules/_testcapimodule.c	test_capsule	buffer	-
Modules/_testcapimodule.c	test_empty_argparse	kwlist	-
Modules/_testcapimodule.c	test_structmembers_new	keywords	-
Modules/_testcapimodule.c	getargs_s_hash_int	keywords	-
Modules/_testimportmultiple.c	-	_barmodule	-
Modules/_testimportmultiple.c	-	_foomodule	-
Modules/_testimportmultiple.c	-	_testimportmultiple	-
Modules/_testinternalcapi.c	-	TestMethods	-
Modules/_testinternalcapi.c	-	_testcapimodule	-
Modules/_testmultiphase.c	-	Example_Type_slots	-
Modules/_testmultiphase.c	-	Example_Type_spec	-
Modules/_testmultiphase.c	-	Example_methods	-
Modules/_testmultiphase.c	-	StateAccessType_Type_slots	-
Modules/_testmultiphase.c	-	StateAccessType_methods	-
Modules/_testmultiphase.c	-	StateAccessType_spec	-
Modules/_testmultiphase.c	-	Str_Type_slots	-
Modules/_testmultiphase.c	-	Str_Type_spec	-
Modules/_testmultiphase.c	-	def_bad_large	-
Modules/_testmultiphase.c	-	def_bad_negative	-
Modules/_testmultiphase.c	-	def_create_int_with_state	-
Modules/_testmultiphase.c	-	def_create_null	-
Modules/_testmultiphase.c	-	def_create_raise	-
Modules/_testmultiphase.c	-	def_create_unreported_exception	-
Modules/_testmultiphase.c	-	def_exec_err	-
Modules/_testmultiphase.c	-	def_exec_raise	-
Modules/_testmultiphase.c	-	def_exec_unreported_exception	-
Modules/_testmultiphase.c	-	def_meth_state_access	-
Modules/_testmultiphase.c	-	def_negative_size	-
Modules/_testmultiphase.c	-	def_nonascii_kana	-
Modules/_testmultiphase.c	-	def_nonascii_latin	-
Modules/_testmultiphase.c	-	def_nonmodule	-
Modules/_testmultiphase.c	-	def_nonmodule_with_exec_slots	-
Modules/_testmultiphase.c	-	def_nonmodule_with_methods	-
Modules/_testmultiphase.c	-	imp_dummy_def	-
Modules/_testmultiphase.c	-	main_def	-
Modules/_testmultiphase.c	-	main_slots	-
Modules/_testmultiphase.c	-	meth_state_access_slots	-
Modules/_testmultiphase.c	-	nonmodule_methods	-
Modules/_testmultiphase.c	-	null_slots_def	-
Modules/_testmultiphase.c	-	slots_bad_large	-
Modules/_testmultiphase.c	-	slots_bad_negative	-
Modules/_testmultiphase.c	-	slots_create_nonmodule	-
Modules/_testmultiphase.c	-	slots_create_nonmodule	-
Modules/_testmultiphase.c	-	slots_create_null	-
Modules/_testmultiphase.c	-	slots_create_raise	-
Modules/_testmultiphase.c	-	slots_create_unreported_exception	-
Modules/_testmultiphase.c	-	slots_exec_err	-
Modules/_testmultiphase.c	-	slots_exec_raise	-
Modules/_testmultiphase.c	-	slots_exec_unreported_exception	-
Modules/_testmultiphase.c	-	slots_nonmodule_with_exec_slots	-
Modules/_testmultiphase.c	-	testexport_methods	-
Modules/_testmultiphase.c	-	uninitialized_def	-
Modules/_xxtestfuzz/_xxtestfuzz.c	-	_fuzzmodule	-
Modules/_xxtestfuzz/_xxtestfuzz.c	-	module_methods	-
Modules/_xxtestfuzz/fuzzer.c	-	SRE_FLAG_DEBUG	-
Modules/_xxtestfuzz/fuzzer.c	-	ast_literal_eval_method	-
Modules/_xxtestfuzz/fuzzer.c	-	compiled_patterns	-
Modules/_xxtestfuzz/fuzzer.c	-	csv_error	-
Modules/_xxtestfuzz/fuzzer.c	-	csv_module	-
Modules/_xxtestfuzz/fuzzer.c	-	json_loads_method	-
Modules/_xxtestfuzz/fuzzer.c	-	regex_patterns	-
Modules/_xxtestfuzz/fuzzer.c	-	sre_compile_method	-
Modules/_xxtestfuzz/fuzzer.c	-	sre_error_exception	-
Modules/_xxtestfuzz/fuzzer.c	-	struct_error	-
Modules/_xxtestfuzz/fuzzer.c	-	struct_unpack_method	-
Modules/_xxtestfuzz/fuzzer.c	LLVMFuzzerTestOneInput	CSV_READER_INITIALIZED	-
Modules/_xxtestfuzz/fuzzer.c	LLVMFuzzerTestOneInput	JSON_LOADS_INITIALIZED	-
Modules/_xxtestfuzz/fuzzer.c	LLVMFuzzerTestOneInput	SRE_COMPILE_INITIALIZED	-
Modules/_xxtestfuzz/fuzzer.c	LLVMFuzzerTestOneInput	SRE_MATCH_INITIALIZED	-
Modules/_xxtestfuzz/fuzzer.c	LLVMFuzzerTestOneInput	STRUCT_UNPACK_INITIALIZED	-
Modules/_xxtestfuzz/fuzzer.c	LLVMFuzzerTestOneInput	AST_LITERAL_EVAL_INITIALIZED	-


##################################
# should be const
# XXX Make them const.

# These are all variables that we will be leaving global.

#-----------------------
# keywords for PyArg_ParseTupleAndKeywords()
# "static char *name[]" -> "static const char * const name[]"

Modules/cjkcodecs/multibytecodec.c	-	incnewkwarglist	-
Modules/cjkcodecs/multibytecodec.c	-	streamkwarglist	-
Modules/_csv.c	-	dialect_kws	-
Modules/_datetimemodule.c	date_fromisocalendar	keywords	-
Modules/_datetimemodule.c	-	date_kws	-
Modules/_datetimemodule.c	date_strftime	keywords	-
Modules/_datetimemodule.c	datetime_astimezone	keywords	-
Modules/_datetimemodule.c	datetime_combine	keywords	-
Modules/_datetimemodule.c	datetime_fromtimestamp	keywords	-
Modules/_datetimemodule.c	datetime_isoformat	keywords	-
Modules/_datetimemodule.c	-	datetime_kws	-
Modules/_datetimemodule.c	delta_new	keywords	-
Modules/_datetimemodule.c	time_isoformat	keywords	-
Modules/_datetimemodule.c	-	time_kws	-
Modules/_datetimemodule.c	time_strftime	keywords	-
Modules/_datetimemodule.c	-	timezone_kws	-
Modules/_decimal/_decimal.c	context_init	kwlist	-
Modules/_decimal/_decimal.c	ctxmanager_new	kwlist	-
Modules/_decimal/_decimal.c	ctx_mpd_qpow	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_class	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_compare_total	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_compare_total_mag	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_isnormal	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_issubnormal	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qand	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qcompare	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qcompare_signal	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qcopy_sign	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qexp	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qfma	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qinvert	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qln	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qlog10	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qlogb	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qmax	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qmax_mag	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qmin	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qmin_mag	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qnext_minus	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qnext_plus	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qnext_toward	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qor	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qquantize	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qreduce	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qrem_near	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qrotate	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qscaleb	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qshift	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qsqrt	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_qxor	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_same_quantum	kwlist	-
Modules/_decimal/_decimal.c	dec_mpd_to_eng	kwlist	-
Modules/_decimal/_decimal.c	dec_new	kwlist	-
Modules/_decimal/_decimal.c	PyDec_ToIntegralExact	kwlist	-
Modules/_decimal/_decimal.c	PyDec_ToIntegralValue	kwlist	-
Modules/_elementtree.c	element_setstate_from_Python	kwlist	-
Modules/faulthandler.c	faulthandler_dump_traceback_later	kwlist	-
Modules/faulthandler.c	faulthandler_dump_traceback_py	kwlist	-
Modules/faulthandler.c	faulthandler_py_enable	kwlist	-
Modules/faulthandler.c	faulthandler_register_py	kwlist	-
Modules/_functoolsmodule.c	functools_cmp_to_key	kwargs	-
Modules/_functoolsmodule.c	keyobject_call	kwargs	-
Modules/_functoolsmodule.c	lru_cache_new	keywords	-
Modules/itertoolsmodule.c	repeat_new	kwargs	-
Modules/_json.c	encoder_call	kwlist	-
Modules/_json.c	encoder_new	kwlist	-
Modules/_json.c	scanner_call	kwlist	-
Modules/_json.c	scanner_new	kwlist	-
Modules/_lsprof.c	profiler_enable	kwlist	-
Modules/_lsprof.c	profiler_init	kwlist	-
Modules/_lzmamodule.c	Compressor_init	arg_names	-
Modules/_lzmamodule.c	parse_filter_spec_bcj	optnames	-
Modules/_lzmamodule.c	parse_filter_spec_delta	optnames	-
Modules/_lzmamodule.c	parse_filter_spec_lzma	optnames	-
Modules/mmapmodule.c	new_mmap_object	keywords	-
Modules/nismodule.c	nis_cat	kwlist	-
Modules/nismodule.c	nis_maps	kwlist	-
Modules/nismodule.c	nis_match	kwlist	-
Modules/signalmodule.c	signal_set_wakeup_fd	kwlist	-
Modules/socketmodule.c	sock_initobj	keywords	-
Modules/socketmodule.c	sock_recvfrom_into	kwlist	-
Modules/socketmodule.c	sock_recv_into	kwlist	-
Modules/socketmodule.c	sock_sendmsg_afalg	keywords	-
Modules/socketmodule.c	socket_getaddrinfo	kwnames	-
Modules/_sqlite/connection.c	pysqlite_connection_backup	keywords	-
Modules/_sqlite/connection.c	pysqlite_connection_create_aggregate	kwlist	-
Modules/_sqlite/connection.c	pysqlite_connection_create_function	kwlist	-
Modules/_sqlite/connection.c	pysqlite_connection_cursor	kwlist	-
Modules/_sqlite/connection.c	pysqlite_connection_init	kwlist	-
Modules/_sqlite/connection.c	pysqlite_connection_set_authorizer	kwlist	-
Modules/_sqlite/connection.c	pysqlite_connection_set_progress_handler	kwlist	-
Modules/_sqlite/connection.c	pysqlite_connection_set_trace_callback	kwlist	-
Modules/_sqlite/cursor.c	pysqlite_cursor_fetchmany	kwlist	-
Modules/_sqlite/module.c	module_complete	kwlist	-
Modules/_sqlite/module.c	module_connect	kwlist	-
Modules/_sqlite/module.c	module_enable_shared_cache	kwlist	-
Modules/syslogmodule.c	syslog_openlog	keywords	-
Modules/_xxsubinterpretersmodule.c	channel_close	kwlist	-
Modules/_xxsubinterpretersmodule.c	channel_destroy	kwlist	-
Modules/_xxsubinterpretersmodule.c	channelid_new	kwlist	-
Modules/_xxsubinterpretersmodule.c	channel_list_interpreters	kwlist	-
Modules/_xxsubinterpretersmodule.c	channel_recv	kwlist	-
Modules/_xxsubinterpretersmodule.c	channel_release	kwlist	-
Modules/_xxsubinterpretersmodule.c	channel_send	kwlist	-
Modules/_xxsubinterpretersmodule.c	interp_create	kwlist	-
Modules/_xxsubinterpretersmodule.c	interp_destroy	kwlist	-
Modules/_xxsubinterpretersmodule.c	interp_is_running	kwlist	-
Modules/_xxsubinterpretersmodule.c	interp_run_string	kwlist	-
Modules/_xxsubinterpretersmodule.c	object_is_shareable	kwlist	-
Modules/_zoneinfo.c	zoneinfo_clear_cache	kwlist	-
Modules/_zoneinfo.c	zoneinfo_from_file	kwlist	-
Modules/_zoneinfo.c	zoneinfo_new	kwlist	-
Modules/_zoneinfo.c	zoneinfo_no_cache	kwlist	-
Objects/exceptions.c	ImportError_init	kwlist	-
Objects/interpreteridobject.c	interpid_new	kwlist	-
Objects/weakrefobject.c	weakref_call	kwlist	-
Objects/exceptions.c	NameError_init	kwlist	-
Objects/exceptions.c	AttributeError_init	kwlist	-
Python/_warnings.c	warnings_warn_explicit	kwd_list	-
Python/bltinmodule.c	builtin___import__	kwlist	-
Python/bltinmodule.c	min_max	kwlist	-
Python/bltinmodule.c	zip_new	kwlist	-
Python/context.c	contextvar_tp_new	kwlist	-
Python/sysmodule.c	sys_getsizeof	kwlist	-
Python/sysmodule.c	sys_set_asyncgen_hooks	keywords	-

#-----------------------
# PyModuleDef

Modules/_multiprocessing/posixshmem.c	-	_posixshmemmodule	-
Modules/_sqlite/module.h	-	_sqlite3module	-
Modules/_ssl.c	-	_sslmodule_def	-
Modules/_ssl.h	-	_sslmodule_def	-
Modules/_testmultiphase.c	-	def_module_state_shared	-
Modules/_threadmodule.c	-	thread_module	-
Modules/_typingmodule.c	-	typingmodule	-
Modules/signalmodule.c	-	signal_module	-
Modules/xxlimited_35.c	-	xxmodule	-
Python/Python-ast.c	-	_astmodule	-
Python/Python-tokenize.c	-	_tokenizemodule	-
Python/_warnings.c	-	warnings_module	-
Python/bltinmodule.c	-	builtinsmodule	-
Python/import.c	-	imp_module	-
Python/marshal.c	-	marshalmodule	-
Python/sysmodule.c	-	sysmodule	-

#-----------------------
# PyModuleDef_Slot

Modules/_abc.c	-	_abcmodule_slots	-
Modules/_blake2/blake2module.c	-	_blake2_slots	-
Modules/_bz2module.c	-	_bz2_slots	-
Modules/_codecsmodule.c	-	_codecs_slots	-
Modules/_collectionsmodule.c	-	collections_slots	-
Modules/_contextvarsmodule.c	-	_contextvars_slots	-
Modules/_cryptmodule.c	-	_crypt_slots	-
Modules/_csv.c	-	csv_slots	-
Modules/_curses_panel.c	-	_curses_slots	-
Modules/_dbmmodule.c	-	_dbmmodule_slots	-
Modules/_functoolsmodule.c	-	_functools_slots	-
Modules/_gdbmmodule.c	-	_gdbm_module_slots	-
Modules/_hashopenssl.c	-	hashlib_slots	-
Modules/_heapqmodule.c	-	heapq_slots	-
Modules/_json.c	-	_json_slots	-
Modules/_localemodule.c	-	_locale_slots	-
Modules/_lsprof.c	-	_lsprofslots	-
Modules/_lzmamodule.c	-	lzma_slots	-
Modules/_multiprocessing/multiprocessing.c	-	multiprocessing_slots	-
Modules/_operator.c	-	operator_slots	-
Modules/_posixsubprocess.c	-	_posixsubprocess_slots	-
Modules/_queuemodule.c	-	queuemodule_slots	-
Modules/_randommodule.c	-	_random_slots	-
Modules/_scproxy.c	-	_scproxy_slots	-
Modules/_sha3/sha3module.c	-	_sha3_slots	-
Modules/_sqlite/module.c	-	module_slots	-
Modules/_sre.c	-	sre_slots	-
Modules/_ssl.c	-	sslmodule_slots	-
Modules/_stat.c	-	stat_slots	-
Modules/_statisticsmodule.c	-	_statisticsmodule_slots	-
Modules/_struct.c	-	_structmodule_slots	-
Modules/_threadmodule.c	-	thread_module_slots	-
Modules/_typingmodule.c	-	_typingmodule_slots	-
Modules/_uuidmodule.c	-	uuid_slots	-
Modules/_weakref.c	-	weakref_slots	-
Modules/_winapi.c	-	winapi_slots	-
Modules/_zoneinfo.c	-	zoneinfomodule_slots	-
Modules/arraymodule.c	-	arrayslots	-
Modules/atexitmodule.c	-	atexit_slots	-
Modules/audioop.c	-	audioop_slots	-
Modules/binascii.c	-	binascii_slots	-
Modules/cjkcodecs/cjkcodecs.h	-	_cjk_slots	-
Modules/cjkcodecs/multibytecodec.c	-	_multibytecodec_slots	-
Modules/cmathmodule.c	-	cmath_slots	-
Modules/errnomodule.c	-	errno_slots	-
Modules/faulthandler.c	-	faulthandler_slots	-
Modules/fcntlmodule.c	-	fcntl_slots	-
Modules/gcmodule.c	-	gcmodule_slots	-
Modules/gcmodule.c	-	gcmodule_slots	-
Modules/grpmodule.c	-	grpmodule_slots	-
Modules/itertoolsmodule.c	-	itertoolsmodule_slots	-
Modules/mathmodule.c	-	math_slots	-
Modules/md5module.c	-	_md5_slots	-
Modules/mmapmodule.c	-	mmap_slots	-
Modules/nismodule.c	-	nis_slots	-
Modules/overlapped.c	-	overlapped_slots	-
Modules/posixmodule.c	-	posixmodile_slots	-
Modules/pwdmodule.c	-	pwdmodule_slots	-
Modules/pyexpat.c	-	pyexpat_slots	-
Modules/resource.c	-	resource_slots	-
Modules/selectmodule.c	-	_select_slots	-
Modules/sha1module.c	-	_sha1_slots	-
Modules/sha256module.c	-	_sha256_slots	-
Modules/sha512module.c	-	_sha512_slots	-
Modules/signalmodule.c	-	signal_slots	-
Modules/spwdmodule.c	-	spwdmodule_slots	-
Modules/symtablemodule.c	-	symtable_slots	-
Modules/syslogmodule.c	-	syslog_slots	-
Modules/termios.c	-	termios_slots	-
Modules/timemodule.c	-	time_slots	-
Modules/unicodedata.c	-	unicodedata_slots	-
Modules/xxlimited.c	-	xx_slots	-
Modules/xxlimited_35.c	-	xx_slots	-
Modules/xxmodule.c	-	xx_slots	-
Modules/xxsubtype.c	-	xxsubtype_slots	-
Modules/zlibmodule.c	-	zlib_slots	-
Python/Python-ast.c	-	astmodule_slots	-
Python/Python-tokenize.c	-	tokenizemodule_slots	-
Python/_warnings.c	-	warnings_slots	-
Python/marshal.c	-	marshalmodule_slots	-

#-----------------------
# PyMethodDef and PyMethodDef[], for static types and modules

Modules/_abc.c	-	_abcmodule_methods	-
Modules/_abc.c	-	_destroy_def	-
Modules/_asynciomodule.c	-	FutureIter_methods	-
Modules/_asynciomodule.c	-	FutureType_methods	-
Modules/_asynciomodule.c	-	TaskType_methods	-
Modules/_asynciomodule.c	-	TaskWakeupDef	-
Modules/_asynciomodule.c	-	asyncio_methods	-
Modules/_bisectmodule.c	-	bisect_methods	-
Modules/_blake2/blake2b_impl.c	-	py_blake2b_methods	-
Modules/_blake2/blake2module.c	-	blake2mod_functions	-
Modules/_blake2/blake2s_impl.c	-	py_blake2s_methods	-
Modules/_bz2module.c	-	BZ2Compressor_methods	-
Modules/_bz2module.c	-	BZ2Decompressor_methods	-
Modules/_codecsmodule.c	-	_codecs_functions	-
Modules/_collectionsmodule.c	-	collections_methods	-
Modules/_collectionsmodule.c	-	defdict_methods	-
Modules/_collectionsmodule.c	-	deque_methods	-
Modules/_collectionsmodule.c	-	dequeiter_methods	-
Modules/_collectionsmodule.c	-	tuplegetter_methods	-
Modules/_contextvarsmodule.c	-	_contextvars_methods	-
Modules/_cryptmodule.c	-	crypt_methods	-
Modules/_csv.c	-	Reader_methods	-
Modules/_csv.c	-	Writer_methods	-
Modules/_csv.c	-	csv_methods	-
Modules/_csv.c	-	dialect_methods	-
Modules/_ctypes/_ctypes.c	-	Array_methods	-
Modules/_ctypes/_ctypes.c	-	CDataType_methods	-
Modules/_ctypes/_ctypes.c	-	PyCData_methods	-
Modules/_ctypes/_ctypes.c	-	PyCPointerType_methods	-
Modules/_ctypes/_ctypes.c	-	PyCSimpleType_methods	-
Modules/_ctypes/_ctypes.c	-	Simple_methods	-
Modules/_ctypes/_ctypes.c	-	c_char_p_method	-
Modules/_ctypes/_ctypes.c	-	c_void_p_method	-
Modules/_ctypes/_ctypes.c	-	c_wchar_p_method	-
Modules/_ctypes/callproc.c	-	_ctypes_module_methods	-
Modules/_ctypes/stgdict.c	-	PyCStgDict_methods	-
Modules/_curses_panel.c	-	PyCursesPanel_Methods	-
Modules/_curses_panel.c	-	PyCurses_methods	-
Modules/_cursesmodule.c	-	PyCursesWindow_Methods	-
Modules/_cursesmodule.c	-	PyCurses_methods	-
Modules/_datetimemodule.c	-	date_methods	-
Modules/_datetimemodule.c	-	datetime_methods	-
Modules/_datetimemodule.c	-	delta_methods	-
Modules/_datetimemodule.c	-	iso_calendar_date_methods	-
Modules/_datetimemodule.c	-	module_methods	-
Modules/_datetimemodule.c	-	time_methods	-
Modules/_datetimemodule.c	-	timezone_methods	-
Modules/_datetimemodule.c	-	tzinfo_methods	-
Modules/_dbmmodule.c	-	dbm_methods	-
Modules/_dbmmodule.c	-	dbmmodule_methods	-
Modules/_decimal/_decimal.c	-	_decimal_methods	-
Modules/_decimal/_decimal.c	-	context_methods	-
Modules/_decimal/_decimal.c	-	ctxmanager_methods	-
Modules/_decimal/_decimal.c	-	dec_methods	-
Modules/_decimal/_decimal.c	-	signaldict_methods	-
Modules/_elementtree.c	-	_functions	-
Modules/_elementtree.c	-	element_methods	-
Modules/_elementtree.c	-	treebuilder_methods	-
Modules/_elementtree.c	-	xmlparser_methods	-
Modules/_functoolsmodule.c	-	_functools_methods	-
Modules/_functoolsmodule.c	-	lru_cache_methods	-
Modules/_functoolsmodule.c	-	partial_methods	-
Modules/_gdbmmodule.c	-	_gdbm_module_methods	-
Modules/_gdbmmodule.c	-	gdbm_methods	-
Modules/_hashopenssl.c	-	EVPXOF_methods	-
Modules/_hashopenssl.c	-	EVP_functions	-
Modules/_hashopenssl.c	-	EVP_methods	-
Modules/_hashopenssl.c	-	HMAC_methods	-
Modules/_heapqmodule.c	-	heapq_methods	-
Modules/_io/_iomodule.c	-	module_methods	-
Modules/_io/bufferedio.c	-	bufferediobase_methods	-
Modules/_io/bufferedio.c	-	bufferedrandom_methods	-
Modules/_io/bufferedio.c	-	bufferedreader_methods	-
Modules/_io/bufferedio.c	-	bufferedrwpair_methods	-
Modules/_io/bufferedio.c	-	bufferedwriter_methods	-
Modules/_io/bytesio.c	-	bytesio_methods	-
Modules/_io/fileio.c	-	fileio_methods	-
Modules/_io/iobase.c	-	iobase_methods	-
Modules/_io/iobase.c	-	rawiobase_methods	-
Modules/_io/stringio.c	-	stringio_methods	-
Modules/_io/textio.c	-	incrementalnewlinedecoder_methods	-
Modules/_io/textio.c	-	textiobase_methods	-
Modules/_io/textio.c	-	textiowrapper_methods	-
Modules/_io/winconsoleio.c	-	winconsoleio_methods	-
Modules/_json.c	-	speedups_methods	-
Modules/_localemodule.c	-	PyLocale_Methods	-
Modules/_lsprof.c	-	moduleMethods	-
Modules/_lsprof.c	-	profiler_methods	-
Modules/_lzmamodule.c	-	Compressor_methods	-
Modules/_lzmamodule.c	-	Decompressor_methods	-
Modules/_lzmamodule.c	-	lzma_methods	-
Modules/_multiprocessing/multiprocessing.c	-	module_methods	-
Modules/_multiprocessing/posixshmem.c	-	module_methods	-
Modules/_multiprocessing/semaphore.c	-	semlock_methods	-
Modules/_opcode.c	-	opcode_functions	-
Modules/_operator.c	-	attrgetter_methods	-
Modules/_operator.c	-	itemgetter_methods	-
Modules/_operator.c	-	methodcaller_methods	-
Modules/_operator.c	-	operator_methods	-
Modules/_pickle.c	-	Pickler_methods	-
Modules/_pickle.c	-	Unpickler_methods	-
Modules/_pickle.c	-	pickle_methods	-
Modules/_pickle.c	-	picklerproxy_methods	-
Modules/_pickle.c	-	unpicklerproxy_methods	-
Modules/_posixsubprocess.c	-	module_methods	-
Modules/_queuemodule.c	-	simplequeue_methods	-
Modules/_randommodule.c	-	random_methods	-
Modules/_scproxy.c	-	mod_methods	-
Modules/_sha3/sha3module.c	-	SHA3_methods	-
Modules/_sha3/sha3module.c	-	SHAKE_methods	-
Modules/_sqlite/connection.c	-	connection_methods	-
Modules/_sqlite/cursor.c	-	cursor_methods	-
Modules/_sqlite/module.c	-	module_methods	-
Modules/_sqlite/row.c	-	row_methods	-
Modules/_sre.c	-	_functions	-
Modules/_sre.c	-	match_methods	-
Modules/_sre.c	-	pattern_methods	-
Modules/_sre.c	-	scanner_methods	-
Modules/_ssl.c	-	PySSLMethods	-
Modules/_ssl.c	-	PySSL_methods	-
Modules/_ssl.c	-	context_methods	-
Modules/_ssl.c	-	memory_bio_methods	-
Modules/_ssl/cert.c	-	certificate_methods	-
Modules/_stat.c	-	stat_methods	-
Modules/_statisticsmodule.c	-	statistics_methods	-
Modules/_struct.c	-	module_functions	-
Modules/_struct.c	-	s_methods	-
Modules/_struct.c	-	unpackiter_methods	-
Modules/_threadmodule.c	-	lock_methods	-
Modules/_threadmodule.c	-	rlock_methods	-
Modules/_threadmodule.c	-	thread_methods	-
Modules/_threadmodule.c	local_new	wr_callback_def	-
Modules/_tkinter.c	-	Tkapp_methods	-
Modules/_tkinter.c	-	Tktt_methods	-
Modules/_tkinter.c	-	moduleMethods	-
Modules/_tracemalloc.c	-	module_methods	-
Modules/_typingmodule.c	-	typing_methods	-
Modules/_uuidmodule.c	-	uuid_methods	-
Modules/_weakref.c	-	weakref_functions	-
Modules/_winapi.c	-	overlapped_methods	-
Modules/_winapi.c	-	winapi_functions	-
Modules/_xxsubinterpretersmodule.c	-	module_functions	-
Modules/_zoneinfo.c	-	module_methods	-
Modules/_zoneinfo.c	-	zoneinfo_methods	-
Modules/arraymodule.c	-	a_methods	-
Modules/arraymodule.c	-	array_methods	-
Modules/arraymodule.c	-	arrayiter_methods	-
Modules/atexitmodule.c	-	atexit_methods	-
Modules/audioop.c	-	audioop_methods	-
Modules/binascii.c	-	binascii_module_methods	-
Modules/cjkcodecs/cjkcodecs.h	-	__methods	-
Modules/cjkcodecs/cjkcodecs.h	-	_cjk_methods	-
Modules/cjkcodecs/multibytecodec.c	-	__methods	-
Modules/cjkcodecs/multibytecodec.c	-	_multibytecodec_methods	-
Modules/cjkcodecs/multibytecodec.c	-	mbidecoder_methods	-
Modules/cjkcodecs/multibytecodec.c	-	mbiencoder_methods	-
Modules/cjkcodecs/multibytecodec.c	-	mbstreamreader_methods	-
Modules/cjkcodecs/multibytecodec.c	-	mbstreamwriter_methods	-
Modules/cjkcodecs/multibytecodec.c	-	multibytecodec_methods	-
Modules/cmathmodule.c	-	cmath_methods	-
Modules/errnomodule.c	-	errno_methods	-
Modules/faulthandler.c	-	module_methods	-
Modules/fcntlmodule.c	-	fcntl_methods	-
Modules/gcmodule.c	-	GcMethods	-
Modules/getpath.c	-	getpath_methods	-
Modules/getpath.c	-	getpath_nowarn_method	-
Modules/getpath.c	-	getpath_warn_method	-
Modules/grpmodule.c	-	grp_methods	-
Modules/itertoolsmodule.c	-	_grouper_methods	-
Modules/itertoolsmodule.c	-	accumulate_methods	-
Modules/itertoolsmodule.c	-	chain_methods	-
Modules/itertoolsmodule.c	-	combinations_methods	-
Modules/itertoolsmodule.c	-	compress_methods	-
Modules/itertoolsmodule.c	-	count_methods	-
Modules/itertoolsmodule.c	-	cwr_methods	-
Modules/itertoolsmodule.c	-	cycle_methods	-
Modules/itertoolsmodule.c	-	dropwhile_methods	-
Modules/itertoolsmodule.c	-	filterfalse_methods	-
Modules/itertoolsmodule.c	-	groupby_methods	-
Modules/itertoolsmodule.c	-	islice_methods	-
Modules/itertoolsmodule.c	-	module_methods	-
Modules/itertoolsmodule.c	-	permuations_methods	-
Modules/itertoolsmodule.c	-	product_methods	-
Modules/itertoolsmodule.c	-	repeat_methods	-
Modules/itertoolsmodule.c	-	starmap_methods	-
Modules/itertoolsmodule.c	-	takewhile_reduce_methods	-
Modules/itertoolsmodule.c	-	tee_methods	-
Modules/itertoolsmodule.c	-	teedataobject_methods	-
Modules/itertoolsmodule.c	-	zip_longest_methods	-
Modules/mathmodule.c	-	math_methods	-
Modules/md5module.c	-	MD5_functions	-
Modules/md5module.c	-	MD5_methods	-
Modules/mmapmodule.c	-	mmap_object_methods	-
Modules/nismodule.c	-	nis_methods	-
Modules/ossaudiodev.c	-	oss_methods	-
Modules/ossaudiodev.c	-	oss_mixer_methods	-
Modules/ossaudiodev.c	-	ossaudiodev_methods	-
Modules/overlapped.c	-	Overlapped_methods	-
Modules/overlapped.c	-	overlapped_functions	-
Modules/posixmodule.c	-	DirEntry_methods	-
Modules/posixmodule.c	-	ScandirIterator_methods	-
Modules/posixmodule.c	-	posix_methods	-
Modules/pwdmodule.c	-	pwd_methods	-
Modules/pyexpat.c	-	pyexpat_methods	-
Modules/pyexpat.c	-	xmlparse_methods	-
Modules/readline.c	-	readline_methods	-
Modules/resource.c	-	resource_methods	-
Modules/selectmodule.c	-	devpoll_methods	-
Modules/selectmodule.c	-	kqueue_queue_methods	-
Modules/selectmodule.c	-	poll_methods	-
Modules/selectmodule.c	-	pyepoll_methods	-
Modules/selectmodule.c	-	select_methods	-
Modules/sha1module.c	-	SHA1_functions	-
Modules/sha1module.c	-	SHA1_methods	-
Modules/sha256module.c	-	SHA_functions	-
Modules/sha256module.c	-	SHA_methods	-
Modules/sha512module.c	-	SHA_functions	-
Modules/sha512module.c	-	SHA_methods	-
Modules/signalmodule.c	-	signal_methods	-
Modules/socketmodule.c	-	sock_methods	-
Modules/socketmodule.c	-	socket_methods	-
Modules/spwdmodule.c	-	spwd_methods	-
Modules/symtablemodule.c	-	symtable_methods	-
Modules/syslogmodule.c	-	syslog_methods	-
Modules/termios.c	-	termios_methods	-
Modules/timemodule.c	-	time_methods	-
Modules/unicodedata.c	-	unicodedata_functions	-
Modules/xxlimited.c	-	Xxo_methods	-
Modules/xxlimited.c	-	xx_methods	-
Modules/xxlimited_35.c	-	Xxo_methods	-
Modules/xxlimited_35.c	-	xx_methods	-
Modules/xxmodule.c	-	Xxo_methods	-
Modules/xxmodule.c	-	xx_methods	-
Modules/xxsubtype.c	-	spamdict_methods	-
Modules/xxsubtype.c	-	spamlist_methods	-
Modules/xxsubtype.c	-	xxsubtype_functions	-
Modules/zlibmodule.c	-	Decomp_methods	-
Modules/zlibmodule.c	-	comp_methods	-
Modules/zlibmodule.c	-	zlib_methods	-
Objects/bytearrayobject.c	-	bytearray_methods	-
Objects/bytearrayobject.c	-	bytearrayiter_methods	-
Objects/bytesobject.c	-	bytes_methods	-
Objects/bytesobject.c	-	striter_methods	-
Objects/classobject.c	-	method_methods	-
Objects/codeobject.c	-	code_methods	-
Objects/complexobject.c	-	complex_methods	-
Objects/descrobject.c	-	descr_methods	-
Objects/descrobject.c	-	mappingproxy_methods	-
Objects/descrobject.c	-	property_methods	-
Objects/descrobject.c	-	wrapper_methods	-
Objects/dictobject.c	-	dictitems_methods	-
Objects/dictobject.c	-	dictiter_methods	-
Objects/dictobject.c	-	dictkeys_methods	-
Objects/dictobject.c	-	dictvalues_methods	-
Objects/dictobject.c	-	mapp_methods	-
Objects/enumobject.c	-	enum_methods	-
Objects/enumobject.c	-	reversediter_methods	-
Objects/exceptions.c	-	AttributeError_methods	-
Objects/exceptions.c	-	BaseExceptionGroup_methods	-
Objects/exceptions.c	-	BaseException_methods	-
Objects/exceptions.c	-	ImportError_methods	-
Objects/exceptions.c	-	NameError_methods	-
Objects/exceptions.c	-	OSError_methods	-
Objects/fileobject.c	-	stdprinter_methods	-
Objects/floatobject.c	-	float_methods	-
Objects/frameobject.c	-	frame_methods	-
Objects/genericaliasobject.c	-	ga_methods	-
Objects/genobject.c	-	async_gen_asend_methods	-
Objects/genobject.c	-	async_gen_athrow_methods	-
Objects/genobject.c	-	async_gen_methods	-
Objects/genobject.c	-	coro_methods	-
Objects/genobject.c	-	coro_wrapper_methods	-
Objects/genobject.c	-	gen_methods	-
Objects/iterobject.c	-	anextawaitable_methods	-
Objects/iterobject.c	-	calliter_methods	-
Objects/iterobject.c	-	seqiter_methods	-
Objects/listobject.c	-	list_methods	-
Objects/listobject.c	-	listiter_methods	-
Objects/listobject.c	-	listreviter_methods	-
Objects/longobject.c	-	long_methods	-
Objects/memoryobject.c	-	memory_methods	-
Objects/methodobject.c	-	meth_methods	-
Objects/moduleobject.c	-	module_methods	-
Objects/namespaceobject.c	-	namespace_methods	-
Objects/object.c	-	notimplemented_methods	-
Objects/odictobject.c	-	odict_methods	-
Objects/odictobject.c	-	odictitems_methods	-
Objects/odictobject.c	-	odictiter_methods	-
Objects/odictobject.c	-	odictkeys_methods	-
Objects/odictobject.c	-	odictvalues_methods	-
Objects/picklebufobject.c	-	picklebuf_methods	-
Objects/rangeobject.c	-	longrangeiter_methods	-
Objects/rangeobject.c	-	range_methods	-
Objects/rangeobject.c	-	rangeiter_methods	-
Objects/setobject.c	-	frozenset_methods	-
Objects/setobject.c	-	set_methods	-
Objects/setobject.c	-	setiter_methods	-
Objects/sliceobject.c	-	ellipsis_methods	-
Objects/sliceobject.c	-	slice_methods	-
Objects/stringlib/unicode_format.h	-	fieldnameiter_methods	-
Objects/stringlib/unicode_format.h	-	formatteriter_methods	-
Objects/structseq.c	-	structseq_methods	-
Objects/tupleobject.c	-	tuple_methods	-
Objects/tupleobject.c	-	tupleiter_methods	-
Objects/typeobject.c	-	object_methods	-
Objects/typeobject.c	-	tp_new_methoddef	-
Objects/typeobject.c	-	type_methods	-
Objects/unicodeobject.c	-	_string_methods	-
Objects/unicodeobject.c	-	encoding_map_methods	-
Objects/unicodeobject.c	-	unicode_methods	-
Objects/unicodeobject.c	-	unicodeiter_methods	-
Objects/unionobject.c	-	union_methods	-
Objects/weakrefobject.c	-	proxy_methods	-
Objects/weakrefobject.c	-	weakref_methods	-
Python/Python-ast.c	-	ast_type_methods	-
Python/Python-tokenize.c	-	tokenize_methods	-
Python/_warnings.c	-	warnings_functions	-
Python/bltinmodule.c	-	builtin_methods	-
Python/bltinmodule.c	-	filter_methods	-
Python/bltinmodule.c	-	map_methods	-
Python/bltinmodule.c	-	zip_methods	-
Python/context.c	-	PyContextTokenType_methods	-
Python/context.c	-	PyContextVar_methods	-
Python/context.c	-	PyContext_methods	-
Python/hamt.c	-	PyHamt_methods	-
Python/import.c	-	imp_slots	-
Python/import.c	-	imp_methods	-
Python/marshal.c	-	marshal_methods	-
Python/sysmodule.c	-	sys_methods	-
Python/traceback.c	-	tb_methods	-

#-----------------------
# PyMemberDef[], for static types and strucseq

Modules/_bz2module.c	-	BZ2Decompressor_members	-
Modules/_collectionsmodule.c	-	defdict_members	-
Modules/_collectionsmodule.c	-	tuplegetter_members	-
Modules/_csv.c	-	Dialect_memberlist	-
Modules/_csv.c	-	Reader_memberlist	-
Modules/_csv.c	-	Writer_memberlist	-
Modules/_ctypes/_ctypes.c	-	PyCData_members	-
Modules/_ctypes/callproc.c	-	PyCArgType_members	-
Modules/_datetimemodule.c	-	delta_members	-
Modules/_elementtree.c	-	xmlparser_members	-
Modules/_functoolsmodule.c	-	keyobject_members	-
Modules/_functoolsmodule.c	-	lru_cache_memberlist	-
Modules/_functoolsmodule.c	-	partial_memberlist	-
Modules/_io/bufferedio.c	-	bufferedrandom_members	-
Modules/_io/bufferedio.c	-	bufferedreader_members	-
Modules/_io/bufferedio.c	-	bufferedwriter_members	-
Modules/_io/fileio.c	-	fileio_members	-
Modules/_io/textio.c	-	textiowrapper_members	-
Modules/_io/winconsoleio.c	-	winconsoleio_members	-
Modules/_json.c	-	encoder_members	-
Modules/_json.c	-	scanner_members	-
Modules/_lzmamodule.c	-	Decompressor_members	-
Modules/_multiprocessing/semaphore.c	-	semlock_members	-
Modules/_pickle.c	-	Pickler_members	-
Modules/_queuemodule.c	-	simplequeue_members	-
Modules/_sqlite/connection.c	-	connection_members	-
Modules/_sqlite/cursor.c	-	cursor_members	-
Modules/_sqlite/statement.c	-	stmt_members	-
Modules/_sre.c	-	match_members	-
Modules/_sre.c	-	pattern_members	-
Modules/_sre.c	-	scanner_members	-
Modules/_struct.c	-	s_members	-
Modules/_threadmodule.c	-	local_dummy_type_members	-
Modules/_threadmodule.c	-	local_type_members	-
Modules/_threadmodule.c	-	lock_type_members	-
Modules/_threadmodule.c	-	rlock_type_members	-
Modules/_winapi.c	-	overlapped_members	-
Modules/_zoneinfo.c	-	zoneinfo_members	-
Modules/arraymodule.c	-	array_members	-
Modules/cjkcodecs/multibytecodec.c	-	mbstreamreader_members	-
Modules/cjkcodecs/multibytecodec.c	-	mbstreamwriter_members	-
Modules/mmapmodule.c	-	mmap_object_members	-
Modules/ossaudiodev.c	-	oss_members	-
Modules/overlapped.c	-	Overlapped_members	-
Modules/posixmodule.c	-	DirEntry_members	-
Modules/pyexpat.c	-	xmlparse_members	-
Modules/selectmodule.c	-	kqueue_event_members	-
Modules/sha256module.c	-	SHA_members	-
Modules/sha512module.c	-	SHA_members	-
Modules/socketmodule.c	-	sock_memberlist	-
Modules/unicodedata.c	-	DB_members	-
Modules/xxsubtype.c	-	spamdict_members	-
Modules/zlibmodule.c	-	Decomp_members	-
Objects/classobject.c	-	instancemethod_memberlist	-
Objects/classobject.c	-	method_memberlist	-
Objects/codeobject.c	-	code_memberlist	-
Objects/complexobject.c	-	complex_members	-
Objects/descrobject.c	-	descr_members	-
Objects/descrobject.c	-	property_members	-
Objects/descrobject.c	-	wrapper_members	-
Objects/exceptions.c	-	AttributeError_members	-
Objects/exceptions.c	-	BaseExceptionGroup_members	-
Objects/exceptions.c	-	BaseException_members	-
Objects/exceptions.c	-	ImportError_members	-
Objects/exceptions.c	-	NameError_members	-
Objects/exceptions.c	-	OSError_members	-
Objects/exceptions.c	-	StopIteration_members	-
Objects/exceptions.c	-	SyntaxError_members	-
Objects/exceptions.c	-	SystemExit_members	-
Objects/exceptions.c	-	UnicodeError_members	-
Objects/frameobject.c	-	frame_memberlist	-
Objects/funcobject.c	-	cm_memberlist	-
Objects/funcobject.c	-	func_memberlist	-
Objects/funcobject.c	-	sm_memberlist	-
Objects/genericaliasobject.c	-	ga_members	-
Objects/genobject.c	-	async_gen_memberlist	-
Objects/genobject.c	-	coro_memberlist	-
Objects/genobject.c	-	gen_memberlist	-
Objects/methodobject.c	-	meth_members	-
Objects/moduleobject.c	-	module_members	-
Objects/namespaceobject.c	-	namespace_members	-
Objects/rangeobject.c	-	range_members	-
Objects/sliceobject.c	-	slice_members	-
Objects/typeobject.c	-	super_members	-
Objects/typeobject.c	-	type_members	-
Objects/unionobject.c	-	union_members	-
Objects/weakrefobject.c	-	weakref_members	-
Python/Python-ast.c	-	ast_type_members	-
Python/context.c	-	PyContextVar_members	-
Python/symtable.c	-	ste_memberlist	-
Python/traceback.c	-	tb_memberlist	-

#-----------------------
# for static types

# PyNumberMethods
Modules/_collectionsmodule.c	-	deque_as_number	-
Modules/_collectionsmodule.c	-	defdict_as_number	-
Modules/_ctypes/_ctypes.c	-	PyCFuncPtr_as_number	-
Modules/_ctypes/_ctypes.c	-	Simple_as_number	-
Modules/_ctypes/_ctypes.c	-	Pointer_as_number	-
Modules/_datetimemodule.c	-	delta_as_number	-
Modules/_datetimemodule.c	-	date_as_number	-
Modules/_datetimemodule.c	-	datetime_as_number	-
Modules/_decimal/_decimal.c	-	dec_number_methods	-
Modules/_xxsubinterpretersmodule.c	-	channelid_as_number	-
Objects/boolobject.c	-	bool_as_number	-
Objects/bytearrayobject.c	-	bytearray_as_number	-
Objects/bytesobject.c	-	bytes_as_number	-
Objects/complexobject.c	-	complex_as_number	-
Objects/descrobject.c	-	mappingproxy_as_number	-
Objects/dictobject.c	-	dict_as_number	-
Objects/dictobject.c	-	dictviews_as_number	-
Objects/floatobject.c	-	float_as_number	-
Objects/genericaliasobject.c	-	ga_as_number	-
Objects/interpreteridobject.c	-	interpid_as_number	-
Objects/longobject.c	-	long_as_number	-
Objects/object.c	-	none_as_number	-
Objects/object.c	-	notimplemented_as_number	-
Objects/odictobject.c	-	odict_as_number	-
Objects/rangeobject.c	-	range_as_number	-
Objects/setobject.c	-	set_as_number	-
Objects/setobject.c	-	frozenset_as_number	-
Objects/typeobject.c	-	type_as_number	-
Objects/unicodeobject.c	-	unicode_as_number	-
Objects/unionobject.c	-	union_as_number	-
Objects/weakrefobject.c	-	proxy_as_number	-

# PySequenceMethods
Modules/arraymodule.c	-	array_as_sequence	-
Modules/_collectionsmodule.c	-	deque_as_sequence	-
Modules/_ctypes/_ctypes.c	-	CDataType_as_sequence	-
Modules/_ctypes/_ctypes.c	-	Array_as_sequence	-
Modules/_ctypes/_ctypes.c	-	Pointer_as_sequence	-
Modules/_elementtree.c	-	element_as_sequence	-
Modules/mmapmodule.c	-	mmap_as_sequence	-
Objects/bytearrayobject.c	-	bytearray_as_sequence	-
Objects/bytesobject.c	-	bytes_as_sequence	-
Objects/descrobject.c	-	mappingproxy_as_sequence	-
Objects/dictobject.c	-	dict_as_sequence	-
Objects/dictobject.c	-	dictkeys_as_sequence	-
Objects/dictobject.c	-	dictitems_as_sequence	-
Objects/dictobject.c	-	dictvalues_as_sequence	-
Objects/listobject.c	-	list_as_sequence	-
Objects/memoryobject.c	-	memory_as_sequence	-
Objects/rangeobject.c	-	range_as_sequence	-
Objects/setobject.c	-	set_as_sequence	-
Objects/tupleobject.c	-	tuple_as_sequence	-
Objects/unicodeobject.c	-	unicode_as_sequence	-
Objects/weakrefobject.c	-	proxy_as_sequence	-
Python/context.c	-	PyContext_as_sequence	-
Python/hamt.c	-	PyHamt_as_sequence	-

# PyMappingMethods
Modules/arraymodule.c	-	array_as_mapping	-
Modules/_ctypes/_ctypes.c	-	Array_as_mapping	-
Modules/_ctypes/_ctypes.c	-	Pointer_as_mapping	-
Modules/_decimal/_decimal.c	-	signaldict_as_mapping	-
Modules/_elementtree.c	-	element_as_mapping	-
Modules/mmapmodule.c	-	mmap_as_mapping	-
Modules/_sre.c	-	match_as_mapping	-
Objects/bytearrayobject.c	-	bytearray_as_mapping	-
Objects/bytesobject.c	-	bytes_as_mapping	-
Objects/descrobject.c	-	mappingproxy_as_mapping	-
Objects/dictobject.c	-	dict_as_mapping	-
Objects/genericaliasobject.c	-	ga_as_mapping	-
Objects/listobject.c	-	list_as_mapping	-
Objects/memoryobject.c	-	memory_as_mapping	-
Objects/odictobject.c	-	odict_as_mapping	-
Objects/rangeobject.c	-	range_as_mapping	-
Objects/tupleobject.c	-	tuple_as_mapping	-
Objects/unicodeobject.c	-	unicode_as_mapping	-
Objects/unionobject.c	-	union_as_mapping	-
Objects/weakrefobject.c	-	proxy_as_mapping	-
Python/context.c	-	PyContext_as_mapping	-
Python/hamt.c	-	PyHamtIterator_as_mapping	-
Python/hamt.c	-	PyHamt_as_mapping	-

# PyAsyncMethods
Modules/_asynciomodule.c	-	FutureIterType_as_async	-
Modules/_asynciomodule.c	-	FutureType_as_async	-
Objects/genobject.c	-	async_gen_as_async	-
Objects/genobject.c	-	async_gen_asend_as_async	-
Objects/genobject.c	-	async_gen_athrow_as_async	-
Objects/genobject.c	-	coro_as_async	-
Objects/genobject.c	-	gen_as_async	-
Objects/iterobject.c	-	anextawaitable_as_async	-

# PyBufferProcs
Modules/arraymodule.c	-	array_as_buffer	-
Modules/_ctypes/_ctypes.c	-	PyCData_as_buffer	-
Modules/_io/bytesio.c	-	bytesiobuf_as_buffer	-
Modules/mmapmodule.c	-	mmap_as_buffer	-
Objects/bytearrayobject.c	-	bytearray_as_buffer	-
Objects/bytesobject.c	-	bytes_as_buffer	-
Objects/memoryobject.c	-	memory_as_buffer	-
Objects/picklebufobject.c	-	picklebuf_as_buffer	-

# PyGetSetDef
Modules/_asynciomodule.c	-	FutureType_getsetlist	-
Modules/_asynciomodule.c	-	TaskStepMethWrapper_getsetlist	-
Modules/_asynciomodule.c	-	TaskType_getsetlist	-
Modules/_blake2/blake2b_impl.c	-	py_blake2b_getsetters	-
Modules/_blake2/blake2s_impl.c	-	py_blake2s_getsetters	-
Modules/_collectionsmodule.c	-	deque_getset	-
Modules/_csv.c	-	Dialect_getsetlist	-
Modules/_ctypes/_ctypes.c	-	CharArray_getsets	-
Modules/_ctypes/_ctypes.c	-	Pointer_getsets	-
Modules/_ctypes/_ctypes.c	-	PyCFuncPtr_getsets	-
Modules/_ctypes/_ctypes.c	-	Simple_getsets	-
Modules/_ctypes/_ctypes.c	-	WCharArray_getsets	-
Modules/_ctypes/cfield.c	-	PyCField_getset	-
Modules/_cursesmodule.c	-	PyCursesWindow_getsets	-
Modules/_datetimemodule.c	-	date_getset	-
Modules/_datetimemodule.c	-	datetime_getset	-
Modules/_datetimemodule.c	-	iso_calendar_date_getset	-
Modules/_datetimemodule.c	-	time_getset	-
Modules/_decimal/_decimal.c	-	context_getsets	-
Modules/_decimal/_decimal.c	-	dec_getsets	-
Modules/_elementtree.c	-	element_getsetlist	-
Modules/_elementtree.c	-	xmlparser_getsetlist	-
Modules/_functoolsmodule.c	-	lru_cache_getsetlist	-
Modules/_functoolsmodule.c	-	partial_getsetlist	-
Modules/_hashopenssl.c	-	EVPXOF_getseters	-
Modules/_hashopenssl.c	-	EVP_getseters	-
Modules/_hashopenssl.c	-	HMAC_getset	-
Modules/_io/bufferedio.c	-	bufferedrandom_getset	-
Modules/_io/bufferedio.c	-	bufferedreader_getset	-
Modules/_io/bufferedio.c	-	bufferedrwpair_getset	-
Modules/_io/bufferedio.c	-	bufferedwriter_getset	-
Modules/_io/bytesio.c	-	bytesio_getsetlist	-
Modules/_io/fileio.c	-	fileio_getsetlist	-
Modules/_io/iobase.c	-	iobase_getset	-
Modules/_io/stringio.c	-	stringio_getset	-
Modules/_io/textio.c	-	incrementalnewlinedecoder_getset	-
Modules/_io/textio.c	-	textiobase_getset	-
Modules/_io/textio.c	-	textiowrapper_getset	-
Modules/_io/winconsoleio.c	-	winconsoleio_getsetlist	-
Modules/_pickle.c	-	Pickler_getsets	-
Modules/_pickle.c	-	Unpickler_getsets	-
Modules/_sha3/sha3module.c	-	SHA3_getseters	-
Modules/_sqlite/connection.c	-	connection_getset	-
Modules/_sre.c	-	match_getset	-
Modules/_sre.c	-	pattern_getset	-
Modules/_ssl.c	-	PySSLSession_getsetlist	-
Modules/_ssl.c	-	context_getsetlist	-
Modules/_ssl.c	-	memory_bio_getsetlist	-
Modules/_ssl.c	-	ssl_getsetlist	-
Modules/_struct.c	-	s_getsetlist	-
Modules/_tkinter.c	-	PyTclObject_getsetlist	-
Modules/_xxsubinterpretersmodule.c	-	channelid_getsets	-
Modules/arraymodule.c	-	array_getsets	-
Modules/cjkcodecs/multibytecodec.c	-	codecctx_getsets	-
Modules/md5module.c	-	MD5_getseters	-
Modules/mmapmodule.c	-	mmap_object_getset	-
Modules/ossaudiodev.c	-	oss_getsetlist	-
Modules/overlapped.c	-	Overlapped_getsets	-
Modules/pyexpat.c	-	xmlparse_getsetlist	-
Modules/selectmodule.c	-	devpoll_getsetlist	-
Modules/selectmodule.c	-	kqueue_queue_getsetlist	-
Modules/selectmodule.c	-	pyepoll_getsetlist	-
Modules/sha1module.c	-	SHA1_getseters	-
Modules/sha256module.c	-	SHA_getseters	-
Modules/sha512module.c	-	SHA_getseters	-
Modules/socketmodule.c	-	sock_getsetlist	-
Modules/xxlimited.c	-	Xxo_getsetlist	-
Modules/xxsubtype.c	-	spamlist_getsets	-
Objects/cellobject.c	-	cell_getsetlist	-
Objects/classobject.c	-	instancemethod_getset	-
Objects/classobject.c	-	method_getset	-
Objects/codeobject.c	-	code_getsetlist	-
Objects/descrobject.c	-	getset_getset	-
Objects/descrobject.c	-	member_getset	-
Objects/descrobject.c	-	method_getset	-
Objects/descrobject.c	-	property_getsetlist	-
Objects/descrobject.c	-	wrapper_getsets	-
Objects/descrobject.c	-	wrapperdescr_getset	-
Objects/dictobject.c	-	dictview_getset	-
Objects/exceptions.c	-	BaseException_getset	-
Objects/exceptions.c	-	OSError_getset	-
Objects/fileobject.c	-	stdprinter_getsetlist	-
Objects/floatobject.c	-	float_getset	-
Objects/frameobject.c	-	frame_getsetlist	-
Objects/funcobject.c	-	cm_getsetlist	-
Objects/funcobject.c	-	func_getsetlist	-
Objects/funcobject.c	-	sm_getsetlist	-
Objects/genericaliasobject.c	-	ga_properties	-
Objects/genobject.c	-	async_gen_getsetlist	-
Objects/genobject.c	-	coro_getsetlist	-
Objects/genobject.c	-	gen_getsetlist	-
Objects/longobject.c	-	long_getset	-
Objects/memoryobject.c	-	memory_getsetlist	-
Objects/methodobject.c	-	meth_getsets	-
Objects/moduleobject.c	-	module_getsets	-
Objects/odictobject.c	-	odict_getset	-
Objects/typeobject.c	-	object_getsets	-
Objects/typeobject.c	-	subtype_getsets_dict_only	-
Objects/typeobject.c	-	subtype_getsets_full	-
Objects/typeobject.c	-	subtype_getsets_weakref_only	-
Objects/typeobject.c	-	type_getsets	-
Objects/unionobject.c	-	union_properties	-
Python/Python-ast.c	-	ast_type_getsets	-
Python/context.c	-	PyContextTokenType_getsetlist	-
Python/traceback.c	-	tb_getsetters	-

#-----------------------
# for heap types

# PyType_Slot
Modules/_abc.c	-	_abc_data_type_spec_slots	-
Modules/_blake2/blake2b_impl.c	-	blake2b_type_slots	-
Modules/_blake2/blake2s_impl.c	-	blake2s_type_slots	-
Modules/_bz2module.c	-	bz2_compressor_type_slots	-
Modules/_bz2module.c	-	bz2_decompressor_type_slots	-
Modules/_csv.c	-	Dialect_Type_slots	-
Modules/_csv.c	-	Reader_Type_slots	-
Modules/_csv.c	-	Writer_Type_slots	-
Modules/_csv.c	-	error_slots	-
Modules/_curses_panel.c	-	PyCursesPanel_Type_slots	-
Modules/_dbmmodule.c	-	dbmtype_spec_slots	-
Modules/_functoolsmodule.c	-	keyobject_type_slots	-
Modules/_functoolsmodule.c	-	lru_cache_type_slots	-
Modules/_functoolsmodule.c	-	lru_list_elem_type_slots	-
Modules/_functoolsmodule.c	-	partial_type_slots	-
Modules/_gdbmmodule.c	-	gdbmtype_spec_slots	-
Modules/_hashopenssl.c	-	EVPXOFtype_slots	-
Modules/_hashopenssl.c	-	EVPtype_slots	-
Modules/_hashopenssl.c	-	HMACtype_slots	-
Modules/_json.c	-	PyEncoderType_slots	-
Modules/_json.c	-	PyScannerType_slots	-
Modules/_lsprof.c	-	_lsprof_profiler_type_spec_slots	-
Modules/_lzmamodule.c	-	lzma_compressor_type_slots	-
Modules/_lzmamodule.c	-	lzma_decompressor_type_slots	-
Modules/_operator.c	-	attrgetter_type_slots	-
Modules/_operator.c	-	itemgetter_type_slots	-
Modules/_operator.c	-	methodcaller_type_slots	-
Modules/_queuemodule.c	-	simplequeue_slots	-
Modules/_randommodule.c	-	Random_Type_slots	-
Modules/_sha3/sha3module.c	-	SHAKE128slots	-
Modules/_sha3/sha3module.c	-	SHAKE256slots	-
Modules/_sha3/sha3module.c	-	sha3_224_slots	-
Modules/_sha3/sha3module.c	-	sha3_256_slots	-
Modules/_sha3/sha3module.c	-	sha3_384_slots	-
Modules/_sha3/sha3module.c	-	sha3_512_slots	-
Modules/_sha3/sha3module.c	-	type_slots_obj	-
Modules/_sqlite/connection.c	-	connection_slots	-
Modules/_sqlite/cursor.c	-	cursor_slots	-
Modules/_sqlite/prepare_protocol.c	-	type_slots	-
Modules/_sqlite/row.c	-	row_slots	-
Modules/_sqlite/statement.c	-	stmt_slots	-
Modules/_sre.c	-	match_slots	-
Modules/_sre.c	-	pattern_slots	-
Modules/_sre.c	-	scanner_slots	-
Modules/_ssl.c	-	PySSLContext_slots	-
Modules/_ssl.c	-	PySSLMemoryBIO_slots	-
Modules/_ssl.c	-	PySSLSession_slots	-
Modules/_ssl.c	-	PySSLSocket_slots	-
Modules/_ssl.c	-	sslerror_type_slots	-
Modules/_ssl/cert.c	-	PySSLCertificate_slots	-
Modules/_struct.c	-	PyStructType_slots	-
Modules/_struct.c	-	unpackiter_type_slots	-
Modules/_testcapimodule.c	-	HeapTypeNameType_slots	-
Modules/_testcapimodule.c	-	NullTpDocType_slots	-
Modules/_threadmodule.c	-	local_dummy_type_slots	-
Modules/_threadmodule.c	-	local_type_slots	-
Modules/_threadmodule.c	-	lock_type_slots	-
Modules/_threadmodule.c	-	rlock_type_slots	-
Modules/_tkinter.c	-	PyTclObject_Type_slots	-
Modules/_tkinter.c	-	Tkapp_Type_slots	-
Modules/_tkinter.c	-	Tktt_Type_slots	-
Modules/_winapi.c	-	winapi_overlapped_type_slots	-
Modules/arraymodule.c	-	array_slots	-
Modules/arraymodule.c	-	arrayiter_slots	-
Modules/cjkcodecs/multibytecodec.c	-	decoder_slots	-
Modules/cjkcodecs/multibytecodec.c	-	encoder_slots	-
Modules/cjkcodecs/multibytecodec.c	-	multibytecodec_slots	-
Modules/cjkcodecs/multibytecodec.c	-	reader_slots	-
Modules/cjkcodecs/multibytecodec.c	-	writer_slots	-
Modules/md5module.c	-	md5_type_slots	-
Modules/mmapmodule.c	-	mmap_object_slots	-
Modules/overlapped.c	-	overlapped_type_slots	-
Modules/posixmodule.c	-	DirEntryType_slots	-
Modules/posixmodule.c	-	ScandirIteratorType_slots	-
Modules/pyexpat.c	-	_xml_parse_type_spec_slots	-
Modules/selectmodule.c	-	devpoll_Type_slots	-
Modules/selectmodule.c	-	kqueue_event_Type_slots	-
Modules/selectmodule.c	-	kqueue_queue_Type_slots	-
Modules/selectmodule.c	-	poll_Type_slots	-
Modules/selectmodule.c	-	pyEpoll_Type_slots	-
Modules/sha1module.c	-	sha1_type_slots	-
Modules/sha256module.c	-	sha256_types_slots	-
Modules/sha512module.c	-	sha512_sha384_type_slots	-
Modules/sha512module.c	-	sha512_sha512_type_slots	-
Modules/unicodedata.c	-	ucd_type_slots	-
Modules/xxlimited.c	-	Null_Type_slots	-
Modules/xxlimited.c	-	Str_Type_slots	-
Modules/xxlimited.c	-	Xxo_Type_slots	-
Modules/xxlimited_35.c	-	Null_Type_slots	-
Modules/xxlimited_35.c	-	Str_Type_slots	-
Modules/xxlimited_35.c	-	Xxo_Type_slots	-
Modules/zlibmodule.c	-	Comptype_slots	-
Modules/zlibmodule.c	-	Decomptype_slots	-
Python/Python-ast.c	-	AST_type_slots	-
Python/Python-tokenize.c	-	tokenizeriter_slots	-

# PyType_Spec
Modules/_abc.c	-	_abc_data_type_spec	-
Modules/_blake2/blake2b_impl.c	-	blake2b_type_spec	-
Modules/_blake2/blake2s_impl.c	-	blake2s_type_spec	-
Modules/_bz2module.c	-	bz2_compressor_type_spec	-
Modules/_bz2module.c	-	bz2_decompressor_type_spec	-
Modules/_csv.c	-	Dialect_Type_spec	-
Modules/_csv.c	-	Reader_Type_spec	-
Modules/_csv.c	-	Writer_Type_spec	-
Modules/_csv.c	-	error_spec	-
Modules/_curses_panel.c	-	PyCursesPanel_Type_spec	-
Modules/_dbmmodule.c	-	dbmtype_spec	-
Modules/_functoolsmodule.c	-	keyobject_type_spec	-
Modules/_functoolsmodule.c	-	lru_cache_type_spec	-
Modules/_functoolsmodule.c	-	lru_list_elem_type_spec	-
Modules/_functoolsmodule.c	-	partial_type_spec	-
Modules/_gdbmmodule.c	-	gdbmtype_spec	-
Modules/_hashopenssl.c	-	EVPXOFtype_spec	-
Modules/_hashopenssl.c	-	EVPtype_spec	-
Modules/_hashopenssl.c	-	HMACtype_spec	-
Modules/_json.c	-	PyEncoderType_spec	-
Modules/_json.c	-	PyScannerType_spec	-
Modules/_lsprof.c	-	_lsprof_profiler_type_spec	-
Modules/_lzmamodule.c	-	lzma_compressor_type_spec	-
Modules/_lzmamodule.c	-	lzma_decompressor_type_spec	-
Modules/_operator.c	-	attrgetter_type_spec	-
Modules/_operator.c	-	itemgetter_type_spec	-
Modules/_operator.c	-	methodcaller_type_spec	-
Modules/_queuemodule.c	-	simplequeue_spec	-
Modules/_randommodule.c	-	Random_Type_spec	-
Modules/_sha3/sha3module.c	-	SHAKE128_spec	-
Modules/_sha3/sha3module.c	-	SHAKE256_spec	-
Modules/_sha3/sha3module.c	-	sha3_224_spec	-
Modules/_sha3/sha3module.c	-	sha3_256_spec	-
Modules/_sha3/sha3module.c	-	sha3_384_spec	-
Modules/_sha3/sha3module.c	-	sha3_512_spec	-
Modules/_sha3/sha3module.c	-	type_spec_obj	-
Modules/_sqlite/connection.c	-	connection_spec	-
Modules/_sqlite/cursor.c	-	cursor_spec	-
Modules/_sqlite/prepare_protocol.c	-	type_spec	-
Modules/_sqlite/row.c	-	row_spec	-
Modules/_sqlite/statement.c	-	stmt_spec	-
Modules/_sre.c	-	match_spec	-
Modules/_sre.c	-	pattern_spec	-
Modules/_sre.c	-	scanner_spec	-
Modules/_ssl.c	-	PySSLContext_spec	-
Modules/_ssl.c	-	PySSLMemoryBIO_spec	-
Modules/_ssl.c	-	PySSLSession_spec	-
Modules/_ssl.c	-	PySSLSocket_spec	-
Modules/_ssl.c	-	sslerror_type_spec	-
Modules/_ssl/cert.c	-	PySSLCertificate_spec	-
Modules/_struct.c	-	PyStructType_spec	-
Modules/_struct.c	-	unpackiter_type_spec	-
Modules/_testcapimodule.c	-	HeapTypeNameType_Spec	-
Modules/_testcapimodule.c	-	NullTpDocType_spec	-
Modules/_threadmodule.c	-	local_dummy_type_spec	-
Modules/_threadmodule.c	-	local_type_spec	-
Modules/_threadmodule.c	-	lock_type_spec	-
Modules/_threadmodule.c	-	rlock_type_spec	-
Modules/_tkinter.c	-	PyTclObject_Type_spec	-
Modules/_tkinter.c	-	Tkapp_Type_spec	-
Modules/_tkinter.c	-	Tktt_Type_spec	-
Modules/_winapi.c	-	winapi_overlapped_type_spec	-
Modules/_zoneinfo.c	-	DAYS_BEFORE_MONTH	-
Modules/_zoneinfo.c	-	DAYS_IN_MONTH	-
Modules/arraymodule.c	-	array_spec	-
Modules/arraymodule.c	-	arrayiter_spec	-
Modules/cjkcodecs/multibytecodec.c	-	decoder_spec	-
Modules/cjkcodecs/multibytecodec.c	-	encoder_spec	-
Modules/cjkcodecs/multibytecodec.c	-	multibytecodec_spec	-
Modules/cjkcodecs/multibytecodec.c	-	reader_spec	-
Modules/cjkcodecs/multibytecodec.c	-	writer_spec	-
Modules/md5module.c	-	md5_type_spec	-
Modules/mmapmodule.c	-	mmap_object_spec	-
Modules/overlapped.c	-	overlapped_type_spec	-
Modules/posixmodule.c	-	DirEntryType_spec	-
Modules/posixmodule.c	-	ScandirIteratorType_spec	-
Modules/pyexpat.c	-	_xml_parse_type_spec	-
Modules/selectmodule.c	-	devpoll_Type_spec	-
Modules/selectmodule.c	-	kqueue_event_Type_spec	-
Modules/selectmodule.c	-	kqueue_queue_Type_spec	-
Modules/selectmodule.c	-	poll_Type_spec	-
Modules/selectmodule.c	-	pyEpoll_Type_spec	-
Modules/sha1module.c	-	sha1_type_spec	-
Modules/sha256module.c	-	sha224_type_spec	-
Modules/sha256module.c	-	sha256_type_spec	-
Modules/sha512module.c	-	sha512_sha384_type_spec	-
Modules/sha512module.c	-	sha512_sha512_type_spec	-
Modules/unicodedata.c	-	ucd_type_spec	-
Modules/xxlimited.c	-	Null_Type_spec	-
Modules/xxlimited.c	-	Str_Type_spec	-
Modules/xxlimited.c	-	Xxo_Type_spec	-
Modules/xxlimited_35.c	-	Null_Type_spec	-
Modules/xxlimited_35.c	-	Str_Type_spec	-
Modules/xxlimited_35.c	-	Xxo_Type_spec	-
Modules/zlibmodule.c	-	Comptype_spec	-
Modules/zlibmodule.c	-	Decomptype_spec	-
Python/Python-ast.c	-	AST_type_spec	-
Python/Python-tokenize.c	-	tokenizeriter_spec	-

#-----------------------
# for structseq

# PyStructSequence_Field[]
Modules/_cursesmodule.c	-	ncurses_version_fields	-
Modules/grpmodule.c	-	struct_group_type_fields	-
Modules/_lsprof.c	-	profiler_entry_fields	-
Modules/_lsprof.c	-	profiler_subentry_fields	-
Modules/posixmodule.c	-	stat_result_fields	-
Modules/posixmodule.c	-	statvfs_result_fields	-
Modules/posixmodule.c	-	waitid_result_fields	-
Modules/posixmodule.c	-	uname_result_fields	-
Modules/posixmodule.c	-	sched_param_fields	-
Modules/posixmodule.c	-	times_result_fields	-
Modules/posixmodule.c	-	TerminalSize_fields	-
Modules/pwdmodule.c	-	struct_pwd_type_fields	-
Modules/resource.c	-	struct_rusage_fields	-
Modules/signalmodule.c	-	struct_siginfo_fields	-
Modules/spwdmodule.c	-	struct_spwd_type_fields	-
Modules/_threadmodule.c	-	ExceptHookArgs_fields	-
Modules/timemodule.c	-	struct_time_type_fields	-
Objects/floatobject.c	-	floatinfo_fields	-
Objects/longobject.c	-	int_info_fields	-
Python/errors.c	-	UnraisableHookArgs_fields	-
Python/sysmodule.c	-	asyncgen_hooks_fields	-
Python/sysmodule.c	-	hash_info_fields	-
Python/sysmodule.c	-	windows_version_fields	-
Python/sysmodule.c	-	flags_fields	-
Python/sysmodule.c	-	version_info_fields	-
Python/thread.c	-	threadinfo_fields	-

# PyStructSequence_Desc
Modules/_cursesmodule.c	-	ncurses_version_desc	-
Modules/grpmodule.c	-	struct_group_type_desc	-
Modules/_lsprof.c	-	profiler_entry_desc	-
Modules/_lsprof.c	-	profiler_subentry_desc	-
Modules/posixmodule.c	-	stat_result_desc	-
Modules/posixmodule.c	-	statvfs_result_desc	-
Modules/posixmodule.c	-	waitid_result_desc	-
Modules/posixmodule.c	-	uname_result_desc	-
Modules/posixmodule.c	-	sched_param_desc	-
Modules/posixmodule.c	-	times_result_desc	-
Modules/posixmodule.c	-	TerminalSize_desc	-
Modules/pwdmodule.c	-	struct_pwd_type_desc	-
Modules/resource.c	-	struct_rusage_desc	-
Modules/signalmodule.c	-	struct_siginfo_desc	-
Modules/spwdmodule.c	-	struct_spwd_type_desc	-
Modules/_threadmodule.c	-	ExceptHookArgs_desc	-
Modules/timemodule.c	-	struct_time_type_desc	-
Objects/floatobject.c	-	floatinfo_desc	-
Objects/longobject.c	-	int_info_desc	-
Python/errors.c	-	UnraisableHookArgs_desc	-
Python/sysmodule.c	-	asyncgen_hooks_desc	-
Python/sysmodule.c	-	hash_info_desc	-
Python/sysmodule.c	-	windows_version_desc	-
Python/sysmodule.c	-	flags_desc	-
Python/sysmodule.c	-	version_info_desc	-
Python/thread.c	-	threadinfo_desc	-

#-----------------------
# other vars that are actually constant

Modules/_csv.c	-	quote_styles	-
Modules/_ctypes/cfield.c	-	ffi_type_double	-
Modules/_ctypes/cfield.c	-	ffi_type_float	-
Modules/_ctypes/cfield.c	-	ffi_type_longdouble	-
Modules/_ctypes/cfield.c	-	ffi_type_pointer	-
Modules/_ctypes/cfield.c	-	ffi_type_sint16	-
Modules/_ctypes/cfield.c	-	ffi_type_sint32	-
Modules/_ctypes/cfield.c	-	ffi_type_sint64	-
Modules/_ctypes/cfield.c	-	ffi_type_sint8	-
Modules/_ctypes/cfield.c	-	ffi_type_uint16	-
Modules/_ctypes/cfield.c	-	ffi_type_uint32	-
Modules/_ctypes/cfield.c	-	ffi_type_uint64	-
Modules/_ctypes/cfield.c	-	ffi_type_uint8	-
Modules/_ctypes/cfield.c	-	ffi_type_void	-
Modules/_datetimemodule.c	-	epoch	-
Modules/_datetimemodule.c	-	max_fold_seconds	-
Modules/_datetimemodule.c	datetime_isoformat	specs	-
Modules/_datetimemodule.c	time_isoformat	specs	-
Modules/_decimal/_decimal.c	-	cond_map	-
Modules/_decimal/_decimal.c	-	dec_signal_string	-
Modules/_decimal/_decimal.c	-	dflt_ctx	-
Modules/_decimal/_decimal.c	-	int_constants	-
Modules/_decimal/_decimal.c	-	invalid_rounding_err	-
Modules/_decimal/_decimal.c	-	invalid_signals_err	-
Modules/_decimal/_decimal.c	-	signal_map	-
Modules/_decimal/_decimal.c	-	ssize_constants	-
Modules/_decimal/_decimal.c -   INVALID_SIGNALDICT_ERROR_MSG -
Modules/_elementtree.c	-	ExpatMemoryHandler	-
Modules/_io/_iomodule.c	-	static_types	-
Modules/_io/textio.c	-	encodefuncs	-
Modules/_localemodule.c	-	langinfo_constants	-
Modules/_sqlite/module.c	-	error_codes	-
Modules/_sre.c	pattern_repr	flag_names	-
Modules/_struct.c	-	bigendian_table	-
Modules/_struct.c	-	lilendian_table	-
Modules/_tkinter.c	-	state_key	-
Modules/_xxsubinterpretersmodule.c	-	_channelid_end_recv	-
Modules/_xxsubinterpretersmodule.c	-	_channelid_end_send	-
Modules/arraymodule.c	-	descriptors	-
Modules/arraymodule.c	-	emptybuf	-
Modules/cjkcodecs/cjkcodecs.h	-	__methods	-
Modules/cmathmodule.c	-	acos_special_values	-
Modules/cmathmodule.c	-	acosh_special_values	-
Modules/cmathmodule.c	-	asinh_special_values	-
Modules/cmathmodule.c	-	atanh_special_values	-
Modules/cmathmodule.c	-	cosh_special_values	-
Modules/cmathmodule.c	-	exp_special_values	-
Modules/cmathmodule.c	-	log_special_values	-
Modules/cmathmodule.c	-	rect_special_values	-
Modules/cmathmodule.c	-	sinh_special_values	-
Modules/cmathmodule.c	-	sqrt_special_values	-
Modules/cmathmodule.c	-	tanh_special_values	-
Modules/config.c	-	_PyImport_Inittab	-
Modules/faulthandler.c	-	faulthandler_handlers	-
Modules/getnameinfo.c	-	gni_afdl	-
Modules/nismodule.c	-	TIMEOUT	-
Modules/nismodule.c	-	aliases	-
Modules/ossaudiodev.c	-	control_labels	-
Modules/ossaudiodev.c	-	control_names	-
Modules/posixmodule.c	-	posix_constants_confstr	-
Modules/posixmodule.c	-	posix_constants_pathconf	-
Modules/posixmodule.c	-	posix_constants_sysconf	-
Modules/pyexpat.c	-	ExpatMemoryHandler	-
Modules/pyexpat.c	-	error_info_of	-
Modules/pyexpat.c	-	handler_info	-
Modules/termios.c	-	termios_constants	-
Modules/timemodule.c	init_timezone	YEAR	-
Objects/bytearrayobject.c	-	_PyByteArray_empty_string	-
Objects/complexobject.c	-	c_1	-
Objects/exceptions.c	-	static_exceptions	-
Objects/genobject.c	-	ASYNC_GEN_IGNORED_EXIT_MSG	-
Objects/genobject.c	-	NON_INIT_CORO_MSG	-
Objects/longobject.c	-	_PyLong_DigitValue	-
Objects/object.c	-	_Py_SwappedOp	-
Objects/object.c	-	_Py_abstract_hack	-
Objects/object.c	-	static_types	-
Objects/obmalloc.c	-	_PyMem	-
Objects/obmalloc.c	-	_PyMem_Debug	-
Objects/obmalloc.c	-	_PyMem_Raw	-
Objects/obmalloc.c	-	_PyObject	-
Objects/obmalloc.c	-	usedpools	-
Objects/typeobject.c	-	name_op	-
Objects/unicodeobject.c	-	stripfuncnames	-
Objects/unicodeobject.c	-	utf7_category	-
Objects/unicodeobject.c	unicode_decode_call_errorhandler_wchar	argparse	-
Objects/unicodeobject.c	unicode_decode_call_errorhandler_writer	argparse	-
Objects/unicodeobject.c	unicode_encode_call_errorhandler	argparse	-
Objects/unicodeobject.c	unicode_translate_call_errorhandler	argparse	-
Parser/parser.c	-	reserved_keywords	-
Parser/parser.c	-	soft_keywords	-
Parser/tokenizer.c	-	type_comment_prefix	-
Python/ast_opt.c	fold_unaryop	ops	-
Python/codecs.c	-	Py_hexdigits	-
Python/codecs.c	-	ucnhash_capi	-
Python/codecs.c	_PyCodecRegistry_Init	methods	-
Python/dynload_shlib.c	-	_PyImport_DynLoadFiletab	-
Python/dynload_stub.c	-	_PyImport_DynLoadFiletab	-
Python/frozen.c	-	aliases	-
Python/frozen.c	-	bootstrap_modules	-
Python/frozen.c	-	stdlib_modules	-
Python/frozen.c	-	test_modules	-
Python/getopt.c	-	longopts	-
Python/import.c	-	_PyImport_Inittab	-
Python/import.c	-	_PySys_ImplCacheTag	-
Python/opcode_targets.h	-	opcode_targets	-
Python/pyhash.c	-	PyHash_Func	-
Python/pylifecycle.c	-	_C_LOCALE_WARNING	-
Python/pylifecycle.c	-	_PyOS_mystrnicmp_hack	-
Python/pylifecycle.c	-	_TARGET_LOCALES	-
Python/specialize.c	-	adaptive_opcodes	-
Python/specialize.c	-	cache_requirements	-
Python/specialize.c	-	compare_masks	-
Python/sysmodule.c	-	whatstrings	-
