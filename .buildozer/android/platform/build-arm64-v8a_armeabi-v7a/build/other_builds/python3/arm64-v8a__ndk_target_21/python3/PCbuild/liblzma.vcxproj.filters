<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{cb1870af-3c7e-48ba-bd7f-3e87468f8ed7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{58761ffe-2af0-42a8-9f93-4e57e1954c36}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\alone_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\alone_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\arm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\armthumb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\auto_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_buffer_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_buffer_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_header_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_header_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\check\check.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\block_util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\check\crc32_fast.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\check\crc32_table.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\check\crc64_fast.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\check\crc64_table.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\delta\delta_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\delta\delta_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\delta\delta_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_buffer_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_decoder_memusage.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_preset.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\easy_encoder_memusage.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\fastpos_table.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_buffer_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_buffer_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_flags_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\filter_flags_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\hardware_cputhreads.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\hardware_physmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\index.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\index_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\index_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\index_hash.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lz\lz_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lz\lz_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lz\lz_encoder_mf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\ia64.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder_optimum_fast.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder_optimum_normal.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder_presets.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma2_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\x86.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\vli_size.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\vli_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\vli_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\common\tuklib_physmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\common\tuklib_cpucores.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_flags_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_flags_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_flags_common.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_encoder_mt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_buffer_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\stream_buffer_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\sparc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\simple_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\simple_decoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\simple_coder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\check\sha256.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\rangecoder\price_table.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\simple\powerpc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\common\outqueue.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(lzmaDir)src\liblzma\lzma\lzma2_encoder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="$(lzmaDir)src\common\mythread.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\common\sysdefs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\common\tuklib_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\common\tuklib_config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\common\tuklib_cpucores.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\common\tuklib_integer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\common\tuklib_physmem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\base.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\bcj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\block.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\check.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\container.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\delta.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\filter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\hardware.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\index_hash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\index.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\lzma12.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\stream_flags.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\api\lzma\vli.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\check\check.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc_macros.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc32_table_be.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc32_table_le.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc64_table_be.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\check\crc64_table_le.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\alone_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\block_buffer_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\block_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\block_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\easy_preset.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\filter_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\filter_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\filter_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\index_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\index.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\memcmplen.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\outqueue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\stream_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\common\stream_flags_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\delta\delta_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\delta\delta_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\delta\delta_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\delta\delta_private.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lz\lz_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lz\lz_encoder_hash_table.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lz\lz_encoder_hash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lz\lz_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\fastpos.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder_private.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma2_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\lzma\lzma2_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\rangecoder\price.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\rangecoder\range_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\rangecoder\range_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\rangecoder\range_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\simple\simple_coder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\simple\simple_decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\simple\simple_encoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)src\liblzma\simple\simple_private.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(lzmaDir)windows\vs2019\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>