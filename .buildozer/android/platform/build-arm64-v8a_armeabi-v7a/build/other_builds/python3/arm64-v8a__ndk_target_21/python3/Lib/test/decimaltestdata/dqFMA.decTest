------------------------------------------------------------------------
-- dqFMA.decTest -- decQuad Fused Multiply Add                        --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- These tests comprise three parts:
--   1. Sanity checks and other three-operand tests (especially those
--      where the fused operation makes a difference)
--   2. Multiply tests (third operand is neutral zero [0E+emax])
--   3. Addition tests (first operand is 1)
-- The multiply and addition tests are extensive because FMA may have
-- its own dedicated multiplication or addition routine(s), and they
-- also inherently check the left-to-right properties.

-- Sanity checks
dqfma0001 fma  1   1   1 ->   2
dqfma0002 fma  1   1   2 ->   3
dqfma0003 fma  2   2   3 ->   7
dqfma0004 fma  9   9   9 ->  90
dqfma0005 fma -1   1   1 ->   0
dqfma0006 fma -1   1   2 ->   1
dqfma0007 fma -2   2   3 ->  -1
dqfma0008 fma -9   9   9 -> -72
dqfma0011 fma  1  -1   1 ->   0
dqfma0012 fma  1  -1   2 ->   1
dqfma0013 fma  2  -2   3 ->  -1
dqfma0014 fma  9  -9   9 -> -72
dqfma0015 fma  1   1  -1 ->   0
dqfma0016 fma  1   1  -2 ->  -1
dqfma0017 fma  2   2  -3 ->   1
dqfma0018 fma  9   9  -9 ->  72

-- non-integer exacts
dqfma0100  fma    25.2   63.6   -438  ->  1164.72
dqfma0101  fma   0.301  0.380    334  ->  334.114380
dqfma0102  fma    49.2   -4.8   23.3  ->  -212.86
dqfma0103  fma    4.22  0.079  -94.6  ->  -94.26662
dqfma0104  fma     903  0.797  0.887  ->  720.578
dqfma0105  fma    6.13   -161   65.9  ->  -921.03
dqfma0106  fma    28.2    727   5.45  ->  20506.85
dqfma0107  fma       4    605    688  ->  3108
dqfma0108  fma    93.3   0.19  0.226  ->  17.953
dqfma0109  fma   0.169   -341   5.61  ->  -52.019
dqfma0110  fma   -72.2     30  -51.2  ->  -2217.2
dqfma0111  fma  -0.409     13   20.4  ->  15.083
dqfma0112  fma     317   77.0   19.0  ->  24428.0
dqfma0113  fma      47   6.58   1.62  ->  310.88
dqfma0114  fma    1.36  0.984  0.493  ->  1.83124
dqfma0115  fma    72.7    274   1.56  ->  19921.36
dqfma0116  fma     335    847     83  ->  283828
dqfma0117  fma     666  0.247   25.4  ->  189.902
dqfma0118  fma   -3.87   3.06   78.0  ->  66.1578
dqfma0119  fma   0.742    192   35.6  ->  178.064
dqfma0120  fma   -91.6   5.29  0.153  ->  -484.411

-- cases where result is different from separate multiply + add; each
-- is preceded by the result of unfused multiply and add
-- [this is about 20% of all similar  cases in general]
--                                                                                                            ->  4.500119002100000209469729375698778E+38
dqfma0202  fma       68537985861355864457.5694      6565875762972086605.85969       35892634447236753.172812  ->  4.500119002100000209469729375698779E+38 Inexact Rounded
--                                                                                                            ->  5.996248469584594346858881620185514E+41
dqfma0208  fma          89261822344727628571.9      6717595845654131383336.89      5061036497288796076266.11  ->  5.996248469584594346858881620185513E+41 Inexact Rounded
--                                                                                                            ->  1.899242968678256924021594770874070E+34
dqfma0210  fma       320506237232448685.495971       59257597764017967.984448      3205615239077711589912.85  ->  1.899242968678256924021594770874071E+34 Inexact Rounded
--                                                                                                            ->  7.078596978842809537929699954860309E+37
dqfma0215  fma        220247843259112263.17995       321392340287987979002.80        47533279819997167655440  ->  7.078596978842809537929699954860308E+37 Inexact Rounded
--                                                                                                            ->  1.224955667581427559754106862350743E+37
dqfma0226  fma       23880729790368880412.1449       512947333827064719.55407      217117438419590824502.963  ->  1.224955667581427559754106862350744E+37 Inexact Rounded
--                                                                                                            ->  -2.530094043253148806272276368579144E+42
dqfma0229  fma        2539892357016099706.4126      -996142232667504817717435       53682082598315949425.937  ->  -2.530094043253148806272276368579143E+42 Inexact Rounded
--                                                                                                            ->  1.713387085759711954319391412788454E+37
dqfma0233  fma        4546339491341624464.0804            3768717864169205581       83578980278690395184.620  ->  1.713387085759711954319391412788453E+37 Inexact Rounded
--                                                                                                            ->  4.062275663405823716411579117771547E+35
dqfma0235  fma        409242119433816131.42253      992633815166741501.477249        70179636544416756129546  ->  4.062275663405823716411579117771548E+35 Inexact Rounded
--                                                                                                            ->  6.002604327732568490562249875306823E+47
dqfma0258  fma        817941336593541742159684       733867339769310729266598      78563844650942419311830.8  ->  6.002604327732568490562249875306822E+47 Inexact Rounded
--                                                                                                            ->  -2.027022514381452197510103395283874E+39
dqfma0264  fma       387617310169161270.737532     -5229442703414956061216.62      57665666816652967150473.5  ->  -2.027022514381452197510103395283873E+39 Inexact Rounded
--                                                                                                            ->  -7.856525039803554001144089842730361E+37
dqfma0267  fma      -847655845720565274701.210        92685316564117739.83984      22780950041376424429.5686  ->  -7.856525039803554001144089842730360E+37 Inexact Rounded
--                                                                                                            ->  1.695515562011520746125607502237559E+38
dqfma0268  fma          21590290365127685.3675       7853139227576541379426.8       -3275859437236180.761544  ->  1.695515562011520746125607502237558E+38 Inexact Rounded
--                                                                                                            ->  -8.448422935783289219748115038014710E+38
dqfma0269  fma      -974320636272862697.971586      867109103641860247440.756        -9775170775902454762.98  ->  -8.448422935783289219748115038014709E+38 Inexact Rounded

-- Cases where multiply would overflow or underflow if separate
dqfma0300  fma   9e+6144    10   0         -> Infinity  Overflow Inexact Rounded
dqfma0301  fma   1e+6144    10   0         -> Infinity  Overflow Inexact Rounded
dqfma0302  fma   1e+6144    10   -1e+6144  -> 9.000000000000000000000000000000000E+6144 Clamped
dqfma0303  fma   1e+6144    10   -9e+6144  -> 1.000000000000000000000000000000000E+6144 Clamped
-- subnormal etc.
dqfma0305  fma   1e-6176    0.1  0         -> 0E-6176 Underflow Subnormal Inexact Rounded Clamped
dqfma0306  fma   1e-6176    0.1  1         -> 1.000000000000000000000000000000000 Inexact Rounded
dqfma0307  fma   1e-6176    0.1  1e-6176   -> 1E-6176 Underflow Subnormal Inexact Rounded

-- Infinite combinations
dqfma0800 fma  Inf   Inf   Inf    ->  Infinity
dqfma0801 fma  Inf   Inf  -Inf    ->  NaN Invalid_operation
dqfma0802 fma  Inf  -Inf   Inf    ->  NaN Invalid_operation
dqfma0803 fma  Inf  -Inf  -Inf    -> -Infinity
dqfma0804 fma -Inf   Inf   Inf    ->  NaN Invalid_operation
dqfma0805 fma -Inf   Inf  -Inf    -> -Infinity
dqfma0806 fma -Inf  -Inf   Inf    ->  Infinity
dqfma0807 fma -Inf  -Inf  -Inf    ->  NaN Invalid_operation

-- Triple NaN propagation
dqfma0900 fma  NaN2  NaN3  NaN5   ->  NaN2
dqfma0901 fma  0     NaN3  NaN5   ->  NaN3
dqfma0902 fma  0     0     NaN5   ->  NaN5
-- first sNaN wins (consider qNaN from earlier sNaN being
-- overridden by an sNaN in third operand)
dqfma0903 fma  sNaN1 sNaN2 sNaN3  ->  NaN1 Invalid_operation
dqfma0904 fma  0     sNaN2 sNaN3  ->  NaN2 Invalid_operation
dqfma0905 fma  0     0     sNaN3  ->  NaN3 Invalid_operation
dqfma0906 fma  sNaN1 sNaN2 sNaN3  ->  NaN1 Invalid_operation
dqfma0907 fma  NaN7  sNaN2 sNaN3  ->  NaN2 Invalid_operation
dqfma0908 fma  NaN7  NaN5  sNaN3  ->  NaN3 Invalid_operation

-- MULTIPLICATION TESTS ------------------------------------------------
rounding:    half_even

-- sanity checks
dqfma2000 fma  2      2   0e+6144  -> 4
dqfma2001 fma  2      3   0e+6144  -> 6
dqfma2002 fma  5      1   0e+6144  -> 5
dqfma2003 fma  5      2   0e+6144  -> 10
dqfma2004 fma  1.20   2   0e+6144  -> 2.40
dqfma2005 fma  1.20   0   0e+6144  -> 0.00
dqfma2006 fma  1.20  -2   0e+6144  -> -2.40
dqfma2007 fma  -1.20  2   0e+6144  -> -2.40
dqfma2008 fma  -1.20  0   0e+6144  -> 0.00
dqfma2009 fma  -1.20 -2   0e+6144  -> 2.40
dqfma2010 fma  5.09 7.1   0e+6144  -> 36.139
dqfma2011 fma  2.5    4   0e+6144  -> 10.0
dqfma2012 fma  2.50   4   0e+6144  -> 10.00
dqfma2013 fma  1.23456789 1.0000000000000000000000000000   0e+6144  -> 1.234567890000000000000000000000000 Rounded
dqfma2015 fma  2.50   4   0e+6144  -> 10.00
dqfma2016 fma   9.99999999999999999  9.99999999999999999   0e+6144  ->  99.99999999999999980000000000000000 Inexact Rounded
dqfma2017 fma   9.99999999999999999 -9.99999999999999999   0e+6144  -> -99.99999999999999980000000000000000 Inexact Rounded
dqfma2018 fma  -9.99999999999999999  9.99999999999999999   0e+6144  -> -99.99999999999999980000000000000000 Inexact Rounded
dqfma2019 fma  -9.99999999999999999 -9.99999999999999999   0e+6144  ->  99.99999999999999980000000000000000 Inexact Rounded

-- zeros, etc.
dqfma2021 fma   0      0       0e+6144  ->  0
dqfma2022 fma   0     -0       0e+6144  ->  0
dqfma2023 fma  -0      0       0e+6144  ->  0
dqfma2024 fma  -0     -0       0e+6144  ->  0
dqfma2025 fma  -0.0   -0.0     0e+6144  ->  0.00
dqfma2026 fma  -0.0   -0.0     0e+6144  ->  0.00
dqfma2027 fma  -0.0   -0.0     0e+6144  ->  0.00
dqfma2028 fma  -0.0   -0.0     0e+6144  ->  0.00
dqfma2030 fma   5.00   1E-3    0e+6144  ->  0.00500
dqfma2031 fma   00.00  0.000   0e+6144  ->  0.00000
dqfma2032 fma   00.00  0E-3    0e+6144  ->  0.00000     -- rhs is 0
dqfma2033 fma   0E-3   00.00   0e+6144  ->  0.00000     -- lhs is 0
dqfma2034 fma  -5.00   1E-3    0e+6144  -> -0.00500
dqfma2035 fma  -00.00  0.000   0e+6144  ->  0.00000
dqfma2036 fma  -00.00  0E-3    0e+6144  ->  0.00000     -- rhs is 0
dqfma2037 fma  -0E-3   00.00   0e+6144  ->  0.00000     -- lhs is 0
dqfma2038 fma   5.00  -1E-3    0e+6144  -> -0.00500
dqfma2039 fma   00.00 -0.000   0e+6144  ->  0.00000
dqfma2040 fma   00.00 -0E-3    0e+6144  ->  0.00000     -- rhs is 0
dqfma2041 fma   0E-3  -00.00   0e+6144  ->  0.00000     -- lhs is 0
dqfma2042 fma  -5.00  -1E-3    0e+6144  ->  0.00500
dqfma2043 fma  -00.00 -0.000   0e+6144  ->  0.00000
dqfma2044 fma  -00.00 -0E-3    0e+6144  ->  0.00000     -- rhs is 0
dqfma2045 fma  -0E-3  -00.00   0e+6144  ->  0.00000     -- lhs is 0

-- examples from decarith
dqfma2050 fma  1.20 3          0e+6144  -> 3.60
dqfma2051 fma  7    3          0e+6144  -> 21
dqfma2052 fma  0.9  0.8        0e+6144  -> 0.72
dqfma2053 fma  0.9  -0         0e+6144  -> 0.0
dqfma2054 fma  654321 654321   0e+6144  -> 428135971041

dqfma2060 fma  123.45 1e7    0e+6144  ->  1.2345E+9
dqfma2061 fma  123.45 1e8    0e+6144  ->  1.2345E+10
dqfma2062 fma  123.45 1e+9   0e+6144  ->  1.2345E+11
dqfma2063 fma  123.45 1e10   0e+6144  ->  1.2345E+12
dqfma2064 fma  123.45 1e11   0e+6144  ->  1.2345E+13
dqfma2065 fma  123.45 1e12   0e+6144  ->  1.2345E+14
dqfma2066 fma  123.45 1e13   0e+6144  ->  1.2345E+15


-- test some intermediate lengths
--                    1234567890123456
dqfma2080 fma  0.1 1230123456456789       0e+6144  -> 123012345645678.9
dqfma2084 fma  0.1 1230123456456789       0e+6144  -> 123012345645678.9
dqfma2090 fma  1230123456456789     0.1   0e+6144  -> 123012345645678.9
dqfma2094 fma  1230123456456789     0.1   0e+6144  -> 123012345645678.9

-- test some more edge cases and carries
dqfma2101 fma  9 9     0e+6144  -> 81
dqfma2102 fma  9 90     0e+6144  -> 810
dqfma2103 fma  9 900     0e+6144  -> 8100
dqfma2104 fma  9 9000     0e+6144  -> 81000
dqfma2105 fma  9 90000     0e+6144  -> 810000
dqfma2106 fma  9 900000     0e+6144  -> 8100000
dqfma2107 fma  9 9000000     0e+6144  -> 81000000
dqfma2108 fma  9 90000000     0e+6144  -> 810000000
dqfma2109 fma  9 900000000     0e+6144  -> 8100000000
dqfma2110 fma  9 9000000000     0e+6144  -> 81000000000
dqfma2111 fma  9 90000000000     0e+6144  -> 810000000000
dqfma2112 fma  9 900000000000     0e+6144  -> 8100000000000
dqfma2113 fma  9 9000000000000     0e+6144  -> 81000000000000
dqfma2114 fma  9 90000000000000     0e+6144  -> 810000000000000
dqfma2115 fma  9 900000000000000     0e+6144  -> 8100000000000000
--dqfma2116 fma  9 9000000000000000     0e+6144  -> 81000000000000000
--dqfma2117 fma  9 90000000000000000     0e+6144  -> 810000000000000000
--dqfma2118 fma  9 900000000000000000     0e+6144  -> 8100000000000000000
--dqfma2119 fma  9 9000000000000000000     0e+6144  -> 81000000000000000000
--dqfma2120 fma  9 90000000000000000000     0e+6144  -> 810000000000000000000
--dqfma2121 fma  9 900000000000000000000     0e+6144  -> 8100000000000000000000
--dqfma2122 fma  9 9000000000000000000000     0e+6144  -> 81000000000000000000000
--dqfma2123 fma  9 90000000000000000000000     0e+6144  -> 810000000000000000000000
-- test some more edge cases without carries
dqfma2131 fma  3 3     0e+6144  -> 9
dqfma2132 fma  3 30     0e+6144  -> 90
dqfma2133 fma  3 300     0e+6144  -> 900
dqfma2134 fma  3 3000     0e+6144  -> 9000
dqfma2135 fma  3 30000     0e+6144  -> 90000
dqfma2136 fma  3 300000     0e+6144  -> 900000
dqfma2137 fma  3 3000000     0e+6144  -> 9000000
dqfma2138 fma  3 30000000     0e+6144  -> 90000000
dqfma2139 fma  3 300000000     0e+6144  -> 900000000
dqfma2140 fma  3 3000000000     0e+6144  -> 9000000000
dqfma2141 fma  3 30000000000     0e+6144  -> 90000000000
dqfma2142 fma  3 300000000000     0e+6144  -> 900000000000
dqfma2143 fma  3 3000000000000     0e+6144  -> 9000000000000
dqfma2144 fma  3 30000000000000     0e+6144  -> 90000000000000
dqfma2145 fma  3 300000000000000     0e+6144  -> 900000000000000
dqfma2146 fma  3 3000000000000000     0e+6144  -> 9000000000000000
dqfma2147 fma  3 30000000000000000     0e+6144  -> 90000000000000000
dqfma2148 fma  3 300000000000000000     0e+6144  -> 900000000000000000
dqfma2149 fma  3 3000000000000000000     0e+6144  -> 9000000000000000000
dqfma2150 fma  3 30000000000000000000     0e+6144  -> 90000000000000000000
dqfma2151 fma  3 300000000000000000000     0e+6144  -> 900000000000000000000
dqfma2152 fma  3 3000000000000000000000     0e+6144  -> 9000000000000000000000
dqfma2153 fma  3 30000000000000000000000     0e+6144  -> 90000000000000000000000

dqfma2263 fma  30269.587755640502150977251770554 4.8046009735990873395936309640543   0e+6144  -> 145433.2908011933696719165119928296 Inexact Rounded

-- test some edge cases with exact rounding
dqfma2301 fma  900000000000000000 9     0e+6144  -> 8100000000000000000
dqfma2302 fma  900000000000000000 90     0e+6144  -> 81000000000000000000
dqfma2303 fma  900000000000000000 900     0e+6144  -> 810000000000000000000
dqfma2304 fma  900000000000000000 9000     0e+6144  -> 8100000000000000000000
dqfma2305 fma  900000000000000000 90000     0e+6144  -> 81000000000000000000000
dqfma2306 fma  900000000000000000 900000     0e+6144  -> 810000000000000000000000
dqfma2307 fma  900000000000000000 9000000     0e+6144  -> 8100000000000000000000000
dqfma2308 fma  900000000000000000 90000000     0e+6144  -> 81000000000000000000000000
dqfma2309 fma  900000000000000000 900000000     0e+6144  -> 810000000000000000000000000
dqfma2310 fma  900000000000000000 9000000000     0e+6144  -> 8100000000000000000000000000
dqfma2311 fma  900000000000000000 90000000000     0e+6144  -> 81000000000000000000000000000
dqfma2312 fma  900000000000000000 900000000000     0e+6144  -> 810000000000000000000000000000
dqfma2313 fma  900000000000000000 9000000000000     0e+6144  -> 8100000000000000000000000000000
dqfma2314 fma  900000000000000000 90000000000000     0e+6144  -> 81000000000000000000000000000000
dqfma2315 fma  900000000000000000 900000000000000     0e+6144  -> 810000000000000000000000000000000
dqfma2316 fma  900000000000000000 9000000000000000     0e+6144  -> 8100000000000000000000000000000000
dqfma2317 fma  9000000000000000000 9000000000000000     0e+6144  -> 8.100000000000000000000000000000000E+34  Rounded
dqfma2318 fma  90000000000000000000 9000000000000000     0e+6144  -> 8.100000000000000000000000000000000E+35  Rounded
dqfma2319 fma  900000000000000000000 9000000000000000     0e+6144  -> 8.100000000000000000000000000000000E+36  Rounded
dqfma2320 fma  9000000000000000000000 9000000000000000     0e+6144  -> 8.100000000000000000000000000000000E+37  Rounded
dqfma2321 fma  90000000000000000000000 9000000000000000     0e+6144  -> 8.100000000000000000000000000000000E+38  Rounded
dqfma2322 fma  900000000000000000000000 9000000000000000     0e+6144  -> 8.100000000000000000000000000000000E+39  Rounded
dqfma2323 fma  9000000000000000000000000 9000000000000000     0e+6144  -> 8.100000000000000000000000000000000E+40  Rounded

-- tryzeros cases
dqfma2504  fma   0E-4260 1000E-4260    0e+6144  -> 0E-6176 Clamped
dqfma2505  fma   100E+4260 0E+4260     0e+6144  -> 0E+6111 Clamped

-- mixed with zeros
dqfma2541 fma   0    -1       0e+6144  ->  0
dqfma2542 fma  -0    -1       0e+6144  ->  0
dqfma2543 fma   0     1       0e+6144  ->  0
dqfma2544 fma  -0     1       0e+6144  ->  0
dqfma2545 fma  -1     0       0e+6144  ->  0
dqfma2546 fma  -1    -0       0e+6144  ->  0
dqfma2547 fma   1     0       0e+6144  ->  0
dqfma2548 fma   1    -0       0e+6144  ->  0

dqfma2551 fma   0.0  -1       0e+6144  ->  0.0
dqfma2552 fma  -0.0  -1       0e+6144  ->  0.0
dqfma2553 fma   0.0   1       0e+6144  ->  0.0
dqfma2554 fma  -0.0   1       0e+6144  ->  0.0
dqfma2555 fma  -1.0   0       0e+6144  ->  0.0
dqfma2556 fma  -1.0  -0       0e+6144  ->  0.0
dqfma2557 fma   1.0   0       0e+6144  ->  0.0
dqfma2558 fma   1.0  -0       0e+6144  ->  0.0

dqfma2561 fma   0    -1.0     0e+6144  ->  0.0
dqfma2562 fma  -0    -1.0     0e+6144  ->  0.0
dqfma2563 fma   0     1.0     0e+6144  ->  0.0
dqfma2564 fma  -0     1.0     0e+6144  ->  0.0
dqfma2565 fma  -1     0.0     0e+6144  ->  0.0
dqfma2566 fma  -1    -0.0     0e+6144  ->  0.0
dqfma2567 fma   1     0.0     0e+6144  ->  0.0
dqfma2568 fma   1    -0.0     0e+6144  ->  0.0

dqfma2571 fma   0.0  -1.0     0e+6144  ->  0.00
dqfma2572 fma  -0.0  -1.0     0e+6144  ->  0.00
dqfma2573 fma   0.0   1.0     0e+6144  ->  0.00
dqfma2574 fma  -0.0   1.0     0e+6144  ->  0.00
dqfma2575 fma  -1.0   0.0     0e+6144  ->  0.00
dqfma2576 fma  -1.0  -0.0     0e+6144  ->  0.00
dqfma2577 fma   1.0   0.0     0e+6144  ->  0.00
dqfma2578 fma   1.0  -0.0     0e+6144  ->  0.00
dqfma2579 fma   1.0   0.0     0e+6144  ->  0.00
dqfma2530 fma  -1.0  -0.0     0e+6144  ->  0.00
dqfma2531 fma  -1.0   0.0     0e+6144  ->  0.00
dqfma2532 fma   1.0  -0.0    -0e+6144  -> -0.00
dqfma2533 fma   1.0   0.0    -0e+6144  ->  0.00
dqfma2534 fma  -1.0  -0.0    -0e+6144  ->  0.00
dqfma2535 fma  -1.0   0.0    -0e+6144  -> -0.00


-- Specials
dqfma2580 fma   Inf  -Inf     0e+6144  -> -Infinity
dqfma2581 fma   Inf  -1000    0e+6144  -> -Infinity
dqfma2582 fma   Inf  -1       0e+6144  -> -Infinity
dqfma2583 fma   Inf  -0       0e+6144  ->  NaN  Invalid_operation
dqfma2584 fma   Inf   0       0e+6144  ->  NaN  Invalid_operation
dqfma2585 fma   Inf   1       0e+6144  ->  Infinity
dqfma2586 fma   Inf   1000    0e+6144  ->  Infinity
dqfma2587 fma   Inf   Inf     0e+6144  ->  Infinity
dqfma2588 fma  -1000  Inf     0e+6144  -> -Infinity
dqfma2589 fma  -Inf   Inf     0e+6144  -> -Infinity
dqfma2590 fma  -1     Inf     0e+6144  -> -Infinity
dqfma2591 fma  -0     Inf     0e+6144  ->  NaN  Invalid_operation
dqfma2592 fma   0     Inf     0e+6144  ->  NaN  Invalid_operation
dqfma2593 fma   1     Inf     0e+6144  ->  Infinity
dqfma2594 fma   1000  Inf     0e+6144  ->  Infinity
dqfma2595 fma   Inf   Inf     0e+6144  ->  Infinity

dqfma2600 fma  -Inf  -Inf     0e+6144  ->  Infinity
dqfma2601 fma  -Inf  -1000    0e+6144  ->  Infinity
dqfma2602 fma  -Inf  -1       0e+6144  ->  Infinity
dqfma2603 fma  -Inf  -0       0e+6144  ->  NaN  Invalid_operation
dqfma2604 fma  -Inf   0       0e+6144  ->  NaN  Invalid_operation
dqfma2605 fma  -Inf   1       0e+6144  -> -Infinity
dqfma2606 fma  -Inf   1000    0e+6144  -> -Infinity
dqfma2607 fma  -Inf   Inf     0e+6144  -> -Infinity
dqfma2608 fma  -1000  Inf     0e+6144  -> -Infinity
dqfma2609 fma  -Inf  -Inf     0e+6144  ->  Infinity
dqfma2610 fma  -1    -Inf     0e+6144  ->  Infinity
dqfma2611 fma  -0    -Inf     0e+6144  ->  NaN  Invalid_operation
dqfma2612 fma   0    -Inf     0e+6144  ->  NaN  Invalid_operation
dqfma2613 fma   1    -Inf     0e+6144  -> -Infinity
dqfma2614 fma   1000 -Inf     0e+6144  -> -Infinity
dqfma2615 fma   Inf  -Inf     0e+6144  -> -Infinity

dqfma2621 fma   NaN -Inf      0e+6144  ->  NaN
dqfma2622 fma   NaN -1000     0e+6144  ->  NaN
dqfma2623 fma   NaN -1        0e+6144  ->  NaN
dqfma2624 fma   NaN -0        0e+6144  ->  NaN
dqfma2625 fma   NaN  0        0e+6144  ->  NaN
dqfma2626 fma   NaN  1        0e+6144  ->  NaN
dqfma2627 fma   NaN  1000     0e+6144  ->  NaN
dqfma2628 fma   NaN  Inf      0e+6144  ->  NaN
dqfma2629 fma   NaN  NaN      0e+6144  ->  NaN
dqfma2630 fma  -Inf  NaN      0e+6144  ->  NaN
dqfma2631 fma  -1000 NaN      0e+6144  ->  NaN
dqfma2632 fma  -1    NaN      0e+6144  ->  NaN
dqfma2633 fma  -0    NaN      0e+6144  ->  NaN
dqfma2634 fma   0    NaN      0e+6144  ->  NaN
dqfma2635 fma   1    NaN      0e+6144  ->  NaN
dqfma2636 fma   1000 NaN      0e+6144  ->  NaN
dqfma2637 fma   Inf  NaN      0e+6144  ->  NaN

dqfma2641 fma   sNaN -Inf     0e+6144  ->  NaN  Invalid_operation
dqfma2642 fma   sNaN -1000    0e+6144  ->  NaN  Invalid_operation
dqfma2643 fma   sNaN -1       0e+6144  ->  NaN  Invalid_operation
dqfma2644 fma   sNaN -0       0e+6144  ->  NaN  Invalid_operation
dqfma2645 fma   sNaN  0       0e+6144  ->  NaN  Invalid_operation
dqfma2646 fma   sNaN  1       0e+6144  ->  NaN  Invalid_operation
dqfma2647 fma   sNaN  1000    0e+6144  ->  NaN  Invalid_operation
dqfma2648 fma   sNaN  NaN     0e+6144  ->  NaN  Invalid_operation
dqfma2649 fma   sNaN sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2650 fma   NaN  sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2651 fma  -Inf  sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2652 fma  -1000 sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2653 fma  -1    sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2654 fma  -0    sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2655 fma   0    sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2656 fma   1    sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2657 fma   1000 sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2658 fma   Inf  sNaN     0e+6144  ->  NaN  Invalid_operation
dqfma2659 fma   NaN  sNaN     0e+6144  ->  NaN  Invalid_operation

-- propagating NaNs
dqfma2661 fma   NaN9 -Inf     0e+6144  ->  NaN9
dqfma2662 fma   NaN8  999     0e+6144  ->  NaN8
dqfma2663 fma   NaN71 Inf     0e+6144  ->  NaN71
dqfma2664 fma   NaN6  NaN5    0e+6144  ->  NaN6
dqfma2665 fma  -Inf   NaN4    0e+6144  ->  NaN4
dqfma2666 fma  -999   NaN33   0e+6144  ->  NaN33
dqfma2667 fma   Inf   NaN2    0e+6144  ->  NaN2

dqfma2671 fma   sNaN99 -Inf      0e+6144  ->  NaN99 Invalid_operation
dqfma2672 fma   sNaN98 -11       0e+6144  ->  NaN98 Invalid_operation
dqfma2673 fma   sNaN97  NaN      0e+6144  ->  NaN97 Invalid_operation
dqfma2674 fma   sNaN16 sNaN94    0e+6144  ->  NaN16 Invalid_operation
dqfma2675 fma   NaN95  sNaN93    0e+6144  ->  NaN93 Invalid_operation
dqfma2676 fma  -Inf    sNaN92    0e+6144  ->  NaN92 Invalid_operation
dqfma2677 fma   088    sNaN91    0e+6144  ->  NaN91 Invalid_operation
dqfma2678 fma   Inf    sNaN90    0e+6144  ->  NaN90 Invalid_operation
dqfma2679 fma   NaN    sNaN89    0e+6144  ->  NaN89 Invalid_operation

dqfma2681 fma  -NaN9 -Inf     0e+6144  -> -NaN9
dqfma2682 fma  -NaN8  999     0e+6144  -> -NaN8
dqfma2683 fma  -NaN71 Inf     0e+6144  -> -NaN71
dqfma2684 fma  -NaN6 -NaN5    0e+6144  -> -NaN6
dqfma2685 fma  -Inf  -NaN4    0e+6144  -> -NaN4
dqfma2686 fma  -999  -NaN33   0e+6144  -> -NaN33
dqfma2687 fma   Inf  -NaN2    0e+6144  -> -NaN2

dqfma2691 fma  -sNaN99 -Inf      0e+6144  -> -NaN99 Invalid_operation
dqfma2692 fma  -sNaN98 -11       0e+6144  -> -NaN98 Invalid_operation
dqfma2693 fma  -sNaN97  NaN      0e+6144  -> -NaN97 Invalid_operation
dqfma2694 fma  -sNaN16 -sNaN94   0e+6144  -> -NaN16 Invalid_operation
dqfma2695 fma  -NaN95  -sNaN93   0e+6144  -> -NaN93 Invalid_operation
dqfma2696 fma  -Inf    -sNaN92   0e+6144  -> -NaN92 Invalid_operation
dqfma2697 fma   088    -sNaN91   0e+6144  -> -NaN91 Invalid_operation
dqfma2698 fma   Inf    -sNaN90   0e+6144  -> -NaN90 Invalid_operation
dqfma2699 fma  -NaN    -sNaN89   0e+6144  -> -NaN89 Invalid_operation

dqfma2701 fma  -NaN  -Inf     0e+6144  -> -NaN
dqfma2702 fma  -NaN   999     0e+6144  -> -NaN
dqfma2703 fma  -NaN   Inf     0e+6144  -> -NaN
dqfma2704 fma  -NaN  -NaN     0e+6144  -> -NaN
dqfma2705 fma  -Inf  -NaN0    0e+6144  -> -NaN
dqfma2706 fma  -999  -NaN     0e+6144  -> -NaN
dqfma2707 fma   Inf  -NaN     0e+6144  -> -NaN

dqfma2711 fma  -sNaN   -Inf      0e+6144  -> -NaN Invalid_operation
dqfma2712 fma  -sNaN   -11       0e+6144  -> -NaN Invalid_operation
dqfma2713 fma  -sNaN00  NaN      0e+6144  -> -NaN Invalid_operation
dqfma2714 fma  -sNaN   -sNaN     0e+6144  -> -NaN Invalid_operation
dqfma2715 fma  -NaN    -sNaN     0e+6144  -> -NaN Invalid_operation
dqfma2716 fma  -Inf    -sNaN     0e+6144  -> -NaN Invalid_operation
dqfma2717 fma   088    -sNaN     0e+6144  -> -NaN Invalid_operation
dqfma2718 fma   Inf    -sNaN     0e+6144  -> -NaN Invalid_operation
dqfma2719 fma  -NaN    -sNaN     0e+6144  -> -NaN Invalid_operation

-- overflow and underflow tests .. note subnormal results
-- signs
dqfma2751 fma   1e+4277  1e+3311   0e+6144  ->  Infinity Overflow Inexact Rounded
dqfma2752 fma   1e+4277 -1e+3311   0e+6144  -> -Infinity Overflow Inexact Rounded
dqfma2753 fma  -1e+4277  1e+3311   0e+6144  -> -Infinity Overflow Inexact Rounded
dqfma2754 fma  -1e+4277 -1e+3311   0e+6144  ->  Infinity Overflow Inexact Rounded
dqfma2755 fma   1e-4277  1e-3311   0e+6144  ->  0E-6176 Underflow Subnormal Inexact Rounded Clamped
dqfma2756 fma   1e-4277 -1e-3311   0e+6144  -> -0E-6176 Underflow Subnormal Inexact Rounded Clamped
dqfma2757 fma  -1e-4277  1e-3311   0e+6144  -> -0E-6176 Underflow Subnormal Inexact Rounded Clamped
dqfma2758 fma  -1e-4277 -1e-3311   0e+6144  ->  0E-6176 Underflow Subnormal Inexact Rounded Clamped

-- 'subnormal' boundary (all hard underflow or overflow in base arithmetic)
dqfma2760 fma  1e-6069 1e-101   0e+6144  -> 1E-6170 Subnormal
dqfma2761 fma  1e-6069 1e-102   0e+6144  -> 1E-6171 Subnormal
dqfma2762 fma  1e-6069 1e-103   0e+6144  -> 1E-6172 Subnormal
dqfma2763 fma  1e-6069 1e-104   0e+6144  -> 1E-6173 Subnormal
dqfma2764 fma  1e-6069 1e-105   0e+6144  -> 1E-6174 Subnormal
dqfma2765 fma  1e-6069 1e-106   0e+6144  -> 1E-6175 Subnormal
dqfma2766 fma  1e-6069 1e-107   0e+6144  -> 1E-6176 Subnormal
dqfma2767 fma  1e-6069 1e-108   0e+6144  -> 0E-6176 Underflow Subnormal Inexact Rounded Clamped
dqfma2768 fma  1e-6069 1e-109   0e+6144  -> 0E-6176 Underflow Subnormal Inexact Rounded Clamped
dqfma2769 fma  1e-6069 1e-110   0e+6144  -> 0E-6176 Underflow Subnormal Inexact Rounded Clamped
-- [no equivalent of 'subnormal' for overflow]
dqfma2770 fma  1e+40 1e+6101   0e+6144  -> 1.000000000000000000000000000000E+6141 Clamped
dqfma2771 fma  1e+40 1e+6102   0e+6144  -> 1.0000000000000000000000000000000E+6142  Clamped
dqfma2772 fma  1e+40 1e+6103   0e+6144  -> 1.00000000000000000000000000000000E+6143  Clamped
dqfma2773 fma  1e+40 1e+6104   0e+6144  -> 1.000000000000000000000000000000000E+6144  Clamped
dqfma2774 fma  1e+40 1e+6105   0e+6144  -> Infinity Overflow Inexact Rounded
dqfma2775 fma  1e+40 1e+6106   0e+6144  -> Infinity Overflow Inexact Rounded
dqfma2776 fma  1e+40 1e+6107   0e+6144  -> Infinity Overflow Inexact Rounded
dqfma2777 fma  1e+40 1e+6108   0e+6144  -> Infinity Overflow Inexact Rounded
dqfma2778 fma  1e+40 1e+6109   0e+6144  -> Infinity Overflow Inexact Rounded
dqfma2779 fma  1e+40 1e+6110   0e+6144  -> Infinity Overflow Inexact Rounded

dqfma2801 fma   1.0000E-6172  1       0e+6144  -> 1.0000E-6172 Subnormal
dqfma2802 fma   1.000E-6172   1e-1    0e+6144  -> 1.000E-6173  Subnormal
dqfma2803 fma   1.00E-6172    1e-2    0e+6144  -> 1.00E-6174   Subnormal
dqfma2804 fma   1.0E-6172     1e-3    0e+6144  -> 1.0E-6175    Subnormal
dqfma2805 fma   1.0E-6172     1e-4    0e+6144  -> 1E-6176     Subnormal Rounded
dqfma2806 fma   1.3E-6172     1e-4    0e+6144  -> 1E-6176     Underflow Subnormal Inexact Rounded
dqfma2807 fma   1.5E-6172     1e-4    0e+6144  -> 2E-6176     Underflow Subnormal Inexact Rounded
dqfma2808 fma   1.7E-6172     1e-4    0e+6144  -> 2E-6176     Underflow Subnormal Inexact Rounded
dqfma2809 fma   2.3E-6172     1e-4    0e+6144  -> 2E-6176     Underflow Subnormal Inexact Rounded
dqfma2810 fma   2.5E-6172     1e-4    0e+6144  -> 2E-6176     Underflow Subnormal Inexact Rounded
dqfma2811 fma   2.7E-6172     1e-4    0e+6144  -> 3E-6176     Underflow Subnormal Inexact Rounded
dqfma2812 fma   1.49E-6172    1e-4    0e+6144  -> 1E-6176     Underflow Subnormal Inexact Rounded
dqfma2813 fma   1.50E-6172    1e-4    0e+6144  -> 2E-6176     Underflow Subnormal Inexact Rounded
dqfma2814 fma   1.51E-6172    1e-4    0e+6144  -> 2E-6176     Underflow Subnormal Inexact Rounded
dqfma2815 fma   2.49E-6172    1e-4    0e+6144  -> 2E-6176     Underflow Subnormal Inexact Rounded
dqfma2816 fma   2.50E-6172    1e-4    0e+6144  -> 2E-6176     Underflow Subnormal Inexact Rounded
dqfma2817 fma   2.51E-6172    1e-4    0e+6144  -> 3E-6176     Underflow Subnormal Inexact Rounded

dqfma2818 fma   1E-6172       1e-4    0e+6144  -> 1E-6176     Subnormal
dqfma2819 fma   3E-6172       1e-5    0e+6144  -> 0E-6176     Underflow Subnormal Inexact Rounded Clamped
dqfma2820 fma   5E-6172       1e-5    0e+6144  -> 0E-6176     Underflow Subnormal Inexact Rounded Clamped
dqfma2821 fma   7E-6172       1e-5    0e+6144  -> 1E-6176     Underflow Subnormal Inexact Rounded
dqfma2822 fma   9E-6172       1e-5    0e+6144  -> 1E-6176     Underflow Subnormal Inexact Rounded
dqfma2823 fma   9.9E-6172     1e-5    0e+6144  -> 1E-6176     Underflow Subnormal Inexact Rounded

dqfma2824 fma   1E-6172      -1e-4    0e+6144  -> -1E-6176    Subnormal
dqfma2825 fma   3E-6172      -1e-5    0e+6144  -> -0E-6176    Underflow Subnormal Inexact Rounded Clamped
dqfma2826 fma  -5E-6172       1e-5    0e+6144  -> -0E-6176    Underflow Subnormal Inexact Rounded Clamped
dqfma2827 fma   7E-6172      -1e-5    0e+6144  -> -1E-6176    Underflow Subnormal Inexact Rounded
dqfma2828 fma  -9E-6172       1e-5    0e+6144  -> -1E-6176    Underflow Subnormal Inexact Rounded
dqfma2829 fma   9.9E-6172    -1e-5    0e+6144  -> -1E-6176    Underflow Subnormal Inexact Rounded
dqfma2830 fma   3.0E-6172    -1e-5    0e+6144  -> -0E-6176    Underflow Subnormal Inexact Rounded Clamped

dqfma2831 fma   1.0E-5977     1e-200   0e+6144  -> 0E-6176 Underflow Subnormal Inexact Rounded Clamped
dqfma2832 fma   1.0E-5977     1e-199   0e+6144  -> 1E-6176    Subnormal Rounded
dqfma2833 fma   1.0E-5977     1e-198   0e+6144  -> 1.0E-6175    Subnormal
dqfma2834 fma   2.0E-5977     2e-198   0e+6144  -> 4.0E-6175    Subnormal
dqfma2835 fma   4.0E-5977     4e-198   0e+6144  -> 1.60E-6174   Subnormal
dqfma2836 fma  10.0E-5977    10e-198   0e+6144  -> 1.000E-6173  Subnormal
dqfma2837 fma  30.0E-5977    30e-198   0e+6144  -> 9.000E-6173  Subnormal
dqfma2838 fma  40.0E-5982    40e-166   0e+6144  -> 1.6000E-6145 Subnormal
dqfma2839 fma  40.0E-5982    40e-165   0e+6144  -> 1.6000E-6144 Subnormal
dqfma2840 fma  40.0E-5982    40e-164   0e+6144  -> 1.6000E-6143

-- Long operand overflow may be a different path
dqfma2870 fma  100  9.999E+6143       0e+6144  ->  Infinity Inexact Overflow Rounded
dqfma2871 fma  100 -9.999E+6143       0e+6144  -> -Infinity Inexact Overflow Rounded
dqfma2872 fma       9.999E+6143 100   0e+6144  ->  Infinity Inexact Overflow Rounded
dqfma2873 fma      -9.999E+6143 100   0e+6144  -> -Infinity Inexact Overflow Rounded

-- check for double-rounded subnormals
dqfma2881 fma   1.2347E-6133 1.2347E-40    0e+6144  ->  1.524E-6173 Inexact Rounded Subnormal Underflow
dqfma2882 fma   1.234E-6133 1.234E-40      0e+6144  ->  1.523E-6173 Inexact Rounded Subnormal Underflow
dqfma2883 fma   1.23E-6133  1.23E-40       0e+6144  ->  1.513E-6173 Inexact Rounded Subnormal Underflow
dqfma2884 fma   1.2E-6133   1.2E-40        0e+6144  ->  1.44E-6173  Subnormal
dqfma2885 fma   1.2E-6133   1.2E-41        0e+6144  ->  1.44E-6174  Subnormal
dqfma2886 fma   1.2E-6133   1.2E-42        0e+6144  ->  1.4E-6175   Subnormal Inexact Rounded Underflow
dqfma2887 fma   1.2E-6133   1.3E-42        0e+6144  ->  1.6E-6175   Subnormal Inexact Rounded Underflow
dqfma2888 fma   1.3E-6133   1.3E-42        0e+6144  ->  1.7E-6175   Subnormal Inexact Rounded Underflow
dqfma2889 fma   1.3E-6133   1.3E-43        0e+6144  ->    2E-6176   Subnormal Inexact Rounded Underflow
dqfma2890 fma   1.3E-6134   1.3E-43        0e+6144  ->    0E-6176   Clamped Subnormal Inexact Rounded Underflow

dqfma2891 fma   1.2345E-39    1.234E-6133   0e+6144  ->  1.5234E-6172 Inexact Rounded Subnormal Underflow
dqfma2892 fma   1.23456E-39   1.234E-6133   0e+6144  ->  1.5234E-6172 Inexact Rounded Subnormal Underflow
dqfma2893 fma   1.2345E-40   1.234E-6133   0e+6144  ->  1.523E-6173  Inexact Rounded Subnormal Underflow
dqfma2894 fma   1.23456E-40  1.234E-6133   0e+6144  ->  1.523E-6173  Inexact Rounded Subnormal Underflow
dqfma2895 fma   1.2345E-41   1.234E-6133   0e+6144  ->  1.52E-6174   Inexact Rounded Subnormal Underflow
dqfma2896 fma   1.23456E-41  1.234E-6133   0e+6144  ->  1.52E-6174   Inexact Rounded Subnormal Underflow

-- Now explore the case where we get a normal result with Underflow
-- prove operands are exact
dqfma2906 fma   9.999999999999999999999999999999999E-6143  1                         0e+6144  -> 9.999999999999999999999999999999999E-6143
dqfma2907 fma                        1  0.09999999999999999999999999999999999       0e+6144  -> 0.09999999999999999999999999999999999
-- the next rounds to Nmin
dqfma2908 fma   9.999999999999999999999999999999999E-6143  0.09999999999999999999999999999999999       0e+6144  -> 1.000000000000000000000000000000000E-6143 Underflow Inexact Subnormal Rounded

-- hugest
dqfma2909 fma  9999999999999999999999999999999999 9999999999999999999999999999999999   0e+6144  -> 9.999999999999999999999999999999998E+67 Inexact Rounded

-- Examples from SQL proposal (Krishna Kulkarni)
precision:   34
rounding:    half_up
maxExponent: 6144
minExponent: -6143
dqfma21001  fma  130E-2  120E-2   0e+6144  -> 1.5600
dqfma21002  fma  130E-2  12E-1    0e+6144  -> 1.560
dqfma21003  fma  130E-2  1E0      0e+6144  -> 1.30
dqfma21004  fma  1E2     1E4      0e+6144  -> 1E+6

-- Null tests
dqfma2990 fma  10  #   0e+6144  -> NaN Invalid_operation
dqfma2991 fma   # 10   0e+6144  -> NaN Invalid_operation


-- ADDITION TESTS ------------------------------------------------------
rounding:    half_even

-- [first group are 'quick confidence check']
dqadd3001 fma  1  1       1       ->  2
dqadd3002 fma  1  2       3       ->  5
dqadd3003 fma  1  '5.75'  '3.3'   ->  9.05
dqadd3004 fma  1  '5'     '-3'    ->  2
dqadd3005 fma  1  '-5'    '-3'    ->  -8
dqadd3006 fma  1  '-7'    '2.5'   ->  -4.5
dqadd3007 fma  1  '0.7'   '0.3'   ->  1.0
dqadd3008 fma  1  '1.25'  '1.25'  ->  2.50
dqadd3009 fma  1  '1.23456789'  '1.00000000' -> '2.23456789'
dqadd3010 fma  1  '1.23456789'  '1.00000011' -> '2.23456800'

--             1234567890123456      1234567890123456
dqadd3011 fma  1  '0.4444444444444444444444444444444446'  '0.5555555555555555555555555555555555' -> '1.000000000000000000000000000000000' Inexact Rounded
dqadd3012 fma  1  '0.4444444444444444444444444444444445'  '0.5555555555555555555555555555555555' -> '1.000000000000000000000000000000000' Rounded
dqadd3013 fma  1  '0.4444444444444444444444444444444444'  '0.5555555555555555555555555555555555' -> '0.9999999999999999999999999999999999'
dqadd3014 fma  1    '4444444444444444444444444444444444' '0.49'   -> '4444444444444444444444444444444444' Inexact Rounded
dqadd3015 fma  1    '4444444444444444444444444444444444' '0.499'  -> '4444444444444444444444444444444444' Inexact Rounded
dqadd3016 fma  1    '4444444444444444444444444444444444' '0.4999' -> '4444444444444444444444444444444444' Inexact Rounded
dqadd3017 fma  1    '4444444444444444444444444444444444' '0.5000' -> '4444444444444444444444444444444444' Inexact Rounded
dqadd3018 fma  1    '4444444444444444444444444444444444' '0.5001' -> '4444444444444444444444444444444445' Inexact Rounded
dqadd3019 fma  1    '4444444444444444444444444444444444' '0.501'  -> '4444444444444444444444444444444445' Inexact Rounded
dqadd3020 fma  1    '4444444444444444444444444444444444' '0.51'   -> '4444444444444444444444444444444445' Inexact Rounded

dqadd3021 fma  1  0 1 -> 1
dqadd3022 fma  1  1 1 -> 2
dqadd3023 fma  1  2 1 -> 3
dqadd3024 fma  1  3 1 -> 4
dqadd3025 fma  1  4 1 -> 5
dqadd3026 fma  1  5 1 -> 6
dqadd3027 fma  1  6 1 -> 7
dqadd3028 fma  1  7 1 -> 8
dqadd3029 fma  1  8 1 -> 9
dqadd3030 fma  1  9 1 -> 10

-- some carrying effects
dqadd3031 fma  1  '0.9998'  '0.0000' -> '0.9998'
dqadd3032 fma  1  '0.9998'  '0.0001' -> '0.9999'
dqadd3033 fma  1  '0.9998'  '0.0002' -> '1.0000'
dqadd3034 fma  1  '0.9998'  '0.0003' -> '1.0001'

dqadd3035 fma  1  '70'  '10000e+34' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd3036 fma  1  '700'  '10000e+34' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd3037 fma  1  '7000'  '10000e+34' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd3038 fma  1  '70000'  '10000e+34' -> '1.000000000000000000000000000000001E+38' Inexact Rounded
dqadd3039 fma  1  '700000'  '10000e+34' -> '1.000000000000000000000000000000007E+38' Rounded

-- symmetry:
dqadd3040 fma  1  '10000e+34'  '70' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd3041 fma  1  '10000e+34'  '700' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd3042 fma  1  '10000e+34'  '7000' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd3044 fma  1  '10000e+34'  '70000' -> '1.000000000000000000000000000000001E+38' Inexact Rounded
dqadd3045 fma  1  '10000e+34'  '700000' -> '1.000000000000000000000000000000007E+38' Rounded

-- same, without rounding
dqadd3046 fma  1  '10000e+9'  '7' -> '10000000000007'
dqadd3047 fma  1  '10000e+9'  '70' -> '10000000000070'
dqadd3048 fma  1  '10000e+9'  '700' -> '10000000000700'
dqadd3049 fma  1  '10000e+9'  '7000' -> '10000000007000'
dqadd3050 fma  1  '10000e+9'  '70000' -> '10000000070000'
dqadd3051 fma  1  '10000e+9'  '700000' -> '10000000700000'
dqadd3052 fma  1  '10000e+9'  '7000000' -> '10000007000000'

-- examples from decarith
dqadd3053 fma  1  '12' '7.00' -> '19.00'
dqadd3054 fma  1  '1.3' '-1.07' -> '0.23'
dqadd3055 fma  1  '1.3' '-1.30' -> '0.00'
dqadd3056 fma  1  '1.3' '-2.07' -> '-0.77'
dqadd3057 fma  1  '1E+2' '1E+4' -> '1.01E+4'

-- leading zero preservation
dqadd3061 fma  1  1 '0.0001' -> '1.0001'
dqadd3062 fma  1  1 '0.00001' -> '1.00001'
dqadd3063 fma  1  1 '0.000001' -> '1.000001'
dqadd3064 fma  1  1 '0.0000001' -> '1.0000001'
dqadd3065 fma  1  1 '0.00000001' -> '1.00000001'

-- some funny zeros [in case of bad signum]
dqadd3070 fma  1  1  0    -> 1
dqadd3071 fma  1  1 0.    -> 1
dqadd3072 fma  1  1  .0   -> 1.0
dqadd3073 fma  1  1 0.0   -> 1.0
dqadd3074 fma  1  1 0.00  -> 1.00
dqadd3075 fma  1   0  1   -> 1
dqadd3076 fma  1  0.  1   -> 1
dqadd3077 fma  1   .0 1   -> 1.0
dqadd3078 fma  1  0.0 1   -> 1.0
dqadd3079 fma  1  0.00 1  -> 1.00

-- some carries
dqadd3080 fma  1  999999998 1  -> 999999999
dqadd3081 fma  1  999999999 1  -> 1000000000
dqadd3082 fma  1   99999999 1  -> 100000000
dqadd3083 fma  1    9999999 1  -> 10000000
dqadd3084 fma  1     999999 1  -> 1000000
dqadd3085 fma  1      99999 1  -> 100000
dqadd3086 fma  1       9999 1  -> 10000
dqadd3087 fma  1        999 1  -> 1000
dqadd3088 fma  1         99 1  -> 100
dqadd3089 fma  1          9 1  -> 10


-- more LHS swaps
dqadd3090 fma  1  '-56267E-10'   0 ->  '-0.0000056267'
dqadd3091 fma  1  '-56267E-6'    0 ->  '-0.056267'
dqadd3092 fma  1  '-56267E-5'    0 ->  '-0.56267'
dqadd3093 fma  1  '-56267E-4'    0 ->  '-5.6267'
dqadd3094 fma  1  '-56267E-3'    0 ->  '-56.267'
dqadd3095 fma  1  '-56267E-2'    0 ->  '-562.67'
dqadd3096 fma  1  '-56267E-1'    0 ->  '-5626.7'
dqadd3097 fma  1  '-56267E-0'    0 ->  '-56267'
dqadd3098 fma  1  '-5E-10'       0 ->  '-5E-10'
dqadd3099 fma  1  '-5E-7'        0 ->  '-5E-7'
dqadd3100 fma  1  '-5E-6'        0 ->  '-0.000005'
dqadd3101 fma  1  '-5E-5'        0 ->  '-0.00005'
dqadd3102 fma  1  '-5E-4'        0 ->  '-0.0005'
dqadd3103 fma  1  '-5E-1'        0 ->  '-0.5'
dqadd3104 fma  1  '-5E0'         0 ->  '-5'
dqadd3105 fma  1  '-5E1'         0 ->  '-50'
dqadd3106 fma  1  '-5E5'         0 ->  '-500000'
dqadd3107 fma  1  '-5E33'        0 ->  '-5000000000000000000000000000000000'
dqadd3108 fma  1  '-5E34'        0 ->  '-5.000000000000000000000000000000000E+34'  Rounded
dqadd3109 fma  1  '-5E35'        0 ->  '-5.000000000000000000000000000000000E+35'  Rounded
dqadd3110 fma  1  '-5E36'        0 ->  '-5.000000000000000000000000000000000E+36'  Rounded
dqadd3111 fma  1  '-5E100'       0 ->  '-5.000000000000000000000000000000000E+100' Rounded

-- more RHS swaps
dqadd3113 fma  1  0  '-56267E-10' ->  '-0.0000056267'
dqadd3114 fma  1  0  '-56267E-6'  ->  '-0.056267'
dqadd3116 fma  1  0  '-56267E-5'  ->  '-0.56267'
dqadd3117 fma  1  0  '-56267E-4'  ->  '-5.6267'
dqadd3119 fma  1  0  '-56267E-3'  ->  '-56.267'
dqadd3120 fma  1  0  '-56267E-2'  ->  '-562.67'
dqadd3121 fma  1  0  '-56267E-1'  ->  '-5626.7'
dqadd3122 fma  1  0  '-56267E-0'  ->  '-56267'
dqadd3123 fma  1  0  '-5E-10'     ->  '-5E-10'
dqadd3124 fma  1  0  '-5E-7'      ->  '-5E-7'
dqadd3125 fma  1  0  '-5E-6'      ->  '-0.000005'
dqadd3126 fma  1  0  '-5E-5'      ->  '-0.00005'
dqadd3127 fma  1  0  '-5E-4'      ->  '-0.0005'
dqadd3128 fma  1  0  '-5E-1'      ->  '-0.5'
dqadd3129 fma  1  0  '-5E0'       ->  '-5'
dqadd3130 fma  1  0  '-5E1'       ->  '-50'
dqadd3131 fma  1  0  '-5E5'       ->  '-500000'
dqadd3132 fma  1  0  '-5E33'      ->  '-5000000000000000000000000000000000'
dqadd3133 fma  1  0  '-5E34'      ->  '-5.000000000000000000000000000000000E+34'   Rounded
dqadd3134 fma  1  0  '-5E35'      ->  '-5.000000000000000000000000000000000E+35'   Rounded
dqadd3135 fma  1  0  '-5E36'      ->  '-5.000000000000000000000000000000000E+36'   Rounded
dqadd3136 fma  1  0  '-5E100'     ->  '-5.000000000000000000000000000000000E+100'  Rounded

-- related
dqadd3137 fma  1   1  '0E-39'      ->  '1.000000000000000000000000000000000'  Rounded
dqadd3138 fma  1  -1  '0E-39'      ->  '-1.000000000000000000000000000000000' Rounded
dqadd3139 fma  1  '0E-39' 1        ->  '1.000000000000000000000000000000000'  Rounded
dqadd3140 fma  1  '0E-39' -1       ->  '-1.000000000000000000000000000000000' Rounded
dqadd3141 fma  1  1E+29   0.0000   ->  '100000000000000000000000000000.0000'
dqadd3142 fma  1  1E+29   0.00000  ->  '100000000000000000000000000000.0000'  Rounded
dqadd3143 fma  1  0.000   1E+30    ->  '1000000000000000000000000000000.000'
dqadd3144 fma  1  0.0000  1E+30    ->  '1000000000000000000000000000000.000'  Rounded

-- [some of the next group are really constructor tests]
dqadd3146 fma  1  '00.0'  0       ->  '0.0'
dqadd3147 fma  1  '0.00'  0       ->  '0.00'
dqadd3148 fma  1   0      '0.00'  ->  '0.00'
dqadd3149 fma  1   0      '00.0'  ->  '0.0'
dqadd3150 fma  1  '00.0'  '0.00'  ->  '0.00'
dqadd3151 fma  1  '0.00'  '00.0'  ->  '0.00'
dqadd3152 fma  1  '3'     '.3'    ->  '3.3'
dqadd3153 fma  1  '3.'    '.3'    ->  '3.3'
dqadd3154 fma  1  '3.0'   '.3'    ->  '3.3'
dqadd3155 fma  1  '3.00'  '.3'    ->  '3.30'
dqadd3156 fma  1  '3'     '3'     ->  '6'
dqadd3157 fma  1  '3'     '+3'    ->  '6'
dqadd3158 fma  1  '3'     '-3'    ->  '0'
dqadd3159 fma  1  '0.3'   '-0.3'  ->  '0.0'
dqadd3160 fma  1  '0.03'  '-0.03' ->  '0.00'

-- try borderline precision, with carries, etc.
dqadd3161 fma  1  '1E+12' '-1'    -> '999999999999'
dqadd3162 fma  1  '1E+12'  '1.11' -> '1000000000001.11'
dqadd3163 fma  1  '1.11'  '1E+12' -> '1000000000001.11'
dqadd3164 fma  1  '-1'    '1E+12' -> '999999999999'
dqadd3165 fma  1  '7E+12' '-1'    -> '6999999999999'
dqadd3166 fma  1  '7E+12'  '1.11' -> '7000000000001.11'
dqadd3167 fma  1  '1.11'  '7E+12' -> '7000000000001.11'
dqadd3168 fma  1  '-1'    '7E+12' -> '6999999999999'

rounding: half_up
dqadd3170 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555567' -> '5.000000000000000000000000000000001' Inexact Rounded
dqadd3171 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555566' -> '5.000000000000000000000000000000001' Inexact Rounded
dqadd3172 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555565' -> '5.000000000000000000000000000000001' Inexact Rounded
dqadd3173 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555564' -> '5.000000000000000000000000000000000' Inexact Rounded
dqadd3174 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555553' -> '4.999999999999999999999999999999999' Inexact Rounded
dqadd3175 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555552' -> '4.999999999999999999999999999999999' Inexact Rounded
dqadd3176 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555551' -> '4.999999999999999999999999999999999' Inexact Rounded
dqadd3177 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555550' -> '4.999999999999999999999999999999999' Rounded
dqadd3178 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555545' -> '4.999999999999999999999999999999999' Inexact Rounded
dqadd3179 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555544' -> '4.999999999999999999999999999999998' Inexact Rounded
dqadd3180 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555543' -> '4.999999999999999999999999999999998' Inexact Rounded
dqadd3181 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555542' -> '4.999999999999999999999999999999998' Inexact Rounded
dqadd3182 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555541' -> '4.999999999999999999999999999999998' Inexact Rounded
dqadd3183 fma  1  '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555540' -> '4.999999999999999999999999999999998' Rounded

-- and some more, including residue effects and different roundings
rounding: half_up
dqadd3200 fma  1  '1231234567890123456784560123456789' 0             -> '1231234567890123456784560123456789'
dqadd3201 fma  1  '1231234567890123456784560123456789' 0.000000001   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3202 fma  1  '1231234567890123456784560123456789' 0.000001      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3203 fma  1  '1231234567890123456784560123456789' 0.1           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3204 fma  1  '1231234567890123456784560123456789' 0.4           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3205 fma  1  '1231234567890123456784560123456789' 0.49          -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3206 fma  1  '1231234567890123456784560123456789' 0.499999      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3207 fma  1  '1231234567890123456784560123456789' 0.499999999   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3208 fma  1  '1231234567890123456784560123456789' 0.5           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3209 fma  1  '1231234567890123456784560123456789' 0.500000001   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3210 fma  1  '1231234567890123456784560123456789' 0.500001      -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3211 fma  1  '1231234567890123456784560123456789' 0.51          -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3212 fma  1  '1231234567890123456784560123456789' 0.6           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3213 fma  1  '1231234567890123456784560123456789' 0.9           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3214 fma  1  '1231234567890123456784560123456789' 0.99999       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3215 fma  1  '1231234567890123456784560123456789' 0.999999999   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3216 fma  1  '1231234567890123456784560123456789' 1             -> '1231234567890123456784560123456790'
dqadd3217 fma  1  '1231234567890123456784560123456789' 1.000000001   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3218 fma  1  '1231234567890123456784560123456789' 1.00001       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3219 fma  1  '1231234567890123456784560123456789' 1.1           -> '1231234567890123456784560123456790' Inexact Rounded

rounding: half_even
dqadd3220 fma  1  '1231234567890123456784560123456789' 0             -> '1231234567890123456784560123456789'
dqadd3221 fma  1  '1231234567890123456784560123456789' 0.000000001   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3222 fma  1  '1231234567890123456784560123456789' 0.000001      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3223 fma  1  '1231234567890123456784560123456789' 0.1           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3224 fma  1  '1231234567890123456784560123456789' 0.4           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3225 fma  1  '1231234567890123456784560123456789' 0.49          -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3226 fma  1  '1231234567890123456784560123456789' 0.499999      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3227 fma  1  '1231234567890123456784560123456789' 0.499999999   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3228 fma  1  '1231234567890123456784560123456789' 0.5           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3229 fma  1  '1231234567890123456784560123456789' 0.500000001   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3230 fma  1  '1231234567890123456784560123456789' 0.500001      -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3231 fma  1  '1231234567890123456784560123456789' 0.51          -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3232 fma  1  '1231234567890123456784560123456789' 0.6           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3233 fma  1  '1231234567890123456784560123456789' 0.9           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3234 fma  1  '1231234567890123456784560123456789' 0.99999       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3235 fma  1  '1231234567890123456784560123456789' 0.999999999   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3236 fma  1  '1231234567890123456784560123456789' 1             -> '1231234567890123456784560123456790'
dqadd3237 fma  1  '1231234567890123456784560123456789' 1.00000001    -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3238 fma  1  '1231234567890123456784560123456789' 1.00001       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3239 fma  1  '1231234567890123456784560123456789' 1.1           -> '1231234567890123456784560123456790' Inexact Rounded
-- critical few with even bottom digit...
dqadd3240 fma  1  '1231234567890123456784560123456788' 0.499999999   -> '1231234567890123456784560123456788' Inexact Rounded
dqadd3241 fma  1  '1231234567890123456784560123456788' 0.5           -> '1231234567890123456784560123456788' Inexact Rounded
dqadd3242 fma  1  '1231234567890123456784560123456788' 0.500000001   -> '1231234567890123456784560123456789' Inexact Rounded

rounding: down
dqadd3250 fma  1  '1231234567890123456784560123456789' 0             -> '1231234567890123456784560123456789'
dqadd3251 fma  1  '1231234567890123456784560123456789' 0.000000001   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3252 fma  1  '1231234567890123456784560123456789' 0.000001      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3253 fma  1  '1231234567890123456784560123456789' 0.1           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3254 fma  1  '1231234567890123456784560123456789' 0.4           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3255 fma  1  '1231234567890123456784560123456789' 0.49          -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3256 fma  1  '1231234567890123456784560123456789' 0.499999      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3257 fma  1  '1231234567890123456784560123456789' 0.499999999   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3258 fma  1  '1231234567890123456784560123456789' 0.5           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3259 fma  1  '1231234567890123456784560123456789' 0.500000001   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3260 fma  1  '1231234567890123456784560123456789' 0.500001      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3261 fma  1  '1231234567890123456784560123456789' 0.51          -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3262 fma  1  '1231234567890123456784560123456789' 0.6           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3263 fma  1  '1231234567890123456784560123456789' 0.9           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3264 fma  1  '1231234567890123456784560123456789' 0.99999       -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3265 fma  1  '1231234567890123456784560123456789' 0.999999999   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd3266 fma  1  '1231234567890123456784560123456789' 1             -> '1231234567890123456784560123456790'
dqadd3267 fma  1  '1231234567890123456784560123456789' 1.00000001    -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3268 fma  1  '1231234567890123456784560123456789' 1.00001       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd3269 fma  1  '1231234567890123456784560123456789' 1.1           -> '1231234567890123456784560123456790' Inexact Rounded

-- 1 in last place tests
rounding: half_up
dqadd3301 fma  1   -1   1      ->   0
dqadd3302 fma  1    0   1      ->   1
dqadd3303 fma  1    1   1      ->   2
dqadd3304 fma  1   12   1      ->  13
dqadd3305 fma  1   98   1      ->  99
dqadd3306 fma  1   99   1      -> 100
dqadd3307 fma  1  100   1      -> 101
dqadd3308 fma  1  101   1      -> 102
dqadd3309 fma  1   -1  -1      ->  -2
dqadd3310 fma  1    0  -1      ->  -1
dqadd3311 fma  1    1  -1      ->   0
dqadd3312 fma  1   12  -1      ->  11
dqadd3313 fma  1   98  -1      ->  97
dqadd3314 fma  1   99  -1      ->  98
dqadd3315 fma  1  100  -1      ->  99
dqadd3316 fma  1  101  -1      -> 100

dqadd3321 fma  1  -0.01  0.01    ->  0.00
dqadd3322 fma  1   0.00  0.01    ->  0.01
dqadd3323 fma  1   0.01  0.01    ->  0.02
dqadd3324 fma  1   0.12  0.01    ->  0.13
dqadd3325 fma  1   0.98  0.01    ->  0.99
dqadd3326 fma  1   0.99  0.01    ->  1.00
dqadd3327 fma  1   1.00  0.01    ->  1.01
dqadd3328 fma  1   1.01  0.01    ->  1.02
dqadd3329 fma  1  -0.01 -0.01    -> -0.02
dqadd3330 fma  1   0.00 -0.01    -> -0.01
dqadd3331 fma  1   0.01 -0.01    ->  0.00
dqadd3332 fma  1   0.12 -0.01    ->  0.11
dqadd3333 fma  1   0.98 -0.01    ->  0.97
dqadd3334 fma  1   0.99 -0.01    ->  0.98
dqadd3335 fma  1   1.00 -0.01    ->  0.99
dqadd3336 fma  1   1.01 -0.01    ->  1.00

-- some more cases where adding 0 affects the coefficient
dqadd3340 fma  1  1E+3    0    ->         1000
dqadd3341 fma  1  1E+33   0    ->    1000000000000000000000000000000000
dqadd3342 fma  1  1E+34   0    ->   1.000000000000000000000000000000000E+34  Rounded
dqadd3343 fma  1  1E+35   0    ->   1.000000000000000000000000000000000E+35  Rounded
-- which simply follow from these cases ...
dqadd3344 fma  1  1E+3    1    ->         1001
dqadd3345 fma  1  1E+33   1    ->    1000000000000000000000000000000001
dqadd3346 fma  1  1E+34   1    ->   1.000000000000000000000000000000000E+34  Inexact Rounded
dqadd3347 fma  1  1E+35   1    ->   1.000000000000000000000000000000000E+35  Inexact Rounded
dqadd3348 fma  1  1E+3    7    ->         1007
dqadd3349 fma  1  1E+33   7    ->    1000000000000000000000000000000007
dqadd3350 fma  1  1E+34   7    ->   1.000000000000000000000000000000001E+34  Inexact Rounded
dqadd3351 fma  1  1E+35   7    ->   1.000000000000000000000000000000000E+35  Inexact Rounded

-- tryzeros cases
rounding:    half_up
dqadd3360  fma  1  0E+50 10000E+1  -> 1.0000E+5
dqadd3361  fma  1  0E-50 10000E+1  -> 100000.0000000000000000000000000000 Rounded
dqadd3362  fma  1  10000E+1 0E-50  -> 100000.0000000000000000000000000000 Rounded
dqadd3363  fma  1  10000E+1 10000E-50  -> 100000.0000000000000000000000000000 Rounded Inexact
dqadd3364  fma  1  9.999999999999999999999999999999999E+6144 -9.999999999999999999999999999999999E+6144 -> 0E+6111
--            1 234567890123456789012345678901234

-- a curiosity from JSR 13 testing
rounding:    half_down
dqadd3370 fma  1   999999999999999999999999999999999 815 -> 1000000000000000000000000000000814
dqadd3371 fma  1  9999999999999999999999999999999999 815 -> 1.000000000000000000000000000000081E+34 Rounded Inexact
rounding:    half_up
dqadd3372 fma  1   999999999999999999999999999999999 815 -> 1000000000000000000000000000000814
dqadd3373 fma  1  9999999999999999999999999999999999 815 -> 1.000000000000000000000000000000081E+34 Rounded Inexact
rounding:    half_even
dqadd3374 fma  1   999999999999999999999999999999999 815 -> 1000000000000000000000000000000814
dqadd3375 fma  1  9999999999999999999999999999999999 815 -> 1.000000000000000000000000000000081E+34 Rounded Inexact

-- ulp replacement tests
dqadd3400 fma  1    1   77e-32      ->  1.00000000000000000000000000000077
dqadd3401 fma  1    1   77e-33      ->  1.000000000000000000000000000000077
dqadd3402 fma  1    1   77e-34      ->  1.000000000000000000000000000000008 Inexact Rounded
dqadd3403 fma  1    1   77e-35      ->  1.000000000000000000000000000000001 Inexact Rounded
dqadd3404 fma  1    1   77e-36      ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd3405 fma  1    1   77e-37      ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd3406 fma  1    1   77e-299     ->  1.000000000000000000000000000000000 Inexact Rounded

dqadd3410 fma  1   10   77e-32      ->  10.00000000000000000000000000000077
dqadd3411 fma  1   10   77e-33      ->  10.00000000000000000000000000000008 Inexact Rounded
dqadd3412 fma  1   10   77e-34      ->  10.00000000000000000000000000000001 Inexact Rounded
dqadd3413 fma  1   10   77e-35      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd3414 fma  1   10   77e-36      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd3415 fma  1   10   77e-37      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd3416 fma  1   10   77e-299     ->  10.00000000000000000000000000000000 Inexact Rounded

dqadd3420 fma  1   77e-32       1   ->  1.00000000000000000000000000000077
dqadd3421 fma  1   77e-33       1   ->  1.000000000000000000000000000000077
dqadd3422 fma  1   77e-34       1   ->  1.000000000000000000000000000000008 Inexact Rounded
dqadd3423 fma  1   77e-35       1   ->  1.000000000000000000000000000000001 Inexact Rounded
dqadd3424 fma  1   77e-36       1   ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd3425 fma  1   77e-37       1   ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd3426 fma  1   77e-299      1   ->  1.000000000000000000000000000000000 Inexact Rounded

dqadd3430 fma  1   77e-32      10   ->  10.00000000000000000000000000000077
dqadd3431 fma  1   77e-33      10   ->  10.00000000000000000000000000000008 Inexact Rounded
dqadd3432 fma  1   77e-34      10   ->  10.00000000000000000000000000000001 Inexact Rounded
dqadd3433 fma  1   77e-35      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd3434 fma  1   77e-36      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd3435 fma  1   77e-37      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd3436 fma  1   77e-299     10   ->  10.00000000000000000000000000000000 Inexact Rounded

-- negative ulps
dqadd36440 fma  1    1   -77e-32      ->  0.99999999999999999999999999999923
dqadd36441 fma  1    1   -77e-33      ->  0.999999999999999999999999999999923
dqadd36442 fma  1    1   -77e-34      ->  0.9999999999999999999999999999999923
dqadd36443 fma  1    1   -77e-35      ->  0.9999999999999999999999999999999992 Inexact Rounded
dqadd36444 fma  1    1   -77e-36      ->  0.9999999999999999999999999999999999 Inexact Rounded
dqadd36445 fma  1    1   -77e-37      ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd36446 fma  1    1   -77e-99      ->  1.000000000000000000000000000000000 Inexact Rounded

dqadd36450 fma  1   10   -77e-32      ->   9.99999999999999999999999999999923
dqadd36451 fma  1   10   -77e-33      ->   9.999999999999999999999999999999923
dqadd36452 fma  1   10   -77e-34      ->   9.999999999999999999999999999999992 Inexact Rounded
dqadd36453 fma  1   10   -77e-35      ->   9.999999999999999999999999999999999 Inexact Rounded
dqadd36454 fma  1   10   -77e-36      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd36455 fma  1   10   -77e-37      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd36456 fma  1   10   -77e-99      ->  10.00000000000000000000000000000000 Inexact Rounded

dqadd36460 fma  1   -77e-32       1   ->  0.99999999999999999999999999999923
dqadd36461 fma  1   -77e-33       1   ->  0.999999999999999999999999999999923
dqadd36462 fma  1   -77e-34       1   ->  0.9999999999999999999999999999999923
dqadd36463 fma  1   -77e-35       1   ->  0.9999999999999999999999999999999992 Inexact Rounded
dqadd36464 fma  1   -77e-36       1   ->  0.9999999999999999999999999999999999 Inexact Rounded
dqadd36465 fma  1   -77e-37       1   ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd36466 fma  1   -77e-99       1   ->  1.000000000000000000000000000000000 Inexact Rounded

dqadd36470 fma  1   -77e-32      10   ->   9.99999999999999999999999999999923
dqadd36471 fma  1   -77e-33      10   ->   9.999999999999999999999999999999923
dqadd36472 fma  1   -77e-34      10   ->   9.999999999999999999999999999999992 Inexact Rounded
dqadd36473 fma  1   -77e-35      10   ->   9.999999999999999999999999999999999 Inexact Rounded
dqadd36474 fma  1   -77e-36      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd36475 fma  1   -77e-37      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd36476 fma  1   -77e-99      10   ->  10.00000000000000000000000000000000 Inexact Rounded

-- negative ulps
dqadd36480 fma  1   -1    77e-32      ->  -0.99999999999999999999999999999923
dqadd36481 fma  1   -1    77e-33      ->  -0.999999999999999999999999999999923
dqadd36482 fma  1   -1    77e-34      ->  -0.9999999999999999999999999999999923
dqadd36483 fma  1   -1    77e-35      ->  -0.9999999999999999999999999999999992 Inexact Rounded
dqadd36484 fma  1   -1    77e-36      ->  -0.9999999999999999999999999999999999 Inexact Rounded
dqadd36485 fma  1   -1    77e-37      ->  -1.000000000000000000000000000000000 Inexact Rounded
dqadd36486 fma  1   -1    77e-99      ->  -1.000000000000000000000000000000000 Inexact Rounded

dqadd36490 fma  1  -10    77e-32      ->   -9.99999999999999999999999999999923
dqadd36491 fma  1  -10    77e-33      ->   -9.999999999999999999999999999999923
dqadd36492 fma  1  -10    77e-34      ->   -9.999999999999999999999999999999992 Inexact Rounded
dqadd36493 fma  1  -10    77e-35      ->   -9.999999999999999999999999999999999 Inexact Rounded
dqadd36494 fma  1  -10    77e-36      ->  -10.00000000000000000000000000000000 Inexact Rounded
dqadd36495 fma  1  -10    77e-37      ->  -10.00000000000000000000000000000000 Inexact Rounded
dqadd36496 fma  1  -10    77e-99      ->  -10.00000000000000000000000000000000 Inexact Rounded

dqadd36500 fma  1    77e-32      -1   ->  -0.99999999999999999999999999999923
dqadd36501 fma  1    77e-33      -1   ->  -0.999999999999999999999999999999923
dqadd36502 fma  1    77e-34      -1   ->  -0.9999999999999999999999999999999923
dqadd36503 fma  1    77e-35      -1   ->  -0.9999999999999999999999999999999992 Inexact Rounded
dqadd36504 fma  1    77e-36      -1   ->  -0.9999999999999999999999999999999999 Inexact Rounded
dqadd36505 fma  1    77e-37      -1   ->  -1.000000000000000000000000000000000 Inexact Rounded
dqadd36506 fma  1    77e-99      -1   ->  -1.000000000000000000000000000000000 Inexact Rounded

dqadd36510 fma  1    77e-32      -10  ->   -9.99999999999999999999999999999923
dqadd36511 fma  1    77e-33      -10  ->   -9.999999999999999999999999999999923
dqadd36512 fma  1    77e-34      -10  ->   -9.999999999999999999999999999999992 Inexact Rounded
dqadd36513 fma  1    77e-35      -10  ->   -9.999999999999999999999999999999999 Inexact Rounded
dqadd36514 fma  1    77e-36      -10  ->  -10.00000000000000000000000000000000 Inexact Rounded
dqadd36515 fma  1    77e-37      -10  ->  -10.00000000000000000000000000000000 Inexact Rounded
dqadd36516 fma  1    77e-99      -10  ->  -10.00000000000000000000000000000000 Inexact Rounded

-- and some more residue effects and different roundings
rounding: half_up
dqadd36540 fma  1  '9876543219876543216543210123456789' 0             -> '9876543219876543216543210123456789'
dqadd36541 fma  1  '9876543219876543216543210123456789' 0.000000001   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36542 fma  1  '9876543219876543216543210123456789' 0.000001      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36543 fma  1  '9876543219876543216543210123456789' 0.1           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36544 fma  1  '9876543219876543216543210123456789' 0.4           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36545 fma  1  '9876543219876543216543210123456789' 0.49          -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36546 fma  1  '9876543219876543216543210123456789' 0.499999      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36547 fma  1  '9876543219876543216543210123456789' 0.499999999   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36548 fma  1  '9876543219876543216543210123456789' 0.5           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36549 fma  1  '9876543219876543216543210123456789' 0.500000001   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36550 fma  1  '9876543219876543216543210123456789' 0.500001      -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36551 fma  1  '9876543219876543216543210123456789' 0.51          -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36552 fma  1  '9876543219876543216543210123456789' 0.6           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36553 fma  1  '9876543219876543216543210123456789' 0.9           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36554 fma  1  '9876543219876543216543210123456789' 0.99999       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36555 fma  1  '9876543219876543216543210123456789' 0.999999999   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36556 fma  1  '9876543219876543216543210123456789' 1             -> '9876543219876543216543210123456790'
dqadd36557 fma  1  '9876543219876543216543210123456789' 1.000000001   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36558 fma  1  '9876543219876543216543210123456789' 1.00001       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36559 fma  1  '9876543219876543216543210123456789' 1.1           -> '9876543219876543216543210123456790' Inexact Rounded

rounding: half_even
dqadd36560 fma  1  '9876543219876543216543210123456789' 0             -> '9876543219876543216543210123456789'
dqadd36561 fma  1  '9876543219876543216543210123456789' 0.000000001   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36562 fma  1  '9876543219876543216543210123456789' 0.000001      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36563 fma  1  '9876543219876543216543210123456789' 0.1           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36564 fma  1  '9876543219876543216543210123456789' 0.4           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36565 fma  1  '9876543219876543216543210123456789' 0.49          -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36566 fma  1  '9876543219876543216543210123456789' 0.499999      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36567 fma  1  '9876543219876543216543210123456789' 0.499999999   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd36568 fma  1  '9876543219876543216543210123456789' 0.5           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36569 fma  1  '9876543219876543216543210123456789' 0.500000001   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36570 fma  1  '9876543219876543216543210123456789' 0.500001      -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36571 fma  1  '9876543219876543216543210123456789' 0.51          -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36572 fma  1  '9876543219876543216543210123456789' 0.6           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36573 fma  1  '9876543219876543216543210123456789' 0.9           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36574 fma  1  '9876543219876543216543210123456789' 0.99999       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36575 fma  1  '9876543219876543216543210123456789' 0.999999999   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36576 fma  1  '9876543219876543216543210123456789' 1             -> '9876543219876543216543210123456790'
dqadd36577 fma  1  '9876543219876543216543210123456789' 1.00000001    -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36578 fma  1  '9876543219876543216543210123456789' 1.00001       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd36579 fma  1  '9876543219876543216543210123456789' 1.1           -> '9876543219876543216543210123456790' Inexact Rounded

-- critical few with even bottom digit...
dqadd37540 fma  1  '9876543219876543216543210123456788' 0.499999999   -> '9876543219876543216543210123456788' Inexact Rounded
dqadd37541 fma  1  '9876543219876543216543210123456788' 0.5           -> '9876543219876543216543210123456788' Inexact Rounded
dqadd37542 fma  1  '9876543219876543216543210123456788' 0.500000001   -> '9876543219876543216543210123456789' Inexact Rounded

rounding: down
dqadd37550 fma  1  '9876543219876543216543210123456789' 0             -> '9876543219876543216543210123456789'
dqadd37551 fma  1  '9876543219876543216543210123456789' 0.000000001   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37552 fma  1  '9876543219876543216543210123456789' 0.000001      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37553 fma  1  '9876543219876543216543210123456789' 0.1           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37554 fma  1  '9876543219876543216543210123456789' 0.4           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37555 fma  1  '9876543219876543216543210123456789' 0.49          -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37556 fma  1  '9876543219876543216543210123456789' 0.499999      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37557 fma  1  '9876543219876543216543210123456789' 0.499999999   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37558 fma  1  '9876543219876543216543210123456789' 0.5           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37559 fma  1  '9876543219876543216543210123456789' 0.500000001   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37560 fma  1  '9876543219876543216543210123456789' 0.500001      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37561 fma  1  '9876543219876543216543210123456789' 0.51          -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37562 fma  1  '9876543219876543216543210123456789' 0.6           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37563 fma  1  '9876543219876543216543210123456789' 0.9           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37564 fma  1  '9876543219876543216543210123456789' 0.99999       -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37565 fma  1  '9876543219876543216543210123456789' 0.999999999   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd37566 fma  1  '9876543219876543216543210123456789' 1             -> '9876543219876543216543210123456790'
dqadd37567 fma  1  '9876543219876543216543210123456789' 1.00000001    -> '9876543219876543216543210123456790' Inexact Rounded
dqadd37568 fma  1  '9876543219876543216543210123456789' 1.00001       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd37569 fma  1  '9876543219876543216543210123456789' 1.1           -> '9876543219876543216543210123456790' Inexact Rounded

-- more zeros, etc.
rounding: half_even

dqadd37701 fma  1  5.00 1.00E-3 -> 5.00100
dqadd37702 fma  1  00.00 0.000  -> 0.000
dqadd37703 fma  1  00.00 0E-3   -> 0.000
dqadd37704 fma  1  0E-3  00.00  -> 0.000

dqadd37710 fma  1  0E+3  00.00  -> 0.00
dqadd37711 fma  1  0E+3  00.0   -> 0.0
dqadd37712 fma  1  0E+3  00.    -> 0
dqadd37713 fma  1  0E+3  00.E+1 -> 0E+1
dqadd37714 fma  1  0E+3  00.E+2 -> 0E+2
dqadd37715 fma  1  0E+3  00.E+3 -> 0E+3
dqadd37716 fma  1  0E+3  00.E+4 -> 0E+3
dqadd37717 fma  1  0E+3  00.E+5 -> 0E+3
dqadd37718 fma  1  0E+3  -00.0   -> 0.0
dqadd37719 fma  1  0E+3  -00.    -> 0
dqadd37731 fma  1  0E+3  -00.E+1 -> 0E+1

dqadd37720 fma  1  00.00  0E+3  -> 0.00
dqadd37721 fma  1  00.0   0E+3  -> 0.0
dqadd37722 fma  1  00.    0E+3  -> 0
dqadd37723 fma  1  00.E+1 0E+3  -> 0E+1
dqadd37724 fma  1  00.E+2 0E+3  -> 0E+2
dqadd37725 fma  1  00.E+3 0E+3  -> 0E+3
dqadd37726 fma  1  00.E+4 0E+3  -> 0E+3
dqadd37727 fma  1  00.E+5 0E+3  -> 0E+3
dqadd37728 fma  1  -00.00 0E+3  -> 0.00
dqadd37729 fma  1  -00.0  0E+3  -> 0.0
dqadd37730 fma  1  -00.   0E+3  -> 0

dqadd37732 fma  1   0     0     ->  0
dqadd37733 fma  1   0    -0     ->  0
dqadd37734 fma  1  -0     0     ->  0
dqadd37735 fma  1  -0    -0     -> -0     -- IEEE 854 special case

dqadd37736 fma  1   1    -1     ->  0
dqadd37737 fma  1  -1    -1     -> -2
dqadd37738 fma  1   1     1     ->  2
dqadd37739 fma  1  -1     1     ->  0

dqadd37741 fma  1   0    -1     -> -1
dqadd37742 fma  1  -0    -1     -> -1
dqadd37743 fma  1   0     1     ->  1
dqadd37744 fma  1  -0     1     ->  1
dqadd37745 fma  1  -1     0     -> -1
dqadd37746 fma  1  -1    -0     -> -1
dqadd37747 fma  1   1     0     ->  1
dqadd37748 fma  1   1    -0     ->  1

dqadd37751 fma  1   0.0  -1     -> -1.0
dqadd37752 fma  1  -0.0  -1     -> -1.0
dqadd37753 fma  1   0.0   1     ->  1.0
dqadd37754 fma  1  -0.0   1     ->  1.0
dqadd37755 fma  1  -1.0   0     -> -1.0
dqadd37756 fma  1  -1.0  -0     -> -1.0
dqadd37757 fma  1   1.0   0     ->  1.0
dqadd37758 fma  1   1.0  -0     ->  1.0

dqadd37761 fma  1   0    -1.0   -> -1.0
dqadd37762 fma  1  -0    -1.0   -> -1.0
dqadd37763 fma  1   0     1.0   ->  1.0
dqadd37764 fma  1  -0     1.0   ->  1.0
dqadd37765 fma  1  -1     0.0   -> -1.0
dqadd37766 fma  1  -1    -0.0   -> -1.0
dqadd37767 fma  1   1     0.0   ->  1.0
dqadd37768 fma  1   1    -0.0   ->  1.0

dqadd37771 fma  1   0.0  -1.0   -> -1.0
dqadd37772 fma  1  -0.0  -1.0   -> -1.0
dqadd37773 fma  1   0.0   1.0   ->  1.0
dqadd37774 fma  1  -0.0   1.0   ->  1.0
dqadd37775 fma  1  -1.0   0.0   -> -1.0
dqadd37776 fma  1  -1.0  -0.0   -> -1.0
dqadd37777 fma  1   1.0   0.0   ->  1.0
dqadd37778 fma  1   1.0  -0.0   ->  1.0

-- Specials
dqadd37780 fma  1  -Inf  -Inf   -> -Infinity
dqadd37781 fma  1  -Inf  -1000  -> -Infinity
dqadd37782 fma  1  -Inf  -1     -> -Infinity
dqadd37783 fma  1  -Inf  -0     -> -Infinity
dqadd37784 fma  1  -Inf   0     -> -Infinity
dqadd37785 fma  1  -Inf   1     -> -Infinity
dqadd37786 fma  1  -Inf   1000  -> -Infinity
dqadd37787 fma  1  -1000 -Inf   -> -Infinity
dqadd37788 fma  1  -Inf  -Inf   -> -Infinity
dqadd37789 fma  1  -1    -Inf   -> -Infinity
dqadd37790 fma  1  -0    -Inf   -> -Infinity
dqadd37791 fma  1   0    -Inf   -> -Infinity
dqadd37792 fma  1   1    -Inf   -> -Infinity
dqadd37793 fma  1   1000 -Inf   -> -Infinity
dqadd37794 fma  1   Inf  -Inf   ->  NaN  Invalid_operation

dqadd37800 fma  1   Inf  -Inf   ->  NaN  Invalid_operation
dqadd37801 fma  1   Inf  -1000  ->  Infinity
dqadd37802 fma  1   Inf  -1     ->  Infinity
dqadd37803 fma  1   Inf  -0     ->  Infinity
dqadd37804 fma  1   Inf   0     ->  Infinity
dqadd37805 fma  1   Inf   1     ->  Infinity
dqadd37806 fma  1   Inf   1000  ->  Infinity
dqadd37807 fma  1   Inf   Inf   ->  Infinity
dqadd37808 fma  1  -1000  Inf   ->  Infinity
dqadd37809 fma  1  -Inf   Inf   ->  NaN  Invalid_operation
dqadd37810 fma  1  -1     Inf   ->  Infinity
dqadd37811 fma  1  -0     Inf   ->  Infinity
dqadd37812 fma  1   0     Inf   ->  Infinity
dqadd37813 fma  1   1     Inf   ->  Infinity
dqadd37814 fma  1   1000  Inf   ->  Infinity
dqadd37815 fma  1   Inf   Inf   ->  Infinity

dqadd37821 fma  1   NaN -Inf    ->  NaN
dqadd37822 fma  1   NaN -1000   ->  NaN
dqadd37823 fma  1   NaN -1      ->  NaN
dqadd37824 fma  1   NaN -0      ->  NaN
dqadd37825 fma  1   NaN  0      ->  NaN
dqadd37826 fma  1   NaN  1      ->  NaN
dqadd37827 fma  1   NaN  1000   ->  NaN
dqadd37828 fma  1   NaN  Inf    ->  NaN
dqadd37829 fma  1   NaN  NaN    ->  NaN
dqadd37830 fma  1  -Inf  NaN    ->  NaN
dqadd37831 fma  1  -1000 NaN    ->  NaN
dqadd37832 fma  1  -1    NaN    ->  NaN
dqadd37833 fma  1  -0    NaN    ->  NaN
dqadd37834 fma  1   0    NaN    ->  NaN
dqadd37835 fma  1   1    NaN    ->  NaN
dqadd37836 fma  1   1000 NaN    ->  NaN
dqadd37837 fma  1   Inf  NaN    ->  NaN

dqadd37841 fma  1   sNaN -Inf   ->  NaN  Invalid_operation
dqadd37842 fma  1   sNaN -1000  ->  NaN  Invalid_operation
dqadd37843 fma  1   sNaN -1     ->  NaN  Invalid_operation
dqadd37844 fma  1   sNaN -0     ->  NaN  Invalid_operation
dqadd37845 fma  1   sNaN  0     ->  NaN  Invalid_operation
dqadd37846 fma  1   sNaN  1     ->  NaN  Invalid_operation
dqadd37847 fma  1   sNaN  1000  ->  NaN  Invalid_operation
dqadd37848 fma  1   sNaN  NaN   ->  NaN  Invalid_operation
dqadd37849 fma  1   sNaN sNaN   ->  NaN  Invalid_operation
dqadd37850 fma  1   NaN  sNaN   ->  NaN  Invalid_operation
dqadd37851 fma  1  -Inf  sNaN   ->  NaN  Invalid_operation
dqadd37852 fma  1  -1000 sNaN   ->  NaN  Invalid_operation
dqadd37853 fma  1  -1    sNaN   ->  NaN  Invalid_operation
dqadd37854 fma  1  -0    sNaN   ->  NaN  Invalid_operation
dqadd37855 fma  1   0    sNaN   ->  NaN  Invalid_operation
dqadd37856 fma  1   1    sNaN   ->  NaN  Invalid_operation
dqadd37857 fma  1   1000 sNaN   ->  NaN  Invalid_operation
dqadd37858 fma  1   Inf  sNaN   ->  NaN  Invalid_operation
dqadd37859 fma  1   NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dqadd37861 fma  1   NaN1   -Inf    ->  NaN1
dqadd37862 fma  1  +NaN2   -1000   ->  NaN2
dqadd37863 fma  1   NaN3    1000   ->  NaN3
dqadd37864 fma  1   NaN4    Inf    ->  NaN4
dqadd37865 fma  1   NaN5   +NaN6   ->  NaN5
dqadd37866 fma  1  -Inf     NaN7   ->  NaN7
dqadd37867 fma  1  -1000    NaN8   ->  NaN8
dqadd37868 fma  1   1000    NaN9   ->  NaN9
dqadd37869 fma  1   Inf    +NaN10  ->  NaN10
dqadd37871 fma  1   sNaN11  -Inf   ->  NaN11  Invalid_operation
dqadd37872 fma  1   sNaN12  -1000  ->  NaN12  Invalid_operation
dqadd37873 fma  1   sNaN13   1000  ->  NaN13  Invalid_operation
dqadd37874 fma  1   sNaN14   NaN17 ->  NaN14  Invalid_operation
dqadd37875 fma  1   sNaN15  sNaN18 ->  NaN15  Invalid_operation
dqadd37876 fma  1   NaN16   sNaN19 ->  NaN19  Invalid_operation
dqadd37877 fma  1  -Inf    +sNaN20 ->  NaN20  Invalid_operation
dqadd37878 fma  1  -1000    sNaN21 ->  NaN21  Invalid_operation
dqadd37879 fma  1   1000    sNaN22 ->  NaN22  Invalid_operation
dqadd37880 fma  1   Inf     sNaN23 ->  NaN23  Invalid_operation
dqadd37881 fma  1  +NaN25  +sNaN24 ->  NaN24  Invalid_operation
dqadd37882 fma  1  -NaN26    NaN28 -> -NaN26
dqadd37883 fma  1  -sNaN27  sNaN29 -> -NaN27  Invalid_operation
dqadd37884 fma  1   1000    -NaN30 -> -NaN30
dqadd37885 fma  1   1000   -sNaN31 -> -NaN31  Invalid_operation

-- Here we explore near the boundary of rounding a subnormal to Nmin
dqadd37575 fma  1   1E-6143 -1E-6176 ->  9.99999999999999999999999999999999E-6144 Subnormal
dqadd37576 fma  1  -1E-6143 +1E-6176 -> -9.99999999999999999999999999999999E-6144 Subnormal

-- check overflow edge case
--               1234567890123456
dqadd37972 apply       9.999999999999999999999999999999999E+6144         -> 9.999999999999999999999999999999999E+6144
dqadd37973 fma  1      9.999999999999999999999999999999999E+6144  1      -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37974 fma  1       9999999999999999999999999999999999E+6111  1      -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37975 fma  1       9999999999999999999999999999999999E+6111  1E+6111  -> Infinity Overflow Inexact Rounded
dqadd37976 fma  1       9999999999999999999999999999999999E+6111  9E+6110  -> Infinity Overflow Inexact Rounded
dqadd37977 fma  1       9999999999999999999999999999999999E+6111  8E+6110  -> Infinity Overflow Inexact Rounded
dqadd37978 fma  1       9999999999999999999999999999999999E+6111  7E+6110  -> Infinity Overflow Inexact Rounded
dqadd37979 fma  1       9999999999999999999999999999999999E+6111  6E+6110  -> Infinity Overflow Inexact Rounded
dqadd37980 fma  1       9999999999999999999999999999999999E+6111  5E+6110  -> Infinity Overflow Inexact Rounded
dqadd37981 fma  1       9999999999999999999999999999999999E+6111  4E+6110  -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37982 fma  1       9999999999999999999999999999999999E+6111  3E+6110  -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37983 fma  1       9999999999999999999999999999999999E+6111  2E+6110  -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37984 fma  1       9999999999999999999999999999999999E+6111  1E+6110  -> 9.999999999999999999999999999999999E+6144 Inexact Rounded

dqadd37985 apply      -9.999999999999999999999999999999999E+6144         -> -9.999999999999999999999999999999999E+6144
dqadd37986 fma  1     -9.999999999999999999999999999999999E+6144 -1      -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37987 fma  1      -9999999999999999999999999999999999E+6111 -1      -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37988 fma  1      -9999999999999999999999999999999999E+6111 -1E+6111  -> -Infinity Overflow Inexact Rounded
dqadd37989 fma  1      -9999999999999999999999999999999999E+6111 -9E+6110  -> -Infinity Overflow Inexact Rounded
dqadd37990 fma  1      -9999999999999999999999999999999999E+6111 -8E+6110  -> -Infinity Overflow Inexact Rounded
dqadd37991 fma  1      -9999999999999999999999999999999999E+6111 -7E+6110  -> -Infinity Overflow Inexact Rounded
dqadd37992 fma  1      -9999999999999999999999999999999999E+6111 -6E+6110  -> -Infinity Overflow Inexact Rounded
dqadd37993 fma  1      -9999999999999999999999999999999999E+6111 -5E+6110  -> -Infinity Overflow Inexact Rounded
dqadd37994 fma  1      -9999999999999999999999999999999999E+6111 -4E+6110  -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37995 fma  1      -9999999999999999999999999999999999E+6111 -3E+6110  -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37996 fma  1      -9999999999999999999999999999999999E+6111 -2E+6110  -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd37997 fma  1      -9999999999999999999999999999999999E+6111 -1E+6110  -> -9.999999999999999999999999999999999E+6144 Inexact Rounded

-- And for round down full and subnormal results
rounding:     down
dqadd371100 fma  1  1e+2 -1e-6143    -> 99.99999999999999999999999999999999 Rounded Inexact
dqadd371101 fma  1  1e+1 -1e-6143    -> 9.999999999999999999999999999999999  Rounded Inexact
dqadd371103 fma  1    +1 -1e-6143    -> 0.9999999999999999999999999999999999  Rounded Inexact
dqadd371104 fma  1  1e-1 -1e-6143    -> 0.09999999999999999999999999999999999  Rounded Inexact
dqadd371105 fma  1  1e-2 -1e-6143    -> 0.009999999999999999999999999999999999  Rounded Inexact
dqadd371106 fma  1  1e-3 -1e-6143    -> 0.0009999999999999999999999999999999999  Rounded Inexact
dqadd371107 fma  1  1e-4 -1e-6143    -> 0.00009999999999999999999999999999999999  Rounded Inexact
dqadd371108 fma  1  1e-5 -1e-6143    -> 0.000009999999999999999999999999999999999  Rounded Inexact
dqadd371109 fma  1  1e-6 -1e-6143    -> 9.999999999999999999999999999999999E-7  Rounded Inexact

rounding:     ceiling
dqadd371110 fma  1  -1e+2 +1e-6143   -> -99.99999999999999999999999999999999 Rounded Inexact
dqadd371111 fma  1  -1e+1 +1e-6143   -> -9.999999999999999999999999999999999  Rounded Inexact
dqadd371113 fma  1     -1 +1e-6143   -> -0.9999999999999999999999999999999999  Rounded Inexact
dqadd371114 fma  1  -1e-1 +1e-6143   -> -0.09999999999999999999999999999999999  Rounded Inexact
dqadd371115 fma  1  -1e-2 +1e-6143   -> -0.009999999999999999999999999999999999  Rounded Inexact
dqadd371116 fma  1  -1e-3 +1e-6143   -> -0.0009999999999999999999999999999999999  Rounded Inexact
dqadd371117 fma  1  -1e-4 +1e-6143   -> -0.00009999999999999999999999999999999999  Rounded Inexact
dqadd371118 fma  1  -1e-5 +1e-6143   -> -0.000009999999999999999999999999999999999  Rounded Inexact
dqadd371119 fma  1  -1e-6 +1e-6143   -> -9.999999999999999999999999999999999E-7  Rounded Inexact

-- tests based on Gunnar Degnbol's edge case
rounding:     half_even

dqadd371300 fma  1  1E34  -0.5                 ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371310 fma  1  1E34  -0.51                ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371311 fma  1  1E34  -0.501               ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371312 fma  1  1E34  -0.5001              ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371313 fma  1  1E34  -0.50001             ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371314 fma  1  1E34  -0.500001            ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371315 fma  1  1E34  -0.5000001           ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371316 fma  1  1E34  -0.50000001          ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371317 fma  1  1E34  -0.500000001         ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371318 fma  1  1E34  -0.5000000001        ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371319 fma  1  1E34  -0.50000000001       ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371320 fma  1  1E34  -0.500000000001      ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371321 fma  1  1E34  -0.5000000000001     ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371322 fma  1  1E34  -0.50000000000001    ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371323 fma  1  1E34  -0.500000000000001   ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371324 fma  1  1E34  -0.5000000000000001  ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371325 fma  1  1E34  -0.5000000000000000  ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371326 fma  1  1E34  -0.500000000000000   ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371327 fma  1  1E34  -0.50000000000000    ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371328 fma  1  1E34  -0.5000000000000     ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371329 fma  1  1E34  -0.500000000000      ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371330 fma  1  1E34  -0.50000000000       ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371331 fma  1  1E34  -0.5000000000        ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371332 fma  1  1E34  -0.500000000         ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371333 fma  1  1E34  -0.50000000          ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371334 fma  1  1E34  -0.5000000           ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371335 fma  1  1E34  -0.500000            ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371336 fma  1  1E34  -0.50000             ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371337 fma  1  1E34  -0.5000              ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371338 fma  1  1E34  -0.500               ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371339 fma  1  1E34  -0.50                ->  1.000000000000000000000000000000000E+34 Inexact Rounded

dqadd371340 fma  1  1E34  -5000000.000010001   ->  9999999999999999999999999995000000      Inexact Rounded
dqadd371341 fma  1  1E34  -5000000.000000001   ->  9999999999999999999999999995000000      Inexact Rounded

dqadd371349 fma  1  9999999999999999999999999999999999 0.4                 ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371350 fma  1  9999999999999999999999999999999999 0.49                ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371351 fma  1  9999999999999999999999999999999999 0.499               ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371352 fma  1  9999999999999999999999999999999999 0.4999              ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371353 fma  1  9999999999999999999999999999999999 0.49999             ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371354 fma  1  9999999999999999999999999999999999 0.499999            ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371355 fma  1  9999999999999999999999999999999999 0.4999999           ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371356 fma  1  9999999999999999999999999999999999 0.49999999          ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371357 fma  1  9999999999999999999999999999999999 0.499999999         ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371358 fma  1  9999999999999999999999999999999999 0.4999999999        ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371359 fma  1  9999999999999999999999999999999999 0.49999999999       ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371360 fma  1  9999999999999999999999999999999999 0.499999999999      ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371361 fma  1  9999999999999999999999999999999999 0.4999999999999     ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371362 fma  1  9999999999999999999999999999999999 0.49999999999999    ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371363 fma  1  9999999999999999999999999999999999 0.499999999999999   ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371364 fma  1  9999999999999999999999999999999999 0.4999999999999999  ->  9999999999999999999999999999999999      Inexact Rounded
dqadd371365 fma  1  9999999999999999999999999999999999 0.5000000000000000  ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371367 fma  1  9999999999999999999999999999999999 0.500000000000000   ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371368 fma  1  9999999999999999999999999999999999 0.50000000000000    ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371369 fma  1  9999999999999999999999999999999999 0.5000000000000     ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371370 fma  1  9999999999999999999999999999999999 0.500000000000      ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371371 fma  1  9999999999999999999999999999999999 0.50000000000       ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371372 fma  1  9999999999999999999999999999999999 0.5000000000        ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371373 fma  1  9999999999999999999999999999999999 0.500000000         ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371374 fma  1  9999999999999999999999999999999999 0.50000000          ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371375 fma  1  9999999999999999999999999999999999 0.5000000           ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371376 fma  1  9999999999999999999999999999999999 0.500000            ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371377 fma  1  9999999999999999999999999999999999 0.50000             ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371378 fma  1  9999999999999999999999999999999999 0.5000              ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371379 fma  1  9999999999999999999999999999999999 0.500               ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371380 fma  1  9999999999999999999999999999999999 0.50                ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371381 fma  1  9999999999999999999999999999999999 0.5                 ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371382 fma  1  9999999999999999999999999999999999 0.5000000000000001  ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371383 fma  1  9999999999999999999999999999999999 0.500000000000001   ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371384 fma  1  9999999999999999999999999999999999 0.50000000000001    ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371385 fma  1  9999999999999999999999999999999999 0.5000000000001     ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371386 fma  1  9999999999999999999999999999999999 0.500000000001      ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371387 fma  1  9999999999999999999999999999999999 0.50000000001       ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371388 fma  1  9999999999999999999999999999999999 0.5000000001        ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371389 fma  1  9999999999999999999999999999999999 0.500000001         ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371390 fma  1  9999999999999999999999999999999999 0.50000001          ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371391 fma  1  9999999999999999999999999999999999 0.5000001           ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371392 fma  1  9999999999999999999999999999999999 0.500001            ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371393 fma  1  9999999999999999999999999999999999 0.50001             ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371394 fma  1  9999999999999999999999999999999999 0.5001              ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371395 fma  1  9999999999999999999999999999999999 0.501               ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd371396 fma  1  9999999999999999999999999999999999 0.51                ->  1.000000000000000000000000000000000E+34 Inexact Rounded

-- More GD edge cases, where difference between the unadjusted
-- exponents is larger than the maximum precision and one side is 0
dqadd371420 fma  1   0 1.123456789987654321123456789012345     -> 1.123456789987654321123456789012345
dqadd371421 fma  1   0 1.123456789987654321123456789012345E-1  -> 0.1123456789987654321123456789012345
dqadd371422 fma  1   0 1.123456789987654321123456789012345E-2  -> 0.01123456789987654321123456789012345
dqadd371423 fma  1   0 1.123456789987654321123456789012345E-3  -> 0.001123456789987654321123456789012345
dqadd371424 fma  1   0 1.123456789987654321123456789012345E-4  -> 0.0001123456789987654321123456789012345
dqadd371425 fma  1   0 1.123456789987654321123456789012345E-5  -> 0.00001123456789987654321123456789012345
dqadd371426 fma  1   0 1.123456789987654321123456789012345E-6  -> 0.000001123456789987654321123456789012345
dqadd371427 fma  1   0 1.123456789987654321123456789012345E-7  -> 1.123456789987654321123456789012345E-7
dqadd371428 fma  1   0 1.123456789987654321123456789012345E-8  -> 1.123456789987654321123456789012345E-8
dqadd371429 fma  1   0 1.123456789987654321123456789012345E-9  -> 1.123456789987654321123456789012345E-9
dqadd371430 fma  1   0 1.123456789987654321123456789012345E-10 -> 1.123456789987654321123456789012345E-10
dqadd371431 fma  1   0 1.123456789987654321123456789012345E-11 -> 1.123456789987654321123456789012345E-11
dqadd371432 fma  1   0 1.123456789987654321123456789012345E-12 -> 1.123456789987654321123456789012345E-12
dqadd371433 fma  1   0 1.123456789987654321123456789012345E-13 -> 1.123456789987654321123456789012345E-13
dqadd371434 fma  1   0 1.123456789987654321123456789012345E-14 -> 1.123456789987654321123456789012345E-14
dqadd371435 fma  1   0 1.123456789987654321123456789012345E-15 -> 1.123456789987654321123456789012345E-15
dqadd371436 fma  1   0 1.123456789987654321123456789012345E-16 -> 1.123456789987654321123456789012345E-16
dqadd371437 fma  1   0 1.123456789987654321123456789012345E-17 -> 1.123456789987654321123456789012345E-17
dqadd371438 fma  1   0 1.123456789987654321123456789012345E-18 -> 1.123456789987654321123456789012345E-18
dqadd371439 fma  1   0 1.123456789987654321123456789012345E-19 -> 1.123456789987654321123456789012345E-19
dqadd371440 fma  1   0 1.123456789987654321123456789012345E-20 -> 1.123456789987654321123456789012345E-20
dqadd371441 fma  1   0 1.123456789987654321123456789012345E-21 -> 1.123456789987654321123456789012345E-21
dqadd371442 fma  1   0 1.123456789987654321123456789012345E-22 -> 1.123456789987654321123456789012345E-22
dqadd371443 fma  1   0 1.123456789987654321123456789012345E-23 -> 1.123456789987654321123456789012345E-23
dqadd371444 fma  1   0 1.123456789987654321123456789012345E-24 -> 1.123456789987654321123456789012345E-24
dqadd371445 fma  1   0 1.123456789987654321123456789012345E-25 -> 1.123456789987654321123456789012345E-25
dqadd371446 fma  1   0 1.123456789987654321123456789012345E-26 -> 1.123456789987654321123456789012345E-26
dqadd371447 fma  1   0 1.123456789987654321123456789012345E-27 -> 1.123456789987654321123456789012345E-27
dqadd371448 fma  1   0 1.123456789987654321123456789012345E-28 -> 1.123456789987654321123456789012345E-28
dqadd371449 fma  1   0 1.123456789987654321123456789012345E-29 -> 1.123456789987654321123456789012345E-29
dqadd371450 fma  1   0 1.123456789987654321123456789012345E-30 -> 1.123456789987654321123456789012345E-30
dqadd371451 fma  1   0 1.123456789987654321123456789012345E-31 -> 1.123456789987654321123456789012345E-31
dqadd371452 fma  1   0 1.123456789987654321123456789012345E-32 -> 1.123456789987654321123456789012345E-32
dqadd371453 fma  1   0 1.123456789987654321123456789012345E-33 -> 1.123456789987654321123456789012345E-33
dqadd371454 fma  1   0 1.123456789987654321123456789012345E-34 -> 1.123456789987654321123456789012345E-34
dqadd371455 fma  1   0 1.123456789987654321123456789012345E-35 -> 1.123456789987654321123456789012345E-35
dqadd371456 fma  1   0 1.123456789987654321123456789012345E-36 -> 1.123456789987654321123456789012345E-36

-- same, reversed 0
dqadd371460 fma  1  1.123456789987654321123456789012345     0 -> 1.123456789987654321123456789012345
dqadd371461 fma  1  1.123456789987654321123456789012345E-1  0 -> 0.1123456789987654321123456789012345
dqadd371462 fma  1  1.123456789987654321123456789012345E-2  0 -> 0.01123456789987654321123456789012345
dqadd371463 fma  1  1.123456789987654321123456789012345E-3  0 -> 0.001123456789987654321123456789012345
dqadd371464 fma  1  1.123456789987654321123456789012345E-4  0 -> 0.0001123456789987654321123456789012345
dqadd371465 fma  1  1.123456789987654321123456789012345E-5  0 -> 0.00001123456789987654321123456789012345
dqadd371466 fma  1  1.123456789987654321123456789012345E-6  0 -> 0.000001123456789987654321123456789012345
dqadd371467 fma  1  1.123456789987654321123456789012345E-7  0 -> 1.123456789987654321123456789012345E-7
dqadd371468 fma  1  1.123456789987654321123456789012345E-8  0 -> 1.123456789987654321123456789012345E-8
dqadd371469 fma  1  1.123456789987654321123456789012345E-9  0 -> 1.123456789987654321123456789012345E-9
dqadd371470 fma  1  1.123456789987654321123456789012345E-10 0 -> 1.123456789987654321123456789012345E-10
dqadd371471 fma  1  1.123456789987654321123456789012345E-11 0 -> 1.123456789987654321123456789012345E-11
dqadd371472 fma  1  1.123456789987654321123456789012345E-12 0 -> 1.123456789987654321123456789012345E-12
dqadd371473 fma  1  1.123456789987654321123456789012345E-13 0 -> 1.123456789987654321123456789012345E-13
dqadd371474 fma  1  1.123456789987654321123456789012345E-14 0 -> 1.123456789987654321123456789012345E-14
dqadd371475 fma  1  1.123456789987654321123456789012345E-15 0 -> 1.123456789987654321123456789012345E-15
dqadd371476 fma  1  1.123456789987654321123456789012345E-16 0 -> 1.123456789987654321123456789012345E-16
dqadd371477 fma  1  1.123456789987654321123456789012345E-17 0 -> 1.123456789987654321123456789012345E-17
dqadd371478 fma  1  1.123456789987654321123456789012345E-18 0 -> 1.123456789987654321123456789012345E-18
dqadd371479 fma  1  1.123456789987654321123456789012345E-19 0 -> 1.123456789987654321123456789012345E-19
dqadd371480 fma  1  1.123456789987654321123456789012345E-20 0 -> 1.123456789987654321123456789012345E-20
dqadd371481 fma  1  1.123456789987654321123456789012345E-21 0 -> 1.123456789987654321123456789012345E-21
dqadd371482 fma  1  1.123456789987654321123456789012345E-22 0 -> 1.123456789987654321123456789012345E-22
dqadd371483 fma  1  1.123456789987654321123456789012345E-23 0 -> 1.123456789987654321123456789012345E-23
dqadd371484 fma  1  1.123456789987654321123456789012345E-24 0 -> 1.123456789987654321123456789012345E-24
dqadd371485 fma  1  1.123456789987654321123456789012345E-25 0 -> 1.123456789987654321123456789012345E-25
dqadd371486 fma  1  1.123456789987654321123456789012345E-26 0 -> 1.123456789987654321123456789012345E-26
dqadd371487 fma  1  1.123456789987654321123456789012345E-27 0 -> 1.123456789987654321123456789012345E-27
dqadd371488 fma  1  1.123456789987654321123456789012345E-28 0 -> 1.123456789987654321123456789012345E-28
dqadd371489 fma  1  1.123456789987654321123456789012345E-29 0 -> 1.123456789987654321123456789012345E-29
dqadd371490 fma  1  1.123456789987654321123456789012345E-30 0 -> 1.123456789987654321123456789012345E-30
dqadd371491 fma  1  1.123456789987654321123456789012345E-31 0 -> 1.123456789987654321123456789012345E-31
dqadd371492 fma  1  1.123456789987654321123456789012345E-32 0 -> 1.123456789987654321123456789012345E-32
dqadd371493 fma  1  1.123456789987654321123456789012345E-33 0 -> 1.123456789987654321123456789012345E-33
dqadd371494 fma  1  1.123456789987654321123456789012345E-34 0 -> 1.123456789987654321123456789012345E-34
dqadd371495 fma  1  1.123456789987654321123456789012345E-35 0 -> 1.123456789987654321123456789012345E-35
dqadd371496 fma  1  1.123456789987654321123456789012345E-36 0 -> 1.123456789987654321123456789012345E-36

-- same, Es on the 0
dqadd371500 fma  1  1.123456789987654321123456789012345  0E-0   -> 1.123456789987654321123456789012345
dqadd371501 fma  1  1.123456789987654321123456789012345  0E-1   -> 1.123456789987654321123456789012345
dqadd371502 fma  1  1.123456789987654321123456789012345  0E-2   -> 1.123456789987654321123456789012345
dqadd371503 fma  1  1.123456789987654321123456789012345  0E-3   -> 1.123456789987654321123456789012345
dqadd371504 fma  1  1.123456789987654321123456789012345  0E-4   -> 1.123456789987654321123456789012345
dqadd371505 fma  1  1.123456789987654321123456789012345  0E-5   -> 1.123456789987654321123456789012345
dqadd371506 fma  1  1.123456789987654321123456789012345  0E-6   -> 1.123456789987654321123456789012345
dqadd371507 fma  1  1.123456789987654321123456789012345  0E-7   -> 1.123456789987654321123456789012345
dqadd371508 fma  1  1.123456789987654321123456789012345  0E-8   -> 1.123456789987654321123456789012345
dqadd371509 fma  1  1.123456789987654321123456789012345  0E-9   -> 1.123456789987654321123456789012345
dqadd371510 fma  1  1.123456789987654321123456789012345  0E-10  -> 1.123456789987654321123456789012345
dqadd371511 fma  1  1.123456789987654321123456789012345  0E-11  -> 1.123456789987654321123456789012345
dqadd371512 fma  1  1.123456789987654321123456789012345  0E-12  -> 1.123456789987654321123456789012345
dqadd371513 fma  1  1.123456789987654321123456789012345  0E-13  -> 1.123456789987654321123456789012345
dqadd371514 fma  1  1.123456789987654321123456789012345  0E-14  -> 1.123456789987654321123456789012345
dqadd371515 fma  1  1.123456789987654321123456789012345  0E-15  -> 1.123456789987654321123456789012345
dqadd371516 fma  1  1.123456789987654321123456789012345  0E-16  -> 1.123456789987654321123456789012345
dqadd371517 fma  1  1.123456789987654321123456789012345  0E-17  -> 1.123456789987654321123456789012345
dqadd371518 fma  1  1.123456789987654321123456789012345  0E-18  -> 1.123456789987654321123456789012345
dqadd371519 fma  1  1.123456789987654321123456789012345  0E-19  -> 1.123456789987654321123456789012345
dqadd371520 fma  1  1.123456789987654321123456789012345  0E-20  -> 1.123456789987654321123456789012345
dqadd371521 fma  1  1.123456789987654321123456789012345  0E-21  -> 1.123456789987654321123456789012345
dqadd371522 fma  1  1.123456789987654321123456789012345  0E-22  -> 1.123456789987654321123456789012345
dqadd371523 fma  1  1.123456789987654321123456789012345  0E-23  -> 1.123456789987654321123456789012345
dqadd371524 fma  1  1.123456789987654321123456789012345  0E-24  -> 1.123456789987654321123456789012345
dqadd371525 fma  1  1.123456789987654321123456789012345  0E-25  -> 1.123456789987654321123456789012345
dqadd371526 fma  1  1.123456789987654321123456789012345  0E-26  -> 1.123456789987654321123456789012345
dqadd371527 fma  1  1.123456789987654321123456789012345  0E-27  -> 1.123456789987654321123456789012345
dqadd371528 fma  1  1.123456789987654321123456789012345  0E-28  -> 1.123456789987654321123456789012345
dqadd371529 fma  1  1.123456789987654321123456789012345  0E-29  -> 1.123456789987654321123456789012345
dqadd371530 fma  1  1.123456789987654321123456789012345  0E-30  -> 1.123456789987654321123456789012345
dqadd371531 fma  1  1.123456789987654321123456789012345  0E-31  -> 1.123456789987654321123456789012345
dqadd371532 fma  1  1.123456789987654321123456789012345  0E-32  -> 1.123456789987654321123456789012345
dqadd371533 fma  1  1.123456789987654321123456789012345  0E-33  -> 1.123456789987654321123456789012345
-- next four flag Rounded because the 0 extends the result
dqadd371534 fma  1  1.123456789987654321123456789012345  0E-34  -> 1.123456789987654321123456789012345 Rounded
dqadd371535 fma  1  1.123456789987654321123456789012345  0E-35  -> 1.123456789987654321123456789012345 Rounded
dqadd371536 fma  1  1.123456789987654321123456789012345  0E-36  -> 1.123456789987654321123456789012345 Rounded
dqadd371537 fma  1  1.123456789987654321123456789012345  0E-37  -> 1.123456789987654321123456789012345 Rounded

-- sum of two opposite-sign operands is exactly 0 and floor => -0
rounding:    half_up
-- exact zeros from zeros
dqadd371600 fma  1   0        0E-19  ->  0E-19
dqadd371601 fma  1  -0        0E-19  ->  0E-19
dqadd371602 fma  1   0       -0E-19  ->  0E-19
dqadd371603 fma  1  -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd371611 fma  1  -11      11    ->  0
dqadd371612 fma  1   11     -11    ->  0
-- overflow
dqadd371613 fma  9E6144 10   1     ->  Infinity Overflow Inexact Rounded
dqadd371614 fma -9E6144 10   1     -> -Infinity Overflow Inexact Rounded

rounding:    half_down
-- exact zeros from zeros
dqadd371620 fma  1   0        0E-19  ->  0E-19
dqadd371621 fma  1  -0        0E-19  ->  0E-19
dqadd371622 fma  1   0       -0E-19  ->  0E-19
dqadd371623 fma  1  -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd371631 fma  1  -11      11    ->  0
dqadd371632 fma  1   11     -11    ->  0
-- overflow
dqadd371633 fma  9E6144 10   1     ->  Infinity Overflow Inexact Rounded
dqadd371634 fma -9E6144 10   1     -> -Infinity Overflow Inexact Rounded

rounding:    half_even
-- exact zeros from zeros
dqadd371640 fma  1   0        0E-19  ->  0E-19
dqadd371641 fma  1  -0        0E-19  ->  0E-19
dqadd371642 fma  1   0       -0E-19  ->  0E-19
dqadd371643 fma  1  -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd371651 fma  1  -11      11    ->  0
dqadd371652 fma  1   11     -11    ->  0
-- overflow
dqadd371653 fma  9E6144 10   1     ->  Infinity Overflow Inexact Rounded
dqadd371654 fma -9E6144 10   1     -> -Infinity Overflow Inexact Rounded

rounding:    up
-- exact zeros from zeros
dqadd371660 fma  1   0        0E-19  ->  0E-19
dqadd371661 fma  1  -0        0E-19  ->  0E-19
dqadd371662 fma  1   0       -0E-19  ->  0E-19
dqadd371663 fma  1  -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd371671 fma  1  -11      11    ->  0
dqadd371672 fma  1   11     -11    ->  0
-- overflow
dqadd371673 fma  9E6144 10   1     ->  Infinity Overflow Inexact Rounded
dqadd371674 fma -9E6144 10   1     -> -Infinity Overflow Inexact Rounded

rounding:    down
-- exact zeros from zeros
dqadd371680 fma  1   0        0E-19  ->  0E-19
dqadd371681 fma  1  -0        0E-19  ->  0E-19
dqadd371682 fma  1   0       -0E-19  ->  0E-19
dqadd371683 fma  1  -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd371691 fma  1  -11      11    ->  0
dqadd371692 fma  1   11     -11    ->  0
-- overflow
dqadd371693 fma  9E6144 10   1     ->  9.999999999999999999999999999999999E+6144 Overflow Inexact Rounded
dqadd371694 fma -9E6144 10   1     -> -9.999999999999999999999999999999999E+6144 Overflow Inexact Rounded

rounding:    ceiling
-- exact zeros from zeros
dqadd371700 fma  1   0        0E-19  ->  0E-19
dqadd371701 fma  1  -0        0E-19  ->  0E-19
dqadd371702 fma  1   0       -0E-19  ->  0E-19
dqadd371703 fma  1  -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd371711 fma  1  -11      11    ->  0
dqadd371712 fma  1   11     -11    ->  0
-- overflow
dqadd371713 fma  9E6144 10   1     ->  Infinity Overflow Inexact Rounded
dqadd371714 fma -9E6144 10   1     -> -9.999999999999999999999999999999999E+6144 Overflow Inexact Rounded

-- and the extra-special ugly case; unusual minuses marked by -- *
rounding:    floor
-- exact zeros from zeros
dqadd371720 fma  1   0        0E-19  ->  0E-19
dqadd371721 fma  1  -0        0E-19  -> -0E-19           -- *
dqadd371722 fma  1   0       -0E-19  -> -0E-19           -- *
dqadd371723 fma  1  -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd371731 fma  1  -11      11    ->  -0                -- *
dqadd371732 fma  1   11     -11    ->  -0                -- *
-- overflow
dqadd371733 fma  9E6144 10   1     ->  9.999999999999999999999999999999999E+6144 Overflow Inexact Rounded
dqadd371734 fma -9E6144 10   1     -> -Infinity Overflow Inexact Rounded

rounding:    05up
-- exact zeros from zeros
dqadd371740 fma  1   0        0E-19  ->  0E-19
dqadd371741 fma  1  -0        0E-19  ->  0E-19
dqadd371742 fma  1   0       -0E-19  ->  0E-19
dqadd371743 fma  1  -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd371751 fma  1  -11      11    ->  0
dqadd371752 fma  1   11     -11    ->  0
-- overflow
dqadd371753 fma  9E6144 10   1     ->  9.999999999999999999999999999999999E+6144 Overflow Inexact Rounded
dqadd371754 fma -9E6144 10   1     -> -9.999999999999999999999999999999999E+6144 Overflow Inexact Rounded

-- Examples from SQL proposal (Krishna Kulkarni)
dqadd371761 fma  1  130E-2    120E-2    -> 2.50
dqadd371762 fma  1  130E-2    12E-1     -> 2.50
dqadd371763 fma  1  130E-2    1E0       -> 2.30
dqadd371764 fma  1  1E2       1E4       -> 1.01E+4
dqadd371765 fma  1  130E-2   -120E-2 -> 0.10
dqadd371766 fma  1  130E-2   -12E-1  -> 0.10
dqadd371767 fma  1  130E-2   -1E0    -> 0.30
dqadd371768 fma  1  1E2      -1E4    -> -9.9E+3

-- Gappy coefficients; check residue handling even with full coefficient gap
rounding: half_even

dqadd375001 fma  1  1239876543211234567894567890123456 1      -> 1239876543211234567894567890123457
dqadd375002 fma  1  1239876543211234567894567890123456 0.6    -> 1239876543211234567894567890123457  Inexact Rounded
dqadd375003 fma  1  1239876543211234567894567890123456 0.06   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375004 fma  1  1239876543211234567894567890123456 6E-3   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375005 fma  1  1239876543211234567894567890123456 6E-4   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375006 fma  1  1239876543211234567894567890123456 6E-5   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375007 fma  1  1239876543211234567894567890123456 6E-6   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375008 fma  1  1239876543211234567894567890123456 6E-7   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375009 fma  1  1239876543211234567894567890123456 6E-8   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375010 fma  1  1239876543211234567894567890123456 6E-9   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375011 fma  1  1239876543211234567894567890123456 6E-10  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375012 fma  1  1239876543211234567894567890123456 6E-11  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375013 fma  1  1239876543211234567894567890123456 6E-12  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375014 fma  1  1239876543211234567894567890123456 6E-13  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375015 fma  1  1239876543211234567894567890123456 6E-14  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375016 fma  1  1239876543211234567894567890123456 6E-15  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375017 fma  1  1239876543211234567894567890123456 6E-16  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375018 fma  1  1239876543211234567894567890123456 6E-17  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375019 fma  1  1239876543211234567894567890123456 6E-18  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375020 fma  1  1239876543211234567894567890123456 6E-19  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd375021 fma  1  1239876543211234567894567890123456 6E-20  -> 1239876543211234567894567890123456  Inexact Rounded

-- widening second argument at gap
dqadd375030 fma  1  12398765432112345678945678 1                       -> 12398765432112345678945679
dqadd375031 fma  1  12398765432112345678945678 0.1                     -> 12398765432112345678945678.1
dqadd375032 fma  1  12398765432112345678945678 0.12                    -> 12398765432112345678945678.12
dqadd375033 fma  1  12398765432112345678945678 0.123                   -> 12398765432112345678945678.123
dqadd375034 fma  1  12398765432112345678945678 0.1234                  -> 12398765432112345678945678.1234
dqadd375035 fma  1  12398765432112345678945678 0.12345                 -> 12398765432112345678945678.12345
dqadd375036 fma  1  12398765432112345678945678 0.123456                -> 12398765432112345678945678.123456
dqadd375037 fma  1  12398765432112345678945678 0.1234567               -> 12398765432112345678945678.1234567
dqadd375038 fma  1  12398765432112345678945678 0.12345678              -> 12398765432112345678945678.12345678
dqadd375039 fma  1  12398765432112345678945678 0.123456789             -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd375040 fma  1  12398765432112345678945678 0.123456785             -> 12398765432112345678945678.12345678 Inexact Rounded
dqadd375041 fma  1  12398765432112345678945678 0.1234567850            -> 12398765432112345678945678.12345678 Inexact Rounded
dqadd375042 fma  1  12398765432112345678945678 0.1234567851            -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd375043 fma  1  12398765432112345678945678 0.12345678501           -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd375044 fma  1  12398765432112345678945678 0.123456785001          -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd375045 fma  1  12398765432112345678945678 0.1234567850001         -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd375046 fma  1  12398765432112345678945678 0.12345678500001        -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd375047 fma  1  12398765432112345678945678 0.123456785000001       -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd375048 fma  1  12398765432112345678945678 0.1234567850000001      -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd375049 fma  1  12398765432112345678945678 0.1234567850000000      -> 12398765432112345678945678.12345678 Inexact Rounded
--                               90123456
rounding: half_even
dqadd375050 fma  1  12398765432112345678945678 0.0234567750000000      -> 12398765432112345678945678.02345678 Inexact Rounded
dqadd375051 fma  1  12398765432112345678945678 0.0034567750000000      -> 12398765432112345678945678.00345678 Inexact Rounded
dqadd375052 fma  1  12398765432112345678945678 0.0004567750000000      -> 12398765432112345678945678.00045678 Inexact Rounded
dqadd375053 fma  1  12398765432112345678945678 0.0000567750000000      -> 12398765432112345678945678.00005678 Inexact Rounded
dqadd375054 fma  1  12398765432112345678945678 0.0000067750000000      -> 12398765432112345678945678.00000678 Inexact Rounded
dqadd375055 fma  1  12398765432112345678945678 0.0000007750000000      -> 12398765432112345678945678.00000078 Inexact Rounded
dqadd375056 fma  1  12398765432112345678945678 0.0000000750000000      -> 12398765432112345678945678.00000008 Inexact Rounded
dqadd375057 fma  1  12398765432112345678945678 0.0000000050000000      -> 12398765432112345678945678.00000000 Inexact Rounded
dqadd375060 fma  1  12398765432112345678945678 0.0234567750000001      -> 12398765432112345678945678.02345678 Inexact Rounded
dqadd375061 fma  1  12398765432112345678945678 0.0034567750000001      -> 12398765432112345678945678.00345678 Inexact Rounded
dqadd375062 fma  1  12398765432112345678945678 0.0004567750000001      -> 12398765432112345678945678.00045678 Inexact Rounded
dqadd375063 fma  1  12398765432112345678945678 0.0000567750000001      -> 12398765432112345678945678.00005678 Inexact Rounded
dqadd375064 fma  1  12398765432112345678945678 0.0000067750000001      -> 12398765432112345678945678.00000678 Inexact Rounded
dqadd375065 fma  1  12398765432112345678945678 0.0000007750000001      -> 12398765432112345678945678.00000078 Inexact Rounded
dqadd375066 fma  1  12398765432112345678945678 0.0000000750000001      -> 12398765432112345678945678.00000008 Inexact Rounded
dqadd375067 fma  1  12398765432112345678945678 0.0000000050000001      -> 12398765432112345678945678.00000001 Inexact Rounded
-- far-out residues (full coefficient gap is 16+15 digits)
rounding: up
dqadd375070 fma  1  12398765432112345678945678 1E-8                    -> 12398765432112345678945678.00000001
dqadd375071 fma  1  12398765432112345678945678 1E-9                    -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375072 fma  1  12398765432112345678945678 1E-10                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375073 fma  1  12398765432112345678945678 1E-11                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375074 fma  1  12398765432112345678945678 1E-12                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375075 fma  1  12398765432112345678945678 1E-13                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375076 fma  1  12398765432112345678945678 1E-14                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375077 fma  1  12398765432112345678945678 1E-15                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375078 fma  1  12398765432112345678945678 1E-16                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375079 fma  1  12398765432112345678945678 1E-17                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375080 fma  1  12398765432112345678945678 1E-18                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375081 fma  1  12398765432112345678945678 1E-19                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375082 fma  1  12398765432112345678945678 1E-20                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375083 fma  1  12398765432112345678945678 1E-25                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375084 fma  1  12398765432112345678945678 1E-30                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375085 fma  1  12398765432112345678945678 1E-31                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375086 fma  1  12398765432112345678945678 1E-32                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375087 fma  1  12398765432112345678945678 1E-33                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375088 fma  1  12398765432112345678945678 1E-34                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd375089 fma  1  12398765432112345678945678 1E-35                   -> 12398765432112345678945678.00000001 Inexact Rounded

-- Destructive subtract (from remainder tests)

-- +++ some of these will be off-by-one remainder vs remainderNear

dqfma4000  fma  -1234567890123456789012345678901233   1.000000000000000000000000000000001    1234567890123456789012345678901234  ->  -0.234567890123456789012345678901233
dqfma4001  fma  -1234567890123456789012345678901222    1.00000000000000000000000000000001    1234567890123456789012345678901234  ->  -0.34567890123456789012345678901222
dqfma4002  fma  -1234567890123456789012345678901111     1.0000000000000000000000000000001    1234567890123456789012345678901234  ->  -0.4567890123456789012345678901111
dqfma4003  fma   -308641972530864197253086419725314   4.000000000000000000000000000000001    1234567890123456789012345678901255  ->  -1.308641972530864197253086419725314
dqfma4004  fma   -308641972530864197253086419725308   4.000000000000000000000000000000001    1234567890123456789012345678901234  ->  1.691358027469135802746913580274692
dqfma4005  fma   -246913578024691357802469135780252     4.9999999999999999999999999999999    1234567890123456789012345678901234  ->  -1.3086421975308642197530864219748
dqfma4006  fma   -246913578024691357802469135780247    4.99999999999999999999999999999999    1234567890123456789012345678901234  ->  1.46913578024691357802469135780247
dqfma4007  fma   -246913578024691357802469135780247   4.999999999999999999999999999999999    1234567890123456789012345678901234  ->  -0.753086421975308642197530864219753
dqfma4008  fma   -246913578024691357802469135780247   5.000000000000000000000000000000001    1234567890123456789012345678901234  ->  -1.246913578024691357802469135780247
dqfma4009  fma   -246913578024691357802469135780246    5.00000000000000000000000000000001    1234567890123456789012345678901234  ->  1.53086421975308642197530864219754
dqfma4010  fma   -246913578024691357802469135780242     5.0000000000000000000000000000001    1234567890123456789012345678901234  ->  -0.6913578024691357802469135780242
dqfma4011  fma  -1234567890123456789012345678901232   1.000000000000000000000000000000001    1234567890123456789012345678901234  ->  0.765432109876543210987654321098768
dqfma4012  fma  -1234567890123456789012345678901221    1.00000000000000000000000000000001    1234567890123456789012345678901234  ->  0.65432109876543210987654321098779
dqfma4013  fma  -1234567890123456789012345678901110     1.0000000000000000000000000000001    1234567890123456789012345678901234  ->  0.5432109876543210987654321098890
dqfma4014  fma   -308641972530864197253086419725313   4.000000000000000000000000000000001    1234567890123456789012345678901255  ->  2.691358027469135802746913580274687
dqfma4015  fma   -308641972530864197253086419725308   4.000000000000000000000000000000001    1234567890123456789012345678901234  ->  1.691358027469135802746913580274692
dqfma4016  fma   -246913578024691357802469135780251     4.9999999999999999999999999999999    1234567890123456789012345678901234  ->  3.6913578024691357802469135780251
dqfma4017  fma   -246913578024691357802469135780247    4.99999999999999999999999999999999    1234567890123456789012345678901234  ->  1.46913578024691357802469135780247
dqfma4018  fma   -246913578024691357802469135780246   4.999999999999999999999999999999999    1234567890123456789012345678901234  ->  4.246913578024691357802469135780246
dqfma4019  fma   -246913578024691357802469135780241     5.0000000000000000000000000000001    1234567890123456789012345678901234  ->  4.3086421975308642197530864219759

-- Null tests
dqadd39990 fma  1  10  # -> NaN Invalid_operation
dqadd39991 fma  1   # 10 -> NaN Invalid_operation


