<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Fragment>
        <ComponentGroup Id="exe_txt">
            <Component Id="LICENSE.txt" Directory="InstallDirectory" Guid="*">
                <File Name="LICENSE.txt" Source="LICENSE.txt" KeyPath="yes" />
            </Component>
            <Component Id="NEWS.txt" Directory="InstallDirectory" Guid="*">
                <File Name="NEWS.txt" KeyPath="yes" />
            </Component>
        </ComponentGroup>
    </Fragment>

    <Fragment>
        <PropertyRef Id="REGISTRYKEY" />
        
        <ComponentGroup Id="exe_python">
            <Component Id="python.exe" Directory="InstallDirectory" Guid="$(var.PythonExeComponentGuid)">
                <File Name="python.exe" KeyPath="yes" />
                
                <RegistryKey Root="HKMU" Key="[REGISTRYKEY]">
                    <RegistryValue Key="InstallPath" Type="string" Value="[InstallDirectory]" KeyPath="no" />
                    <RegistryValue Key="InstallPath" Name="ExecutablePath" Type="string" Value="[#python.exe]" KeyPath="no" />
                </RegistryKey>
            </Component>
            <Component Id="pythonw.exe" Directory="InstallDirectory" Guid="$(var.PythonwExeComponentGuid)">
                <File Name="pythonw.exe" KeyPath="yes" />
                <RegistryKey Root="HKMU" Key="[REGISTRYKEY]">
                    <RegistryValue Key="InstallPath" Name="WindowedExecutablePath" Type="string" Value="[#pythonw.exe]" KeyPath="no" />
                </RegistryKey>
            </Component>
            <Component Id="vcruntime140.dll" Directory="InstallDirectory" Guid="*">
                <File Name="vcruntime140.dll" Source="vcruntime140.dll" KeyPath="yes" />
            </Component>
<?ifdef Include_Vcruntime140_1_dll ?>
            <Component Id="vcruntime140_1.dll" Directory="InstallDirectory" Guid="*">
                <File Name="vcruntime140_1.dll" Source="vcruntime140_1.dll" KeyPath="yes" />
            </Component>
<?endif ?>
        </ComponentGroup>
    </Fragment>

    <Fragment>
        <ComponentGroup Id="exe_python_symbols">
            <Component Id="python.pdb" Directory="InstallDirectory" Guid="*">
                <File Name="python.pdb" />
            </Component>
            <Component Id="pythonw.pdb" Directory="InstallDirectory" Guid="*">
                <File Name="pythonw.pdb" />
            </Component>
        </ComponentGroup>
    </Fragment>
        
    <Fragment>
        <ComponentGroup Id="exe_python_d">
            <Component Id="python_d.exe" Directory="InstallDirectory" Guid="*">
                <File Name="python_d.exe" />
            </Component>
            <Component Id="python_d.pdb" Directory="InstallDirectory" Guid="*">
                <File Name="python_d.pdb" />
            </Component>
            <Component Id="pythonw_d.exe" Directory="InstallDirectory" Guid="*">
                <File Name="pythonw_d.exe" />
            </Component>
            <Component Id="pythonw_d.pdb" Directory="InstallDirectory" Guid="*">
                <File Name="pythonw_d.pdb" />
            </Component>
        </ComponentGroup>
    </Fragment>
    
    <Fragment>
        <ComponentGroup Id="exe_icons">
            <Component Id="py.ico" Directory="DLLs" Guid="*">
                <File Name="py.ico" Source="!(bindpath.src)PC\icons\py.ico" KeyPath="yes" />
            </Component>
            <Component Id="pyc.ico" Directory="DLLs" Guid="*">
                <File Name="pyc.ico" Source="!(bindpath.src)PC\icons\pyc.ico" KeyPath="yes" />
            </Component>
            <Component Id="pyd.ico" Directory="DLLs" Guid="*">
                <File Name="pyd.ico" Source="!(bindpath.src)PC\icons\pyd.ico" KeyPath="yes" />
            </Component>
        </ComponentGroup>
    </Fragment>
</Wix>
