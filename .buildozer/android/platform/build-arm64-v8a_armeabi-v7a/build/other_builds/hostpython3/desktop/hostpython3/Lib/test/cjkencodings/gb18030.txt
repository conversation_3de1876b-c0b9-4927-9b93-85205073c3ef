Python����ɭ��������һ�ֹ���ǿ������Ƶ�ͨ���ͼ��������������ԣ�
�Ѿ�����ʮ����ķ�չ��ʷ���������ȶ����������Ծ��зǳ���ݶ�����
���﷨�ص㣬�ʺ���ɸ��ָ߲����񣬼������������еĲ���ϵͳ��
���С��������Լ򵥶�ǿ���ʺϸ�����ʿѧϰʹ�á�Ŀǰ��������
�����Ե���ؼ������ڷ��ٵķ�չ���û���������������ص���Դ�ǳ��ࡣ
����� Python ��ʹ�ü��е� C library?
�����YӍ�Ƽ����ٰlչ�Ľ���, �_�l���yԇܛ�w���ٶ��ǲ��ݺ�ҕ��
�n�}. ��ӿ��_�l���yԇ���ٶ�, �҂��㳣ϣ��������һЩ���_�l�õ�
library, �K��һ�� fast prototyping �� programming language ��
��ʹ��. Ŀǰ���S�S���� library ���� C ����, �� Python ��һ��
fast prototyping �� programming language. ���҂�ϣ���܌����е�
C library �õ� Python �ĭh���Мyԇ������. ��������ҪҲ���҂���
ҪӑՓ�Ć��}����:
�5�1�3�3�2�1�3�1 �7�6�0�4�6�3 �8�5�8�6�3�5 �3�1�9�5 �0�9�3�0 �4�3�5�7�5�5 �5�5�0�9�8�9�9�3�0�4 �2�9�2�5�9�9.

