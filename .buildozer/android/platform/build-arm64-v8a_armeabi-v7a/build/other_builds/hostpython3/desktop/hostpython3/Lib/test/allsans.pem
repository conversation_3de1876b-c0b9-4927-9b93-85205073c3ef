************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            cb:2d:80:99:5a:69:52:5f
        Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=XY, O=Python Software Foundation CA, CN=our-ca-server
        Validity
            Not Before: Aug 29 14:23:16 2018 GMT
            Not After : Oct 28 14:23:16 2037 GMT
        Subject: C=XY, L=Castle Anthrax, O=Python Software Foundation, CN=allsans
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                RSA Public-Key: (3072 bit)
                Modulus:
                    00:c1:1a:f8:fe:53:2f:d5:53:24:d1:9a:62:00:d5:
                    41:04:35:38:61:d3:ea:56:38:37:2f:bd:49:64:78:
                    8a:3e:5d:32:77:16:11:78:fd:15:63:22:60:c7:8e:
                    8c:e5:db:c5:df:d9:3c:ca:9e:f4:09:6b:52:82:a6:
                    a6:bb:7e:bf:1c:2f:d4:0c:c2:4e:29:1f:e4:b8:ba:
                    5c:4e:bb:4d:81:97:76:1a:7f:1d:a8:25:55:0c:2f:
                    7e:ef:72:22:60:fb:39:33:3f:1d:64:de:d5:c5:8d:
                    79:2b:2c:68:d9:c0:ea:2e:7c:10:b7:02:63:ee:ab:
                    6c:47:14:1c:c7:ae:fa:79:fc:32:11:1f:6b:25:40:
                    53:3d:7e:95:59:cc:de:fb:81:8c:b3:c5:b6:b4:c0:
                    27:c2:3e:90:9f:78:91:51:c9:82:96:f1:ce:cc:5e:
                    bc:27:33:cd:98:b4:4e:d1:96:77:f6:db:b1:24:09:
                    d3:0d:69:27:99:2d:42:31:79:5a:5c:9d:27:2a:66:
                    5d:12:21:b4:77:60:48:95:d0:b3:c5:93:1d:30:24:
                    59:bc:a9:41:05:53:f8:7e:d2:36:a6:83:2f:ce:37:
                    ed:75:9e:a9:8c:96:9d:c1:8c:d8:bf:25:35:6c:6b:
                    b3:7b:03:77:6f:74:70:bb:55:59:6b:5a:75:20:53:
                    a3:28:4a:78:b2:2f:a8:a3:a6:e7:32:1e:d6:73:2b:
                    69:89:cb:4b:07:47:c3:da:74:72:a8:c3:43:b8:db:
                    7f:f9:37:c1:8a:4d:23:af:68:63:17:4e:30:1e:38:
                    6b:3e:f7:f3:f5:65:8a:37:22:38:d0:3f:3f:cd:57:
                    74:25:84:af:33:46:ac:45:dd:c5:b4:7a:41:c7:91:
                    3f:bf:8d:98:c2:bd:22:a6:ea:67:5b:31:0b:a7:28:
                    4d:56:f9:da:24:01:cf:35:e6:96:f8:f0:cc:df:d5:
                    e5:8a:77:fe:d4:c9:47:fb:09:7b:ac:b3:20:1a:27:
                    77:25:a5:a2:b5:b1:b6:e7:f6:6d
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Alternative Name: 
                DNS:allsans, othername:<unsupported>, othername:<unsupported>, email:<EMAIL>, DNS:www.example.org, DirName:/C=XY/L=Castle Anthrax/O=Python Software Foundation/CN=dirname example, URI:https://www.python.org/, IP Address:127.0.0.1, IP Address:0:0:0:0:0:0:0:1, Registered ID:*******.5
            X509v3 Key Usage: critical
                Digital Signature, Key Encipherment
            X509v3 Extended Key Usage: 
                TLS Web Server Authentication, TLS Web Client Authentication
            X509v3 Basic Constraints: critical
                CA:FALSE
            X509v3 Subject Key Identifier: 
                D4:F1:D8:23:E0:A7:E9:CA:12:45:A0:0D:03:C2:25:A6:E8:65:BC:EE
            X509v3 Authority Key Identifier: 
                keyid:B3:8A:A0:A2:BA:71:F1:A8:24:79:D4:A4:5B:25:36:15:1E:49:C8:CD
                DirName:/C=XY/O=Python Software Foundation CA/CN=our-ca-server
                serial:CB:2D:80:99:5A:69:52:5B

            Authority Information Access: 
                CA Issuers - URI:http://testca.pythontest.net/testca/pycacert.cer
                OCSP - URI:http://testca.pythontest.net/testca/ocsp/

            X509v3 CRL Distribution Points: 

                Full Name:
                  URI:http://testca.pythontest.net/testca/revocation.crl

    Signature Algorithm: sha256WithRSAEncryption
         70:77:d8:82:b0:f4:ab:de:84:ce:88:32:63:5e:23:0f:b6:58:
         a2:b1:65:ff:12:22:0b:88:a6:fa:06:40:9a:e7:63:a7:5d:ae:
         94:c5:68:3c:4b:e9:95:34:01:75:24:df:9d:6e:9b:e4:ff:3f:
         61:97:29:7b:ab:34:2c:14:d3:01:d2:eb:fb:84:40:db:12:54:
         7e:7a:44:bc:08:eb:9f:e2:15:0b:11:4f:25:d2:56:51:95:ad:
         6d:ad:07:aa:6a:61:f9:39:d5:82:8c:45:31:9f:2a:ff:18:98:
         49:0c:bb:17:ad:d5:24:d3:d1:c7:c4:10:3e:c4:79:26:58:f4:
         c5:de:82:16:c4:c3:c4:a7:a3:62:22:41:90:36:0f:bc:4c:fd:
         6a:18:22:f2:87:e9:07:db:b4:3d:65:00:e4:70:f9:d6:e5:a8:
         a1:b9:c9:9d:e7:5d:78:aa:98:d5:f8:f4:fd:5c:d9:4c:d0:6d:
         bf:87:71:d3:5b:ec:f4:bf:46:f9:c8:f8:10:c5:72:af:c3:15:
         b9:c4:06:67:0b:3f:f6:f4:64:c5:27:74:c1:6b:00:37:da:ea:
         18:36:77:36:a7:3e:80:2e:5d:54:0f:01:df:ce:9e:97:dd:c9:
         f2:8b:59:82:c5:65:31:c8:73:20:fd:24:23:25:d8:00:df:90:
         93:26:76:08:0a:06:a9:0e:d3:d3:4c:6f:ef:a7:fb:de:eb:2a:
         40:b9:e4:b1:44:0c:37:ca:c6:9e:44:4a:b4:7c:2c:40:52:35:
         bb:b3:71:28:3d:35:fd:be:c9:4f:54:b3:99:c5:5f:84:38:fb:
         2b:fb:ea:dd:88:e8:9d:c1:9b:67:87:3d:79:7b:3d:7e:61:1f:
         70:3c:b7:c8:4c:17:a5:0c:a3:28:c7:ab:48:11:14:f7:98:7a:
         da:4e:fb:91:76:89:0a:a6:c6:72:e0:96:d9:f1:80:ea:68:90:
         37:5c:c6:69:c7:d7:bc:c7:d1:ae:5b:a9:12:59:c6:e4:6c:61:
         a9:8b:ba:51:b3:13
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
