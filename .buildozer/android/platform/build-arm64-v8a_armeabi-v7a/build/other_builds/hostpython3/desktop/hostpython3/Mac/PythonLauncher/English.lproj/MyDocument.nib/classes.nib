{
    IBClasses = (
        {CLASS = FirstResponder; LANGUAGE = ObjC; SUPERCLASS = NSObject; }, 
        {
            ACTIONS = {"do_apply" = id; "do_cancel" = id; "do_reset" = id; "do_run" = id; }; 
            CLASS = MyDocument; 
            LANGUAGE = ObjC; 
            OUTLETS = {
                commandline = NSTextField; 
                debug = NSButton; 
                honourhashbang = NSButton; 
                inspect = NSButton; 
                interpreter = NSTextField; 
                nosite = NSButton; 
                optimize = NSButton; 
                others = NSTextField; 
                scriptargs = NSTextField; 
                tabs = NSButton; 
                verbose = NSButton; 
                "with_terminal" = NSButton; 
            }; 
            SUPERCLASS = NSDocument; 
        }
    ); 
    IBVersion = 1; 
}