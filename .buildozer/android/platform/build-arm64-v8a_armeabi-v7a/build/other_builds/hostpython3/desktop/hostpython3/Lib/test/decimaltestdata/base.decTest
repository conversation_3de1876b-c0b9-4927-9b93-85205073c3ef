------------------------------------------------------------------------
-- base.decTest -- base decimal <--> string conversions               --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59
extended:    1

-- This file tests base conversions from string to a decimal number
-- and back to a string (in either Scientific or Engineering form)

-- Note that unlike other operations the operand is subject to rounding
-- to conform to emax and precision settings (that is, numbers will
-- conform to rules and exponent will be in permitted range).

precision:   16
rounding:    half_up
maxExponent: 384
minExponent: -383

basx001 toSci       0 -> 0
basx002 toSci       1 -> 1
basx003 toSci     1.0 -> 1.0
basx004 toSci    1.00 -> 1.00
basx005 toSci      10 -> 10
basx006 toSci    1000 -> 1000
basx007 toSci    10.0 -> 10.0
basx008 toSci    10.1 -> 10.1
basx009 toSci    10.4 -> 10.4
basx010 toSci    10.5 -> 10.5
basx011 toSci    10.6 -> 10.6
basx012 toSci    10.9 -> 10.9
basx013 toSci    11.0 -> 11.0
basx014 toSci  1.234 -> 1.234
basx015 toSci  0.123 -> 0.123
basx016 toSci  0.012 -> 0.012
basx017 toSci  -0    -> -0
basx018 toSci  -0.0  -> -0.0
basx019 toSci -00.00 -> -0.00

basx021 toSci     -1 -> -1
basx022 toSci   -1.0 -> -1.0
basx023 toSci   -0.1 -> -0.1
basx024 toSci   -9.1 -> -9.1
basx025 toSci   -9.11 -> -9.11
basx026 toSci   -9.119 -> -9.119
basx027 toSci   -9.999 -> -9.999

basx030 toSci  '123456789.123456'   -> '123456789.123456'
basx031 toSci  '123456789.000000'   -> '123456789.000000'
basx032 toSci   '123456789123456'   -> '123456789123456'
basx033 toSci   '0.0000123456789'   -> '0.0000123456789'
basx034 toSci  '0.00000123456789'   -> '0.00000123456789'
basx035 toSci '0.000000123456789'   -> '1.23456789E-7'
basx036 toSci '0.0000000123456789'  -> '1.23456789E-8'

basx037 toSci '0.123456789012344'   -> '0.123456789012344'
basx038 toSci '0.123456789012345'   -> '0.123456789012345'

-- String [many more examples are implicitly tested elsewhere]
-- strings without E cannot generate E in result
basx040 toSci "12"        -> '12'
basx041 toSci "-76"       -> '-76'
basx042 toSci "12.76"     -> '12.76'
basx043 toSci "+12.76"    -> '12.76'
basx044 toSci "012.76"    -> '12.76'
basx045 toSci "+0.003"    -> '0.003'
basx046 toSci "17."       -> '17'
basx047 toSci ".5"        -> '0.5'
basx048 toSci "044"       -> '44'
basx049 toSci "0044"      -> '44'
basx050 toSci "0.0005"      -> '0.0005'
basx051 toSci "00.00005"    -> '0.00005'
basx052 toSci "0.000005"    -> '0.000005'
basx053 toSci "0.0000050"   -> '0.0000050'
basx054 toSci "0.0000005"   -> '5E-7'
basx055 toSci "0.00000005"  -> '5E-8'
basx056 toSci "12345678.543210" -> '12345678.543210'
basx057 toSci "2345678.543210" -> '2345678.543210'
basx058 toSci "345678.543210" -> '345678.543210'
basx059 toSci "0345678.54321" -> '345678.54321'
basx060 toSci "345678.5432" -> '345678.5432'
basx061 toSci "+345678.5432" -> '345678.5432'
basx062 toSci "+0345678.5432" -> '345678.5432'
basx063 toSci "+00345678.5432" -> '345678.5432'
basx064 toSci "-345678.5432"  -> '-345678.5432'
basx065 toSci "-0345678.5432"  -> '-345678.5432'
basx066 toSci "-00345678.5432"  -> '-345678.5432'
-- examples
basx067 toSci "5E-6"        -> '0.000005'
basx068 toSci "50E-7"       -> '0.0000050'
basx069 toSci "5E-7"        -> '5E-7'

-- [No exotics as no Unicode]

-- rounded with dots in all (including edge) places
basx071 toSci  .1234567890123456123  -> 0.1234567890123456 Inexact Rounded
basx072 toSci  1.234567890123456123  -> 1.234567890123456 Inexact Rounded
basx073 toSci  12.34567890123456123  -> 12.34567890123456 Inexact Rounded
basx074 toSci  123.4567890123456123  -> 123.4567890123456 Inexact Rounded
basx075 toSci  1234.567890123456123  -> 1234.567890123456 Inexact Rounded
basx076 toSci  12345.67890123456123  -> 12345.67890123456 Inexact Rounded
basx077 toSci  123456.7890123456123  -> 123456.7890123456 Inexact Rounded
basx078 toSci  1234567.890123456123  -> 1234567.890123456 Inexact Rounded
basx079 toSci  12345678.90123456123  -> 12345678.90123456 Inexact Rounded
basx080 toSci  123456789.0123456123  -> 123456789.0123456 Inexact Rounded
basx081 toSci  1234567890.123456123  -> 1234567890.123456 Inexact Rounded
basx082 toSci  12345678901.23456123  -> 12345678901.23456 Inexact Rounded
basx083 toSci  123456789012.3456123  -> 123456789012.3456 Inexact Rounded
basx084 toSci  1234567890123.456123  -> 1234567890123.456 Inexact Rounded
basx085 toSci  12345678901234.56123  -> 12345678901234.56 Inexact Rounded
basx086 toSci  123456789012345.6123  -> 123456789012345.6 Inexact Rounded
basx087 toSci  1234567890123456.123  -> 1234567890123456  Inexact Rounded
basx088 toSci  12345678901234561.23  -> 1.234567890123456E+16 Inexact Rounded
basx089 toSci  123456789012345612.3  -> 1.234567890123456E+17 Inexact Rounded
basx090 toSci  1234567890123456123.  -> 1.234567890123456E+18 Inexact Rounded

-- Numbers with E
basx130 toSci "0.000E-1"  -> '0.0000'
basx131 toSci "0.000E-2"  -> '0.00000'
basx132 toSci "0.000E-3"  -> '0.000000'
basx133 toSci "0.000E-4"  -> '0E-7'
basx134 toSci "0.00E-2"   -> '0.0000'
basx135 toSci "0.00E-3"   -> '0.00000'
basx136 toSci "0.00E-4"   -> '0.000000'
basx137 toSci "0.00E-5"   -> '0E-7'
basx138 toSci "+0E+9"     -> '0E+9'
basx139 toSci "-0E+9"     -> '-0E+9'
basx140 toSci "1E+9"      -> '1E+9'
basx141 toSci "1e+09"     -> '1E+9'
basx142 toSci "1E+90"     -> '1E+90'
basx143 toSci "+1E+009"   -> '1E+9'
basx144 toSci "0E+9"      -> '0E+9'
basx145 toSci "1E+9"      -> '1E+9'
basx146 toSci "1E+09"     -> '1E+9'
basx147 toSci "1e+90"     -> '1E+90'
basx148 toSci "1E+009"    -> '1E+9'
basx149 toSci "000E+9"    -> '0E+9'
basx150 toSci "1E9"       -> '1E+9'
basx151 toSci "1e09"      -> '1E+9'
basx152 toSci "1E90"      -> '1E+90'
basx153 toSci "1E009"     -> '1E+9'
basx154 toSci "0E9"       -> '0E+9'
basx155 toSci "0.000e+0"  -> '0.000'
basx156 toSci "0.000E-1"  -> '0.0000'
basx157 toSci "4E+9"      -> '4E+9'
basx158 toSci "44E+9"     -> '4.4E+10'
basx159 toSci "0.73e-7"   -> '7.3E-8'
basx160 toSci "00E+9"     -> '0E+9'
basx161 toSci "00E-9"     -> '0E-9'
basx162 toSci "10E+9"     -> '1.0E+10'
basx163 toSci "10E+09"    -> '1.0E+10'
basx164 toSci "10e+90"    -> '1.0E+91'
basx165 toSci "10E+009"   -> '1.0E+10'
basx166 toSci "100e+9"    -> '1.00E+11'
basx167 toSci "100e+09"   -> '1.00E+11'
basx168 toSci "100E+90"   -> '1.00E+92'
basx169 toSci "100e+009"  -> '1.00E+11'

basx170 toSci "1.265"     -> '1.265'
basx171 toSci "1.265E-20" -> '1.265E-20'
basx172 toSci "1.265E-8"  -> '1.265E-8'
basx173 toSci "1.265E-4"  -> '0.0001265'
basx174 toSci "1.265E-3"  -> '0.001265'
basx175 toSci "1.265E-2"  -> '0.01265'
basx176 toSci "1.265E-1"  -> '0.1265'
basx177 toSci "1.265E-0"  -> '1.265'
basx178 toSci "1.265E+1"  -> '12.65'
basx179 toSci "1.265E+2"  -> '126.5'
basx180 toSci "1.265E+3"  -> '1265'
basx181 toSci "1.265E+4"  -> '1.265E+4'
basx182 toSci "1.265E+8"  -> '1.265E+8'
basx183 toSci "1.265E+20" -> '1.265E+20'

basx190 toSci "12.65"     -> '12.65'
basx191 toSci "12.65E-20" -> '1.265E-19'
basx192 toSci "12.65E-8"  -> '1.265E-7'
basx193 toSci "12.65E-4"  -> '0.001265'
basx194 toSci "12.65E-3"  -> '0.01265'
basx195 toSci "12.65E-2"  -> '0.1265'
basx196 toSci "12.65E-1"  -> '1.265'
basx197 toSci "12.65E-0"  -> '12.65'
basx198 toSci "12.65E+1"  -> '126.5'
basx199 toSci "12.65E+2"  -> '1265'
basx200 toSci "12.65E+3"  -> '1.265E+4'
basx201 toSci "12.65E+4"  -> '1.265E+5'
basx202 toSci "12.65E+8"  -> '1.265E+9'
basx203 toSci "12.65E+20" -> '1.265E+21'

basx210 toSci "126.5"     -> '126.5'
basx211 toSci "126.5E-20" -> '1.265E-18'
basx212 toSci "126.5E-8"  -> '0.000001265'
basx213 toSci "126.5E-4"  -> '0.01265'
basx214 toSci "126.5E-3"  -> '0.1265'
basx215 toSci "126.5E-2"  -> '1.265'
basx216 toSci "126.5E-1"  -> '12.65'
basx217 toSci "126.5E-0"  -> '126.5'
basx218 toSci "126.5E+1"  -> '1265'
basx219 toSci "126.5E+2"  -> '1.265E+4'
basx220 toSci "126.5E+3"  -> '1.265E+5'
basx221 toSci "126.5E+4"  -> '1.265E+6'
basx222 toSci "126.5E+8"  -> '1.265E+10'
basx223 toSci "126.5E+20" -> '1.265E+22'

basx230 toSci "1265"     -> '1265'
basx231 toSci "1265E-20" -> '1.265E-17'
basx232 toSci "1265E-8"  -> '0.00001265'
basx233 toSci "1265E-4"  -> '0.1265'
basx234 toSci "1265E-3"  -> '1.265'
basx235 toSci "1265E-2"  -> '12.65'
basx236 toSci "1265E-1"  -> '126.5'
basx237 toSci "1265E-0"  -> '1265'
basx238 toSci "1265E+1"  -> '1.265E+4'
basx239 toSci "1265E+2"  -> '1.265E+5'
basx240 toSci "1265E+3"  -> '1.265E+6'
basx241 toSci "1265E+4"  -> '1.265E+7'
basx242 toSci "1265E+8"  -> '1.265E+11'
basx243 toSci "1265E+20" -> '1.265E+23'

basx250 toSci "0.1265"     -> '0.1265'
basx251 toSci "0.1265E-20" -> '1.265E-21'
basx252 toSci "0.1265E-8"  -> '1.265E-9'
basx253 toSci "0.1265E-4"  -> '0.00001265'
basx254 toSci "0.1265E-3"  -> '0.0001265'
basx255 toSci "0.1265E-2"  -> '0.001265'
basx256 toSci "0.1265E-1"  -> '0.01265'
basx257 toSci "0.1265E-0"  -> '0.1265'
basx258 toSci "0.1265E+1"  -> '1.265'
basx259 toSci "0.1265E+2"  -> '12.65'
basx260 toSci "0.1265E+3"  -> '126.5'
basx261 toSci "0.1265E+4"  -> '1265'
basx262 toSci "0.1265E+8"  -> '1.265E+7'
basx263 toSci "0.1265E+20" -> '1.265E+19'

-- some more negative zeros [systematic tests below]
basx290 toSci "-0.000E-1"  -> '-0.0000'
basx291 toSci "-0.000E-2"  -> '-0.00000'
basx292 toSci "-0.000E-3"  -> '-0.000000'
basx293 toSci "-0.000E-4"  -> '-0E-7'
basx294 toSci "-0.00E-2"   -> '-0.0000'
basx295 toSci "-0.00E-3"   -> '-0.00000'
basx296 toSci "-0.0E-2"    -> '-0.000'
basx297 toSci "-0.0E-3"    -> '-0.0000'
basx298 toSci "-0E-2"      -> '-0.00'
basx299 toSci "-0E-3"      -> '-0.000'

-- Engineering notation tests
basx301  toSci 10e12  -> 1.0E+13
basx302  toEng 10e12  -> 10E+12
basx303  toSci 10e11  -> 1.0E+12
basx304  toEng 10e11  -> 1.0E+12
basx305  toSci 10e10  -> 1.0E+11
basx306  toEng 10e10  -> 100E+9
basx307  toSci 10e9   -> 1.0E+10
basx308  toEng 10e9   -> 10E+9
basx309  toSci 10e8   -> 1.0E+9
basx310  toEng 10e8   -> 1.0E+9
basx311  toSci 10e7   -> 1.0E+8
basx312  toEng 10e7   -> 100E+6
basx313  toSci 10e6   -> 1.0E+7
basx314  toEng 10e6   -> 10E+6
basx315  toSci 10e5   -> 1.0E+6
basx316  toEng 10e5   -> 1.0E+6
basx317  toSci 10e4   -> 1.0E+5
basx318  toEng 10e4   -> 100E+3
basx319  toSci 10e3   -> 1.0E+4
basx320  toEng 10e3   -> 10E+3
basx321  toSci 10e2   -> 1.0E+3
basx322  toEng 10e2   -> 1.0E+3
basx323  toSci 10e1   -> 1.0E+2
basx324  toEng 10e1   -> 100
basx325  toSci 10e0   -> 10
basx326  toEng 10e0   -> 10
basx327  toSci 10e-1  -> 1.0
basx328  toEng 10e-1  -> 1.0
basx329  toSci 10e-2  -> 0.10
basx330  toEng 10e-2  -> 0.10
basx331  toSci 10e-3  -> 0.010
basx332  toEng 10e-3  -> 0.010
basx333  toSci 10e-4  -> 0.0010
basx334  toEng 10e-4  -> 0.0010
basx335  toSci 10e-5  -> 0.00010
basx336  toEng 10e-5  -> 0.00010
basx337  toSci 10e-6  -> 0.000010
basx338  toEng 10e-6  -> 0.000010
basx339  toSci 10e-7  -> 0.0000010
basx340  toEng 10e-7  -> 0.0000010
basx341  toSci 10e-8  -> 1.0E-7
basx342  toEng 10e-8  -> 100E-9
basx343  toSci 10e-9  -> 1.0E-8
basx344  toEng 10e-9  -> 10E-9
basx345  toSci 10e-10 -> 1.0E-9
basx346  toEng 10e-10 -> 1.0E-9
basx347  toSci 10e-11 -> 1.0E-10
basx348  toEng 10e-11 -> 100E-12
basx349  toSci 10e-12 -> 1.0E-11
basx350  toEng 10e-12 -> 10E-12
basx351  toSci 10e-13 -> 1.0E-12
basx352  toEng 10e-13 -> 1.0E-12

basx361  toSci 7E12  -> 7E+12
basx362  toEng 7E12  -> 7E+12
basx363  toSci 7E11  -> 7E+11
basx364  toEng 7E11  -> 700E+9
basx365  toSci 7E10  -> 7E+10
basx366  toEng 7E10  -> 70E+9
basx367  toSci 7E9   -> 7E+9
basx368  toEng 7E9   -> 7E+9
basx369  toSci 7E8   -> 7E+8
basx370  toEng 7E8   -> 700E+6
basx371  toSci 7E7   -> 7E+7
basx372  toEng 7E7   -> 70E+6
basx373  toSci 7E6   -> 7E+6
basx374  toEng 7E6   -> 7E+6
basx375  toSci 7E5   -> 7E+5
basx376  toEng 7E5   -> 700E+3
basx377  toSci 7E4   -> 7E+4
basx378  toEng 7E4   -> 70E+3
basx379  toSci 7E3   -> 7E+3
basx380  toEng 7E3   -> 7E+3
basx381  toSci 7E2   -> 7E+2
basx382  toEng 7E2   -> 700
basx383  toSci 7E1   -> 7E+1
basx384  toEng 7E1   -> 70
basx385  toSci 7E0   -> 7
basx386  toEng 7E0   -> 7
basx387  toSci 7E-1  -> 0.7
basx388  toEng 7E-1  -> 0.7
basx389  toSci 7E-2  -> 0.07
basx390  toEng 7E-2  -> 0.07
basx391  toSci 7E-3  -> 0.007
basx392  toEng 7E-3  -> 0.007
basx393  toSci 7E-4  -> 0.0007
basx394  toEng 7E-4  -> 0.0007
basx395  toSci 7E-5  -> 0.00007
basx396  toEng 7E-5  -> 0.00007
basx397  toSci 7E-6  -> 0.000007
basx398  toEng 7E-6  -> 0.000007
basx399  toSci 7E-7  -> 7E-7
basx400  toEng 7E-7  -> 700E-9
basx401  toSci 7E-8  -> 7E-8
basx402  toEng 7E-8  -> 70E-9
basx403  toSci 7E-9  -> 7E-9
basx404  toEng 7E-9  -> 7E-9
basx405  toSci 7E-10 -> 7E-10
basx406  toEng 7E-10 -> 700E-12
basx407  toSci 7E-11 -> 7E-11
basx408  toEng 7E-11 -> 70E-12
basx409  toSci 7E-12 -> 7E-12
basx410  toEng 7E-12 -> 7E-12
basx411  toSci 7E-13 -> 7E-13
basx412  toEng 7E-13 -> 700E-15

-- Exacts remain exact up to precision ..
precision: 9
basx420  toSci    100 -> 100
basx421  toEng    100 -> 100
basx422  toSci   1000 -> 1000
basx423  toEng   1000 -> 1000
basx424  toSci  999.9 ->  999.9
basx425  toEng  999.9 ->  999.9
basx426  toSci 1000.0 -> 1000.0
basx427  toEng 1000.0 -> 1000.0
basx428  toSci 1000.1 -> 1000.1
basx429  toEng 1000.1 -> 1000.1
basx430  toSci 10000 -> 10000
basx431  toEng 10000 -> 10000
basx432  toSci 100000 -> 100000
basx433  toEng 100000 -> 100000
basx434  toSci 1000000 -> 1000000
basx435  toEng 1000000 -> 1000000
basx436  toSci 10000000 -> 10000000
basx437  toEng 10000000 -> 10000000
basx438  toSci 100000000 -> 100000000
basx439  toEng 100000000 -> 100000000
basx440  toSci 1*********    -> 1.00000000E+9    Rounded
basx441  toEng 1*********    -> 1.00000000E+9    Rounded
basx442  toSci 1*********    -> 1.00000000E+9    Rounded
basx443  toEng 1*********    -> 1.00000000E+9    Rounded
basx444  toSci 1000000003    -> 1.00000000E+9    Rounded Inexact
basx445  toEng 1000000003    -> 1.00000000E+9    Rounded Inexact
basx446  toSci 1000000005    -> 1.00000001E+9    Rounded Inexact
basx447  toEng 1000000005    -> 1.00000001E+9    Rounded Inexact
basx448  toSci 10000000050   -> 1.00000001E+10   Rounded Inexact
basx449  toEng 10000000050   -> 10.0000001E+9    Rounded Inexact
basx450  toSci 1000000009    -> 1.00000001E+9    Rounded Inexact
basx451  toEng 1000000009    -> 1.00000001E+9    Rounded Inexact
basx452  toSci 1*********0   -> 1.00000000E+10   Rounded
basx453  toEng 1*********0   -> 10.0000000E+9    Rounded
basx454  toSci 1*********3   -> 1.00000000E+10   Rounded Inexact
basx455  toEng 1*********3   -> 10.0000000E+9    Rounded Inexact
basx456  toSci 1*********5   -> 1.00000000E+10   Rounded Inexact
basx457  toEng 1*********5   -> 10.0000000E+9    Rounded Inexact
basx458  toSci 1*********9   -> 1.00000000E+10   Rounded Inexact
basx459  toEng 1*********9   -> 10.0000000E+9    Rounded Inexact
basx460  toSci 1*********00  -> 1.00000000E+11   Rounded
basx461  toEng 1*********00  -> 100.000000E+9    Rounded
basx462  toSci 100000000300  -> 1.00000000E+11   Rounded Inexact
basx463  toEng 100000000300  -> 100.000000E+9    Rounded Inexact
basx464  toSci 100000000500  -> 1.00000001E+11   Rounded Inexact
basx465  toEng 100000000500  -> 100.000001E+9    Rounded Inexact
basx466  toSci 100000000900  -> 1.00000001E+11   Rounded Inexact
basx467  toEng 100000000900  -> 100.000001E+9    Rounded Inexact
basx468  toSci 1*********000 -> 1.00000000E+12   Rounded
basx469  toEng 1*********000 -> 1.00000000E+12   Rounded
basx470  toSci 1000000003000 -> 1.00000000E+12   Rounded Inexact
basx471  toEng 1000000003000 -> 1.00000000E+12   Rounded Inexact
basx472  toSci 1000000005000 -> 1.00000001E+12   Rounded Inexact
basx473  toEng 1000000005000 -> 1.00000001E+12   Rounded Inexact
basx474  toSci 1000000009000 -> 1.00000001E+12   Rounded Inexact
basx475  toEng 1000000009000 -> 1.00000001E+12   Rounded Inexact

-- all-nines rounding
precision: 9
rounding:  half_up
basx270  toSci 999999999          ->   999999999
basx271  toSci 9999999990         ->   9.99999999E+9      Rounded
basx272  toSci 9999999991         ->   9.99999999E+9      Rounded Inexact
basx273  toSci 9999999992         ->   9.99999999E+9      Rounded Inexact
basx274  toSci 9999999993         ->   9.99999999E+9      Rounded Inexact
basx275  toSci 9999999994         ->   9.99999999E+9      Rounded Inexact
basx276  toSci 9999999995         ->   1.00000000E+10     Rounded Inexact
basx277  toSci 9999999996         ->   1.00000000E+10     Rounded Inexact
basx278  toSci 9999999997         ->   1.00000000E+10     Rounded Inexact
basx279  toSci 9999999998         ->   1.00000000E+10     Rounded Inexact
basx280  toSci 9999999999         ->   1.00000000E+10     Rounded Inexact
basx281  toSci 9999999999999999   ->   1.00000000E+16     Rounded Inexact

-- check rounding modes heeded
precision: 5
rounding:  ceiling
bsrx401  toSci  1.23450    ->  1.2345  Rounded
bsrx402  toSci  1.234549   ->  1.2346  Rounded Inexact
bsrx403  toSci  1.234550   ->  1.2346  Rounded Inexact
bsrx404  toSci  1.234551   ->  1.2346  Rounded Inexact
rounding:  up
bsrx405  toSci  1.23450    ->  1.2345  Rounded
bsrx406  toSci  1.234549   ->  1.2346  Rounded Inexact
bsrx407  toSci  1.234550   ->  1.2346  Rounded Inexact
bsrx408  toSci  1.234551   ->  1.2346  Rounded Inexact
rounding:  floor
bsrx410  toSci  1.23450    ->  1.2345  Rounded
bsrx411  toSci  1.234549   ->  1.2345  Rounded Inexact
bsrx412  toSci  1.234550   ->  1.2345  Rounded Inexact
bsrx413  toSci  1.234551   ->  1.2345  Rounded Inexact
rounding:  half_down
bsrx415  toSci  1.23450    ->  1.2345  Rounded
bsrx416  toSci  1.234549   ->  1.2345  Rounded Inexact
bsrx417  toSci  1.234550   ->  1.2345  Rounded Inexact
bsrx418  toSci  1.234650   ->  1.2346  Rounded Inexact
bsrx419  toSci  1.234551   ->  1.2346  Rounded Inexact
rounding:  half_even
bsrx421  toSci  1.23450    ->  1.2345  Rounded
bsrx422  toSci  1.234549   ->  1.2345  Rounded Inexact
bsrx423  toSci  1.234550   ->  1.2346  Rounded Inexact
bsrx424  toSci  1.234650   ->  1.2346  Rounded Inexact
bsrx425  toSci  1.234551   ->  1.2346  Rounded Inexact
rounding:  down
bsrx426  toSci  1.23450    ->  1.2345  Rounded
bsrx427  toSci  1.234549   ->  1.2345  Rounded Inexact
bsrx428  toSci  1.234550   ->  1.2345  Rounded Inexact
bsrx429  toSci  1.234551   ->  1.2345  Rounded Inexact
rounding:  half_up
bsrx431  toSci  1.23450    ->  1.2345  Rounded
bsrx432  toSci  1.234549   ->  1.2345  Rounded Inexact
bsrx433  toSci  1.234550   ->  1.2346  Rounded Inexact
bsrx434  toSci  1.234650   ->  1.2347  Rounded Inexact
bsrx435  toSci  1.234551   ->  1.2346  Rounded Inexact
-- negatives
rounding:  ceiling
bsrx501  toSci -1.23450    -> -1.2345  Rounded
bsrx502  toSci -1.234549   -> -1.2345  Rounded Inexact
bsrx503  toSci -1.234550   -> -1.2345  Rounded Inexact
bsrx504  toSci -1.234551   -> -1.2345  Rounded Inexact
rounding:  up
bsrx505  toSci -1.23450    -> -1.2345  Rounded
bsrx506  toSci -1.234549   -> -1.2346  Rounded Inexact
bsrx507  toSci -1.234550   -> -1.2346  Rounded Inexact
bsrx508  toSci -1.234551   -> -1.2346  Rounded Inexact
rounding:  floor
bsrx510  toSci -1.23450    -> -1.2345  Rounded
bsrx511  toSci -1.234549   -> -1.2346  Rounded Inexact
bsrx512  toSci -1.234550   -> -1.2346  Rounded Inexact
bsrx513  toSci -1.234551   -> -1.2346  Rounded Inexact
rounding:  half_down
bsrx515  toSci -1.23450    -> -1.2345  Rounded
bsrx516  toSci -1.234549   -> -1.2345  Rounded Inexact
bsrx517  toSci -1.234550   -> -1.2345  Rounded Inexact
bsrx518  toSci -1.234650   -> -1.2346  Rounded Inexact
bsrx519  toSci -1.234551   -> -1.2346  Rounded Inexact
rounding:  half_even
bsrx521  toSci -1.23450    -> -1.2345  Rounded
bsrx522  toSci -1.234549   -> -1.2345  Rounded Inexact
bsrx523  toSci -1.234550   -> -1.2346  Rounded Inexact
bsrx524  toSci -1.234650   -> -1.2346  Rounded Inexact
bsrx525  toSci -1.234551   -> -1.2346  Rounded Inexact
rounding:  down
bsrx526  toSci -1.23450    -> -1.2345  Rounded
bsrx527  toSci -1.234549   -> -1.2345  Rounded Inexact
bsrx528  toSci -1.234550   -> -1.2345  Rounded Inexact
bsrx529  toSci -1.234551   -> -1.2345  Rounded Inexact
rounding:  half_up
bsrx531  toSci -1.23450    -> -1.2345  Rounded
bsrx532  toSci -1.234549   -> -1.2345  Rounded Inexact
bsrx533  toSci -1.234550   -> -1.2346  Rounded Inexact
bsrx534  toSci -1.234650   -> -1.2347  Rounded Inexact
bsrx535  toSci -1.234551   -> -1.2346  Rounded Inexact

-- a few larger exponents
maxExponent: 999999999
minExponent: -999999999
basx480 toSci "0.09e999"  -> '9E+997'
basx481 toSci "0.9e999"   -> '9E+998'
basx482 toSci "9e999"     -> '9E+999'
basx483 toSci "9.9e999"   -> '9.9E+999'
basx484 toSci "9.99e999"  -> '9.99E+999'
basx485 toSci "9.99e-999" -> '9.99E-999'
basx486 toSci "9.9e-999"  -> '9.9E-999'
basx487 toSci "9e-999"    -> '9E-999'
basx489 toSci "99e-999"   -> '9.9E-998'
basx490 toSci "999e-999"  -> '9.99E-997'
basx491 toSci '0.9e-998'  -> '9E-999'
basx492 toSci '0.09e-997' -> '9E-999'
basx493 toSci '0.1e1000'  -> '1E+999'
basx494 toSci '10e-1000'  -> '1.0E-999'

rounding:  half_up
precision: 9

-- The 'baddies' tests from DiagBigDecimal, plus some new ones
basx500 toSci '1..2'            -> NaN Conversion_syntax
basx501 toSci '.'               -> NaN Conversion_syntax
basx502 toSci '..'              -> NaN Conversion_syntax
basx503 toSci '++1'             -> NaN Conversion_syntax
basx504 toSci '--1'             -> NaN Conversion_syntax
basx505 toSci '-+1'             -> NaN Conversion_syntax
basx506 toSci '+-1'             -> NaN Conversion_syntax
basx507 toSci '12e'             -> NaN Conversion_syntax
basx508 toSci '12e++'           -> NaN Conversion_syntax
basx509 toSci '12f4'            -> NaN Conversion_syntax
basx510 toSci ' +1'             -> NaN Conversion_syntax
basx511 toSci '+ 1'             -> NaN Conversion_syntax
basx512 toSci '12 '             -> NaN Conversion_syntax
basx513 toSci ' + 1'            -> NaN Conversion_syntax
basx514 toSci ' - 1 '           -> NaN Conversion_syntax
basx515 toSci 'x'               -> NaN Conversion_syntax
basx516 toSci '-1-'             -> NaN Conversion_syntax
basx517 toSci '12-'             -> NaN Conversion_syntax
basx518 toSci '3+'              -> NaN Conversion_syntax
basx519 toSci ''                -> NaN Conversion_syntax
basx520 toSci '1e-'             -> NaN Conversion_syntax
basx521 toSci '7e99999a'        -> NaN Conversion_syntax
basx522 toSci '7e123567890x'    -> NaN Conversion_syntax
basx523 toSci '7e12356789012x'  -> NaN Conversion_syntax
basx524 toSci ''                -> NaN Conversion_syntax
basx525 toSci 'e100'            -> NaN Conversion_syntax
basx526 toSci '\u0e5a'          -> NaN Conversion_syntax
basx527 toSci '\u0b65'          -> NaN Conversion_syntax
basx528 toSci '123,65'          -> NaN Conversion_syntax
basx529 toSci '1.34.5'          -> NaN Conversion_syntax
basx530 toSci '.123.5'          -> NaN Conversion_syntax
basx531 toSci '01.35.'          -> NaN Conversion_syntax
basx532 toSci '01.35-'          -> NaN Conversion_syntax
basx533 toSci '0000..'          -> NaN Conversion_syntax
basx534 toSci '.0000.'          -> NaN Conversion_syntax
basx535 toSci '00..00'          -> NaN Conversion_syntax
basx536 toSci '111e*123'        -> NaN Conversion_syntax
basx537 toSci '111e123-'        -> NaN Conversion_syntax
basx538 toSci '111e+12+'        -> NaN Conversion_syntax
basx539 toSci '111e1-3-'        -> NaN Conversion_syntax
basx540 toSci '111e1*23'        -> NaN Conversion_syntax
basx541 toSci '111e1e+3'        -> NaN Conversion_syntax
basx542 toSci '1e1.0'           -> NaN Conversion_syntax
basx543 toSci '1e123e'          -> NaN Conversion_syntax
basx544 toSci 'ten'             -> NaN Conversion_syntax
basx545 toSci 'ONE'             -> NaN Conversion_syntax
basx546 toSci '1e.1'            -> NaN Conversion_syntax
basx547 toSci '1e1.'            -> NaN Conversion_syntax
basx548 toSci '1ee'             -> NaN Conversion_syntax
basx549 toSci 'e+1'             -> NaN Conversion_syntax
basx550 toSci '1.23.4'          -> NaN Conversion_syntax
basx551 toSci '1.2.1'           -> NaN Conversion_syntax
basx552 toSci '1E+1.2'          -> NaN Conversion_syntax
basx553 toSci '1E+1.2.3'        -> NaN Conversion_syntax
basx554 toSci '1E++1'           -> NaN Conversion_syntax
basx555 toSci '1E--1'           -> NaN Conversion_syntax
basx556 toSci '1E+-1'           -> NaN Conversion_syntax
basx557 toSci '1E-+1'           -> NaN Conversion_syntax
basx558 toSci '1E''1'           -> NaN Conversion_syntax
basx559 toSci "1E""1"           -> NaN Conversion_syntax
basx560 toSci "1E"""""          -> NaN Conversion_syntax
-- Near-specials
basx561 toSci "qNaN"            -> NaN Conversion_syntax
basx562 toSci "NaNq"            -> NaN Conversion_syntax
basx563 toSci "NaNs"            -> NaN Conversion_syntax
basx564 toSci "Infi"            -> NaN Conversion_syntax
basx565 toSci "Infin"           -> NaN Conversion_syntax
basx566 toSci "Infini"          -> NaN Conversion_syntax
basx567 toSci "Infinit"         -> NaN Conversion_syntax
basx568 toSci "-Infinit"        -> NaN Conversion_syntax
basx569 toSci "0Inf"            -> NaN Conversion_syntax
basx570 toSci "9Inf"            -> NaN Conversion_syntax
basx571 toSci "-0Inf"           -> NaN Conversion_syntax
basx572 toSci "-9Inf"           -> NaN Conversion_syntax
basx573 toSci "-sNa"            -> NaN Conversion_syntax
basx574 toSci "xNaN"            -> NaN Conversion_syntax
basx575 toSci "0sNaN"           -> NaN Conversion_syntax

-- some baddies with dots and Es and dots and specials
basx576 toSci  'e+1'            ->  NaN Conversion_syntax
basx577 toSci  '.e+1'           ->  NaN Conversion_syntax
basx578 toSci  '+.e+1'          ->  NaN Conversion_syntax
basx579 toSci  '-.e+'           ->  NaN Conversion_syntax
basx580 toSci  '-.e'            ->  NaN Conversion_syntax
basx581 toSci  'E+1'            ->  NaN Conversion_syntax
basx582 toSci  '.E+1'           ->  NaN Conversion_syntax
basx583 toSci  '+.E+1'          ->  NaN Conversion_syntax
basx584 toSci  '-.E+'           ->  NaN Conversion_syntax
basx585 toSci  '-.E'            ->  NaN Conversion_syntax

basx586 toSci  '.NaN'           ->  NaN Conversion_syntax
basx587 toSci  '-.NaN'          ->  NaN Conversion_syntax
basx588 toSci  '+.sNaN'         ->  NaN Conversion_syntax
basx589 toSci  '+.Inf'          ->  NaN Conversion_syntax
basx590 toSci  '.Infinity'      ->  NaN Conversion_syntax

-- Zeros
basx601 toSci 0.*********       -> 0E-9
basx602 toSci 0.00000000        -> 0E-8
basx603 toSci 0.0000000         -> 0E-7
basx604 toSci 0.000000          -> 0.000000
basx605 toSci 0.00000           -> 0.00000
basx606 toSci 0.0000            -> 0.0000
basx607 toSci 0.000             -> 0.000
basx608 toSci 0.00              -> 0.00
basx609 toSci 0.0               -> 0.0
basx610 toSci  .0               -> 0.0
basx611 toSci 0.                -> 0
basx612 toSci -.0               -> -0.0
basx613 toSci -0.               -> -0
basx614 toSci -0.0              -> -0.0
basx615 toSci -0.00             -> -0.00
basx616 toSci -0.000            -> -0.000
basx617 toSci -0.0000           -> -0.0000
basx618 toSci -0.00000          -> -0.00000
basx619 toSci -0.000000         -> -0.000000
basx620 toSci -0.0000000        -> -0E-7
basx621 toSci -0.00000000       -> -0E-8
basx622 toSci -0.*********      -> -0E-9

basx630 toSci  0.00E+0          -> 0.00
basx631 toSci  0.00E+1          -> 0.0
basx632 toSci  0.00E+2          -> 0
basx633 toSci  0.00E+3          -> 0E+1
basx634 toSci  0.00E+4          -> 0E+2
basx635 toSci  0.00E+5          -> 0E+3
basx636 toSci  0.00E+6          -> 0E+4
basx637 toSci  0.00E+7          -> 0E+5
basx638 toSci  0.00E+8          -> 0E+6
basx639 toSci  0.00E+9          -> 0E+7

basx640 toSci  0.0E+0           -> 0.0
basx641 toSci  0.0E+1           -> 0
basx642 toSci  0.0E+2           -> 0E+1
basx643 toSci  0.0E+3           -> 0E+2
basx644 toSci  0.0E+4           -> 0E+3
basx645 toSci  0.0E+5           -> 0E+4
basx646 toSci  0.0E+6           -> 0E+5
basx647 toSci  0.0E+7           -> 0E+6
basx648 toSci  0.0E+8           -> 0E+7
basx649 toSci  0.0E+9           -> 0E+8

basx650 toSci  0E+0             -> 0
basx651 toSci  0E+1             -> 0E+1
basx652 toSci  0E+2             -> 0E+2
basx653 toSci  0E+3             -> 0E+3
basx654 toSci  0E+4             -> 0E+4
basx655 toSci  0E+5             -> 0E+5
basx656 toSci  0E+6             -> 0E+6
basx657 toSci  0E+7             -> 0E+7
basx658 toSci  0E+8             -> 0E+8
basx659 toSci  0E+9             -> 0E+9

basx660 toSci  0.0E-0           -> 0.0
basx661 toSci  0.0E-1           -> 0.00
basx662 toSci  0.0E-2           -> 0.000
basx663 toSci  0.0E-3           -> 0.0000
basx664 toSci  0.0E-4           -> 0.00000
basx665 toSci  0.0E-5           -> 0.000000
basx666 toSci  0.0E-6           -> 0E-7
basx667 toSci  0.0E-7           -> 0E-8
basx668 toSci  0.0E-8           -> 0E-9
basx669 toSci  0.0E-9           -> 0E-10

basx670 toSci  0.00E-0          -> 0.00
basx671 toSci  0.00E-1          -> 0.000
basx672 toSci  0.00E-2          -> 0.0000
basx673 toSci  0.00E-3          -> 0.00000
basx674 toSci  0.00E-4          -> 0.000000
basx675 toSci  0.00E-5          -> 0E-7
basx676 toSci  0.00E-6          -> 0E-8
basx677 toSci  0.00E-7          -> 0E-9
basx678 toSci  0.00E-8          -> 0E-10
basx679 toSci  0.00E-9          -> 0E-11

basx680 toSci  000000.          ->  0
basx681 toSci   00000.          ->  0
basx682 toSci    0000.          ->  0
basx683 toSci     000.          ->  0
basx684 toSci      00.          ->  0
basx685 toSci       0.          ->  0
basx686 toSci  +00000.          ->  0
basx687 toSci  -00000.          -> -0
basx688 toSci  +0.              ->  0
basx689 toSci  -0.              -> -0

-- Specials
precision: 4
basx700 toSci "NaN"             -> NaN
basx701 toSci "nan"             -> NaN
basx702 toSci "nAn"             -> NaN
basx703 toSci "NAN"             -> NaN
basx704 toSci "+NaN"            -> NaN
basx705 toSci "+nan"            -> NaN
basx706 toSci "+nAn"            -> NaN
basx707 toSci "+NAN"            -> NaN
basx708 toSci "-NaN"            -> -NaN
basx709 toSci "-nan"            -> -NaN
basx710 toSci "-nAn"            -> -NaN
basx711 toSci "-NAN"            -> -NaN
basx712 toSci 'NaN0'            -> NaN
basx713 toSci 'NaN1'            -> NaN1
basx714 toSci 'NaN12'           -> NaN12
basx715 toSci 'NaN123'          -> NaN123
basx716 toSci 'NaN1234'         -> NaN1234
basx717 toSci 'NaN01'           -> NaN1
basx718 toSci 'NaN012'          -> NaN12
basx719 toSci 'NaN0123'         -> NaN123
basx720 toSci 'NaN01234'        -> NaN1234
basx721 toSci 'NaN001'          -> NaN1
basx722 toSci 'NaN0012'         -> NaN12
basx723 toSci 'NaN00123'        -> NaN123
basx724 toSci 'NaN001234'       -> NaN1234
basx725 toSci 'NaN12345'        -> NaN Conversion_syntax
basx726 toSci 'NaN123e+1'       -> NaN Conversion_syntax
basx727 toSci 'NaN12.45'        -> NaN Conversion_syntax
basx728 toSci 'NaN-12'          -> NaN Conversion_syntax
basx729 toSci 'NaN+12'          -> NaN Conversion_syntax

basx730 toSci "sNaN"            -> sNaN
basx731 toSci "snan"            -> sNaN
basx732 toSci "SnAn"            -> sNaN
basx733 toSci "SNAN"            -> sNaN
basx734 toSci "+sNaN"           -> sNaN
basx735 toSci "+snan"           -> sNaN
basx736 toSci "+SnAn"           -> sNaN
basx737 toSci "+SNAN"           -> sNaN
basx738 toSci "-sNaN"           -> -sNaN
basx739 toSci "-snan"           -> -sNaN
basx740 toSci "-SnAn"           -> -sNaN
basx741 toSci "-SNAN"           -> -sNaN
basx742 toSci 'sNaN0000'        -> sNaN
basx743 toSci 'sNaN7'           -> sNaN7
basx744 toSci 'sNaN007234'      -> sNaN7234
basx745 toSci 'sNaN72345'       -> NaN Conversion_syntax
basx746 toSci 'sNaN72.45'       -> NaN Conversion_syntax
basx747 toSci 'sNaN-72'         -> NaN Conversion_syntax

basx748 toSci "Inf"             -> Infinity
basx749 toSci "inf"             -> Infinity
basx750 toSci "iNf"             -> Infinity
basx751 toSci "INF"             -> Infinity
basx752 toSci "+Inf"            -> Infinity
basx753 toSci "+inf"            -> Infinity
basx754 toSci "+iNf"            -> Infinity
basx755 toSci "+INF"            -> Infinity
basx756 toSci "-Inf"            -> -Infinity
basx757 toSci "-inf"            -> -Infinity
basx758 toSci "-iNf"            -> -Infinity
basx759 toSci "-INF"            -> -Infinity

basx760 toSci "Infinity"        -> Infinity
basx761 toSci "infinity"        -> Infinity
basx762 toSci "iNfInItY"        -> Infinity
basx763 toSci "INFINITY"        -> Infinity
basx764 toSci "+Infinity"       -> Infinity
basx765 toSci "+infinity"       -> Infinity
basx766 toSci "+iNfInItY"       -> Infinity
basx767 toSci "+INFINITY"       -> Infinity
basx768 toSci "-Infinity"       -> -Infinity
basx769 toSci "-infinity"       -> -Infinity
basx770 toSci "-iNfInItY"       -> -Infinity
basx771 toSci "-INFINITY"       -> -Infinity

-- Specials and zeros for toEng
basx772 toEng "NaN"              -> NaN
basx773 toEng "-Infinity"        -> -Infinity
basx774 toEng "-sNaN"            -> -sNaN
basx775 toEng "-NaN"             -> -NaN
basx776 toEng "+Infinity"        -> Infinity
basx778 toEng "+sNaN"            -> sNaN
basx779 toEng "+NaN"             -> NaN
basx780 toEng "INFINITY"         -> Infinity
basx781 toEng "SNAN"             -> sNaN
basx782 toEng "NAN"              -> NaN
basx783 toEng "infinity"         -> Infinity
basx784 toEng "snan"             -> sNaN
basx785 toEng "nan"              -> NaN
basx786 toEng "InFINITY"         -> Infinity
basx787 toEng "SnAN"             -> sNaN
basx788 toEng "nAN"              -> NaN
basx789 toEng "iNfinity"         -> Infinity
basx790 toEng "sNan"             -> sNaN
basx791 toEng "Nan"              -> NaN
basx792 toEng "Infinity"         -> Infinity
basx793 toEng "sNaN"             -> sNaN

-- Zero toEng, etc.
basx800 toEng 0e+1              -> "0.00E+3"  -- doc example

basx801 toEng 0.*********       -> 0E-9
basx802 toEng 0.00000000        -> 0.00E-6
basx803 toEng 0.0000000         -> 0.0E-6
basx804 toEng 0.000000          -> 0.000000
basx805 toEng 0.00000           -> 0.00000
basx806 toEng 0.0000            -> 0.0000
basx807 toEng 0.000             -> 0.000
basx808 toEng 0.00              -> 0.00
basx809 toEng 0.0               -> 0.0
basx810 toEng  .0               -> 0.0
basx811 toEng 0.                -> 0
basx812 toEng -.0               -> -0.0
basx813 toEng -0.               -> -0
basx814 toEng -0.0              -> -0.0
basx815 toEng -0.00             -> -0.00
basx816 toEng -0.000            -> -0.000
basx817 toEng -0.0000           -> -0.0000
basx818 toEng -0.00000          -> -0.00000
basx819 toEng -0.000000         -> -0.000000
basx820 toEng -0.0000000        -> -0.0E-6
basx821 toEng -0.00000000       -> -0.00E-6
basx822 toEng -0.*********      -> -0E-9

basx830 toEng  0.00E+0          -> 0.00
basx831 toEng  0.00E+1          -> 0.0
basx832 toEng  0.00E+2          -> 0
basx833 toEng  0.00E+3          -> 0.00E+3
basx834 toEng  0.00E+4          -> 0.0E+3
basx835 toEng  0.00E+5          -> 0E+3
basx836 toEng  0.00E+6          -> 0.00E+6
basx837 toEng  0.00E+7          -> 0.0E+6
basx838 toEng  0.00E+8          -> 0E+6
basx839 toEng  0.00E+9          -> 0.00E+9

basx840 toEng  0.0E+0           -> 0.0
basx841 toEng  0.0E+1           -> 0
basx842 toEng  0.0E+2           -> 0.00E+3
basx843 toEng  0.0E+3           -> 0.0E+3
basx844 toEng  0.0E+4           -> 0E+3
basx845 toEng  0.0E+5           -> 0.00E+6
basx846 toEng  0.0E+6           -> 0.0E+6
basx847 toEng  0.0E+7           -> 0E+6
basx848 toEng  0.0E+8           -> 0.00E+9
basx849 toEng  0.0E+9           -> 0.0E+9

basx850 toEng  0E+0             -> 0
basx851 toEng  0E+1             -> 0.00E+3
basx852 toEng  0E+2             -> 0.0E+3
basx853 toEng  0E+3             -> 0E+3
basx854 toEng  0E+4             -> 0.00E+6
basx855 toEng  0E+5             -> 0.0E+6
basx856 toEng  0E+6             -> 0E+6
basx857 toEng  0E+7             -> 0.00E+9
basx858 toEng  0E+8             -> 0.0E+9
basx859 toEng  0E+9             -> 0E+9

basx860 toEng  0.0E-0           -> 0.0
basx861 toEng  0.0E-1           -> 0.00
basx862 toEng  0.0E-2           -> 0.000
basx863 toEng  0.0E-3           -> 0.0000
basx864 toEng  0.0E-4           -> 0.00000
basx865 toEng  0.0E-5           -> 0.000000
basx866 toEng  0.0E-6           -> 0.0E-6
basx867 toEng  0.0E-7           -> 0.00E-6
basx868 toEng  0.0E-8           -> 0E-9
basx869 toEng  0.0E-9           -> 0.0E-9

basx870 toEng  0.00E-0          -> 0.00
basx871 toEng  0.00E-1          -> 0.000
basx872 toEng  0.00E-2          -> 0.0000
basx873 toEng  0.00E-3          -> 0.00000
basx874 toEng  0.00E-4          -> 0.000000
basx875 toEng  0.00E-5          -> 0.0E-6
basx876 toEng  0.00E-6          -> 0.00E-6
basx877 toEng  0.00E-7          -> 0E-9
basx878 toEng  0.00E-8          -> 0.0E-9
basx879 toEng  0.00E-9          -> 0.00E-9


rounding:  half_up
precision: 9
-- subnormals and overflows
basx906 toSci '99e999999999'       -> Infinity Overflow  Inexact Rounded
basx907 toSci '999e999999999'      -> Infinity Overflow  Inexact Rounded
basx908 toSci '0.9e-999999999'     -> 9E-1********* Subnormal
basx909 toSci '0.09e-999999999'    -> 9E-1000000001 Subnormal
basx910 toSci '0.1e1*********'     -> 1E+999999999
basx911 toSci '10e-1*********'     -> 1.0E-999999999
basx912 toSci '0.9e9999999999'     -> Infinity Overflow  Inexact Rounded
basx913 toSci '99e-9999999999'     -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
basx914 toSci '111e9999999999'     -> Infinity Overflow  Inexact Rounded
basx915 toSci '1111e-9999999999'   -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
basx916 toSci '1111e-99999999999'  -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
basx917 toSci '7e1*********'       -> Infinity Overflow  Inexact Rounded
-- negatives the same
basx918 toSci '-99e999999999'      -> -Infinity Overflow  Inexact Rounded
basx919 toSci '-999e999999999'     -> -Infinity Overflow  Inexact Rounded
basx920 toSci '-0.9e-999999999'    -> -9E-1********* Subnormal
basx921 toSci '-0.09e-999999999'   -> -9E-1000000001 Subnormal
basx922 toSci '-0.1e1*********'    -> -1E+999999999
basx923 toSci '-10e-1*********'    -> -1.0E-999999999
basx924 toSci '-0.9e9999999999'    -> -Infinity Overflow  Inexact Rounded
basx925 toSci '-99e-9999999999'    -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
basx926 toSci '-111e9999999999'    -> -Infinity Overflow  Inexact Rounded
basx927 toSci '-1111e-9999999999'  -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
basx928 toSci '-1111e-99999999999' -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
basx929 toSci '-7e1*********'      -> -Infinity Overflow  Inexact Rounded

rounding:  ceiling
basx930 toSci  '7e1*********'      ->  Infinity Overflow  Inexact Rounded
basx931 toSci '-7e1*********'      -> -9.99999999E+999999999 Overflow  Inexact Rounded
rounding:  up
basx932 toSci  '7e1*********'      ->  Infinity Overflow  Inexact Rounded
basx933 toSci '-7e1*********'      -> -Infinity Overflow  Inexact Rounded
rounding:  down
basx934 toSci  '7e1*********'      ->  9.99999999E+999999999 Overflow  Inexact Rounded
basx935 toSci '-7e1*********'      -> -9.99999999E+999999999 Overflow  Inexact Rounded
rounding:  floor
basx936 toSci  '7e1*********'      ->  9.99999999E+999999999 Overflow  Inexact Rounded
basx937 toSci '-7e1*********'      -> -Infinity Overflow  Inexact Rounded

rounding:  half_up
basx938 toSci  '7e1*********'      ->  Infinity Overflow  Inexact Rounded
basx939 toSci '-7e1*********'      -> -Infinity Overflow  Inexact Rounded
rounding:  half_even
basx940 toSci  '7e1*********'      ->  Infinity Overflow  Inexact Rounded
basx941 toSci '-7e1*********'      -> -Infinity Overflow  Inexact Rounded
rounding:  half_down
basx942 toSci  '7e1*********'      ->  Infinity Overflow  Inexact Rounded
basx943 toSci '-7e1*********'      -> -Infinity Overflow  Inexact Rounded

rounding:  half_even


-- Giga exponent initial tests
maxExponent: 999999999
minExponent: -999999999

basx951 toSci '99e999'          -> '9.9E+1000'
basx952 toSci '999e999'         -> '9.99E+1001'
basx953 toSci '0.9e-999'        -> '9E-1000'
basx954 toSci '0.09e-999'       -> '9E-1001'
basx955 toSci '0.1e1001'        -> '1E+1000'
basx956 toSci '10e-1001'        -> '1.0E-1000'
basx957 toSci '0.9e9999'        -> '9E+9998'
basx958 toSci '99e-9999'        -> '9.9E-9998'
basx959 toSci '111e9997'        -> '1.11E+9999'
basx960 toSci '1111e-9999'      -> '1.111E-9996'
basx961 toSci '99e9999'         -> '9.9E+10000'
basx962 toSci '999e9999'        -> '9.99E+10001'
basx963 toSci '0.9e-9999'       -> '9E-10000'
basx964 toSci '0.09e-9999'      -> '9E-10001'
basx965 toSci '0.1e10001'       -> '1E+10000'
basx966 toSci '10e-10001'       -> '1.0E-10000'
basx967 toSci '0.9e99999'       -> '9E+99998'
basx968 toSci '99e-99999'       -> '9.9E-99998'
basx969 toSci '111e99999'       -> '1.11E+100001'
basx970 toSci '1111e-99999'     -> '1.111E-99996'
basx971 toSci "0.09e999999999"  -> '9E+999999997'
basx972 toSci "0.9e999999999"   -> '9E+999999998'
basx973 toSci "9e999999999"     -> '9E+999999999'
basx974 toSci "9.9e999999999"   -> '9.9E+999999999'
basx975 toSci "9.99e999999999"  -> '9.99E+999999999'
basx976 toSci "9.99e-999999999" -> '9.99E-999999999'
basx977 toSci "9.9e-999999999"  -> '9.9E-999999999'
basx978 toSci "9e-999999999"    -> '9E-999999999'
basx979 toSci "99e-999999999"   -> '9.9E-999999998'
basx980 toSci "999e-999999999"  -> '9.99E-999999997'

-- Varying exponent maximums
precision: 5
maxexponent: 0
minexponent: 0
emax001 toSci -1E+2  -> -Infinity Overflow Inexact Rounded
emax002 toSci -100   -> -Infinity Overflow Inexact Rounded
emax003 toSci  -10   -> -Infinity Overflow Inexact Rounded
emax004 toSci   -9.9 -> -9.9
emax005 toSci   -9   -> -9
emax006 toSci   -1   -> -1
emax007 toSci    0   ->  0
emax008 toSci    1   ->  1
emax009 toSci    9   ->  9
emax010 toSci    9.9 ->  9.9
emax011 toSci   10   ->  Infinity Overflow Inexact Rounded
emax012 toSci  100   ->  Infinity Overflow Inexact Rounded
emax013 toSci  1E+2  ->  Infinity Overflow Inexact Rounded
emax014 toSci   0.99 ->  0.99 Subnormal
emax015 toSci   0.1  ->  0.1 Subnormal
emax016 toSci   0.01 ->  0.01 Subnormal
emax017 toSci  1E-1  ->  0.1 Subnormal
emax018 toSci  1E-2  ->  0.01 Subnormal

maxexponent: 1
minexponent: -1
emax100 toSci -1E+3  -> -Infinity Overflow Inexact Rounded
emax101 toSci -1E+2  -> -Infinity Overflow Inexact Rounded
emax102 toSci -100   -> -Infinity Overflow Inexact Rounded
emax103 toSci  -10   -> -10
emax104 toSci   -9.9 -> -9.9
emax105 toSci   -9   -> -9
emax106 toSci   -1   -> -1
emax107 toSci    0   ->  0
emax108 toSci    1   ->  1
emax109 toSci    9   ->  9
emax110 toSci    9.9 ->  9.9
emax111 toSci   10   -> 10
emax112 toSci  100   ->  Infinity Overflow Inexact Rounded
emax113 toSci  1E+2  ->  Infinity Overflow Inexact Rounded
emax114 toSci  1E+3  ->  Infinity Overflow Inexact Rounded
emax115 toSci   0.99 ->  0.99
emax116 toSci   0.1  ->  0.1
emax117 toSci   0.01 ->  0.01 Subnormal
emax118 toSci  1E-1  ->  0.1
emax119 toSci  1E-2  ->  0.01 Subnormal
emax120 toSci  1E-3  ->  0.001 Subnormal
emax121 toSci  1.1E-3  ->  0.0011 Subnormal
emax122 toSci  1.11E-3  ->  0.00111 Subnormal
emax123 toSci  1.111E-3  ->  0.00111 Subnormal Underflow Inexact Rounded
emax124 toSci  1.1111E-3  ->  0.00111 Subnormal Underflow Inexact Rounded
emax125 toSci  1.11111E-3  ->  0.00111 Subnormal Underflow Inexact Rounded

maxexponent: 2
minexponent: -2
precision: 9
emax200 toSci -1E+3  -> -Infinity Overflow Inexact Rounded
emax201 toSci -1E+2  -> -1E+2
emax202 toSci -100   -> -100
emax203 toSci  -10   -> -10
emax204 toSci   -9.9 -> -9.9
emax205 toSci   -9   -> -9
emax206 toSci   -1   -> -1
emax207 toSci    0   ->  0
emax208 toSci    1   ->  1
emax209 toSci    9   ->  9
emax210 toSci    9.9 ->  9.9
emax211 toSci   10   -> 10
emax212 toSci  100   -> 100
emax213 toSci  1E+2  -> 1E+2
emax214 toSci  1E+3  ->  Infinity Overflow Inexact Rounded
emax215 toSci   0.99 ->  0.99
emax216 toSci   0.1  ->  0.1
emax217 toSci   0.01 ->  0.01
emax218 toSci  0.001 ->  0.001 Subnormal
emax219 toSci  1E-1  ->  0.1
emax220 toSci  1E-2  ->  0.01
emax221 toSci  1E-3  ->  0.001 Subnormal
emax222 toSci  1E-4  ->  0.0001 Subnormal
emax223 toSci  1E-5  ->  0.00001 Subnormal
emax224 toSci  1E-6  ->  0.000001 Subnormal
emax225 toSci  1E-7  ->  1E-7  Subnormal
emax226 toSci  1E-8  ->  1E-8  Subnormal
emax227 toSci  1E-9  ->  1E-9  Subnormal
emax228 toSci  1E-10 ->  1E-10 Subnormal
emax229 toSci  1E-11 ->  0E-10 Underflow Subnormal Inexact Rounded Clamped
emax230 toSci  1E-12 ->  0E-10 Underflow Subnormal Inexact Rounded Clamped

maxexponent: 7
minexponent: -7
emax231 toSci  1E-8  ->  1E-8 Subnormal
emax232 toSci  1E-7  ->  1E-7
emax233 toSci  1E-6  ->  0.000001
emax234 toSci  1E-5  ->  0.00001
emax235 toSci  1E+5  ->  1E+5
emax236 toSci  1E+6  ->  1E+6
emax237 toSci  1E+7  ->  1E+7
emax238 toSci  1E+8  ->  Infinity Overflow Inexact Rounded

maxexponent: 9
minexponent: -9
emax240 toSci  1E-21 ->  0E-17 Subnormal Underflow Inexact Rounded Clamped
emax241 toSci  1E-10 ->  1E-10 Subnormal
emax242 toSci  1E-9  ->  1E-9
emax243 toSci  1E-8  ->  1E-8
emax244 toSci  1E-7  ->  1E-7
emax245 toSci  1E+7  ->  1E+7
emax246 toSci  1E+8  ->  1E+8
emax247 toSci  1E+9  ->  1E+9
emax248 toSci  1E+10 ->  Infinity Overflow Inexact Rounded

maxexponent: 10  -- boundary
minexponent: -10
emax250 toSci  1E-21 ->  0E-18 Underflow Subnormal Inexact Rounded Clamped
emax251 toSci  1E-11 ->  1E-11 Subnormal
emax252 toSci  1E-10 ->  1E-10
emax253 toSci  1E-9  ->  1E-9
emax254 toSci  1E-8  ->  1E-8
emax255 toSci  1E+8  ->  1E+8
emax256 toSci  1E+9  ->  1E+9
emax257 toSci  1E+10 ->  1E+10
emax258 toSci  1E+11 ->  Infinity Overflow Inexact Rounded

emax260 toSci  1.00E-21 ->  0E-18 Underflow Subnormal Inexact Rounded Clamped
emax261 toSci  1.00E-11 ->  1.00E-11 Subnormal
emax262 toSci  1.00E-10 ->  1.00E-10
emax263 toSci  1.00E-9  ->  1.00E-9
emax264 toSci  1.00E-8  ->  1.00E-8
emax265 toSci  1.00E+8  ->  1.00E+8
emax266 toSci  1.00E+9  ->  1.00E+9
emax267 toSci  1.00E+10 ->  1.00E+10
emax268 toSci  1.00E+11 ->  Infinity Overflow Inexact Rounded
emax270 toSci  9.99E-21 ->  0E-18 Underflow Subnormal Inexact Rounded Clamped
emax271 toSci  9.99E-11 ->  9.99E-11 Subnormal
emax272 toSci  9.99E-10 ->  9.99E-10
emax273 toSci  9.99E-9  ->  9.99E-9
emax274 toSci  9.99E-8  ->  9.99E-8
emax275 toSci  9.99E+8  ->  9.99E+8
emax276 toSci  9.99E+9  ->  9.99E+9
emax277 toSci  9.99E+10 ->  9.99E+10
emax278 toSci  9.99E+11 ->  Infinity Overflow Inexact Rounded

maxexponent: 99
minexponent: -99
emax280 toSci  1E-120 ->  0E-107 Underflow Subnormal Inexact Rounded Clamped
emax281 toSci  1E-100 ->  1E-100 Subnormal
emax282 toSci  1E-99  ->  1E-99
emax283 toSci  1E-98  ->  1E-98
emax284 toSci  1E+98  ->  1E+98
emax285 toSci  1E+99  ->  1E+99
emax286 toSci  1E+100 ->  Infinity Overflow Inexact Rounded

maxexponent: 999
minexponent: -999
emax291 toSci  1E-1000 ->  1E-1000 Subnormal
emax292 toSci  1E-999  ->  1E-999
emax293 toSci  1E+999  ->  1E+999
emax294 toSci  1E+1000 ->  Infinity Overflow Inexact Rounded
maxexponent: 9999
minexponent: -9999
emax301 toSci  1E-10000 ->  1E-10000 Subnormal
emax302 toSci  1E-9999  ->  1E-9999
emax303 toSci  1E+9999  ->  1E+9999
emax304 toSci  1E+10000 ->  Infinity Overflow Inexact Rounded
maxexponent: 99999
minexponent: -99999
emax311 toSci  1E-100000 ->  1E-100000 Subnormal
emax312 toSci  1E-99999  ->  1E-99999
emax313 toSci  1E+99999  ->  1E+99999
emax314 toSci  1E+100000 ->  Infinity Overflow Inexact Rounded
maxexponent: 999999
minexponent: -999999
emax321 toSci  1E-1000000 ->  1E-1000000 Subnormal
emax322 toSci  1E-999999  ->  1E-999999
emax323 toSci  1E+999999  ->  1E+999999
emax324 toSci  1E+1000000 ->  Infinity Overflow Inexact Rounded
maxexponent: 9999999
minexponent: -9999999
emax331 toSci  1E-10000000 ->  1E-10000000 Subnormal
emax332 toSci  1E-9999999  ->  1E-9999999
emax333 toSci  1E+9999999  ->  1E+9999999
emax334 toSci  1E+10000000 ->  Infinity Overflow Inexact Rounded
maxexponent: 99999999
minexponent: -99999999
emax341 toSci  1E-100000000 ->  1E-100000000 Subnormal
emax342 toSci  1E-99999999  ->  1E-99999999
emax343 toSci  1E+99999999  ->  1E+99999999
emax344 toSci  1E+100000000 ->  Infinity Overflow Inexact Rounded

maxexponent: 999999999
minexponent: -999999999
emax347 toSci  1E-1000000008     ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
emax348 toSci  1E-1000000007     ->  1E-1000000007 Subnormal
emax349 toSci  1E-1*********     ->  1E-1********* Subnormal
emax350 toSci  1E-999999999      ->  1E-999999999
emax351 toSci  1E+999999999      ->  1E+999999999
emax352 toSci  1E+1*********     ->  Infinity Overflow Inexact Rounded
emax353 toSci  1.000E-1********* ->  1.000E-1********* Subnormal
emax354 toSci  1.000E-999999999  ->  1.000E-999999999
emax355 toSci  1.000E+999999999  ->  1.000E+999999999
emax356 toSci  1.000E+1********* ->  Infinity Overflow Inexact Rounded
emax357 toSci  1.001E-1000000008 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
emax358 toSci  1.001E-1000000007 ->  1E-1000000007 Subnormal Inexact Rounded Underflow
emax359 toSci  1.001E-1********* ->  1.001E-1********* Subnormal
emax360 toSci  1.001E-999999999  ->  1.001E-999999999
emax361 toSci  1.001E+999999999  ->  1.001E+999999999
emax362 toSci  1.001E+1********* ->  Infinity Overflow Inexact Rounded
emax363 toSci  9.000E-1********* ->  9.000E-1********* Subnormal
emax364 toSci  9.000E-999999999  ->  9.000E-999999999
emax365 toSci  9.000E+999999999  ->  9.000E+999999999
emax366 toSci  9.000E+1********* ->  Infinity Overflow Inexact Rounded
emax367 toSci  9.999E-1000000009 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
emax368 toSci  9.999E-1000000008 ->  1E-1000000007 Underflow Subnormal Inexact Rounded
emax369 toSci  9.999E-1000000007 ->  1.0E-1000000006 Underflow Subnormal Inexact Rounded
emax370 toSci  9.999E-1********* ->  9.999E-1********* Subnormal
emax371 toSci  9.999E-999999999  ->  9.999E-999999999
emax372 toSci  9.999E+999999999  ->  9.999E+999999999

emax373 toSci  9.999E+1********* ->  Infinity Overflow Inexact Rounded
emax374 toSci -1E-1*********     -> -1E-1********* Subnormal
emax375 toSci -1E-999999999      -> -1E-999999999
emax376 toSci -1E+999999999      -> -1E+999999999
emax377 toSci -1E+1*********     -> -Infinity Overflow Inexact Rounded
emax378 toSci -1.000E-1********* -> -1.000E-1********* Subnormal
emax379 toSci -1.000E-999999999  -> -1.000E-999999999
emax380 toSci -1.000E+999999999  -> -1.000E+999999999
emax381 toSci -1.000E+1********* -> -Infinity Overflow Inexact Rounded
emax382 toSci -1.001E-1000000008 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
emax383 toSci -1.001E-999999999  -> -1.001E-999999999
emax384 toSci -1.001E+999999999  -> -1.001E+999999999
emax385 toSci -1.001E+1********* -> -Infinity Overflow Inexact Rounded
emax386 toSci -9.000E-1000000123 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
emax387 toSci -9.000E-999999999  -> -9.000E-999999999
emax388 toSci -9.000E+999999999  -> -9.000E+999999999
emax389 toSci -9.000E+1********* -> -Infinity Overflow Inexact Rounded
emax390 toSci -9.999E-1000000008 -> -1E-1000000007 Underflow Subnormal Inexact Rounded
emax391 toSci -9.999E-999999999  -> -9.999E-999999999
emax392 toSci -9.999E+999999999  -> -9.999E+999999999
emax393 toSci -9.999E+1********* -> -Infinity Overflow Inexact Rounded

-- Now check 854 rounding of subnormals and proper underflow to 0
precision:   5
maxExponent: 999
minexponent: -999
rounding:    half_even

emax400 toSci  1.0000E-999     -> 1.0000E-999
emax401 toSci  0.1E-999        -> 1E-1000     Subnormal
emax402 toSci  0.1000E-999     -> 1.000E-1000 Subnormal
emax403 toSci  0.0100E-999     -> 1.00E-1001  Subnormal
emax404 toSci  0.0010E-999     -> 1.0E-1002   Subnormal
emax405 toSci  0.0001E-999     -> 1E-1003     Subnormal
emax406 toSci  0.00010E-999    -> 1E-1003     Subnormal Rounded
emax407 toSci  0.00013E-999    -> 1E-1003     Underflow Subnormal Inexact Rounded
emax408 toSci  0.00015E-999    -> 2E-1003     Underflow Subnormal Inexact Rounded
emax409 toSci  0.00017E-999    -> 2E-1003     Underflow Subnormal Inexact Rounded
emax410 toSci  0.00023E-999    -> 2E-1003     Underflow Subnormal Inexact Rounded
emax411 toSci  0.00025E-999    -> 2E-1003     Underflow Subnormal Inexact Rounded
emax412 toSci  0.00027E-999    -> 3E-1003     Underflow Subnormal Inexact Rounded
emax413 toSci  0.000149E-999   -> 1E-1003     Underflow Subnormal Inexact Rounded
emax414 toSci  0.000150E-999   -> 2E-1003     Underflow Subnormal Inexact Rounded
emax415 toSci  0.000151E-999   -> 2E-1003     Underflow Subnormal Inexact Rounded
emax416 toSci  0.000249E-999   -> 2E-1003     Underflow Subnormal Inexact Rounded
emax417 toSci  0.000250E-999   -> 2E-1003     Underflow Subnormal Inexact Rounded
emax418 toSci  0.000251E-999   -> 3E-1003     Underflow Subnormal Inexact Rounded
emax419 toSci  0.00009E-999    -> 1E-1003     Underflow Subnormal Inexact Rounded
emax420 toSci  0.00005E-999    -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped
emax421 toSci  0.00003E-999    -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped
emax422 toSci  0.000009E-999   -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped
emax423 toSci  0.000005E-999   -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped
emax424 toSci  0.000003E-999   -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped

emax425 toSci  0.001049E-999   -> 1.0E-1002   Underflow Subnormal Inexact Rounded
emax426 toSci  0.001050E-999   -> 1.0E-1002   Underflow Subnormal Inexact Rounded
emax427 toSci  0.001051E-999   -> 1.1E-1002   Underflow Subnormal Inexact Rounded
emax428 toSci  0.001149E-999   -> 1.1E-1002   Underflow Subnormal Inexact Rounded
emax429 toSci  0.001150E-999   -> 1.2E-1002   Underflow Subnormal Inexact Rounded
emax430 toSci  0.001151E-999   -> 1.2E-1002   Underflow Subnormal Inexact Rounded

emax432 toSci  0.010049E-999   -> 1.00E-1001  Underflow Subnormal Inexact Rounded
emax433 toSci  0.010050E-999   -> 1.00E-1001  Underflow Subnormal Inexact Rounded
emax434 toSci  0.010051E-999   -> 1.01E-1001  Underflow Subnormal Inexact Rounded
emax435 toSci  0.010149E-999   -> 1.01E-1001  Underflow Subnormal Inexact Rounded
emax436 toSci  0.010150E-999   -> 1.02E-1001  Underflow Subnormal Inexact Rounded
emax437 toSci  0.010151E-999   -> 1.02E-1001  Underflow Subnormal Inexact Rounded

emax440 toSci  0.10103E-999    -> 1.010E-1000 Underflow Subnormal Inexact Rounded
emax441 toSci  0.10105E-999    -> 1.010E-1000 Underflow Subnormal Inexact Rounded
emax442 toSci  0.10107E-999    -> 1.011E-1000 Underflow Subnormal Inexact Rounded
emax443 toSci  0.10113E-999    -> 1.011E-1000 Underflow Subnormal Inexact Rounded
emax444 toSci  0.10115E-999    -> 1.012E-1000 Underflow Subnormal Inexact Rounded
emax445 toSci  0.10117E-999    -> 1.012E-1000 Underflow Subnormal Inexact Rounded

emax450 toSci  1.10730E-1000   -> 1.107E-1000 Underflow Subnormal Inexact Rounded
emax451 toSci  1.10750E-1000   -> 1.108E-1000 Underflow Subnormal Inexact Rounded
emax452 toSci  1.10770E-1000   -> 1.108E-1000 Underflow Subnormal Inexact Rounded
emax453 toSci  1.10830E-1000   -> 1.108E-1000 Underflow Subnormal Inexact Rounded
emax454 toSci  1.10850E-1000   -> 1.108E-1000 Underflow Subnormal Inexact Rounded
emax455 toSci  1.10870E-1000   -> 1.109E-1000 Underflow Subnormal Inexact Rounded

-- make sure sign OK
emax456 toSci  -0.10103E-999   -> -1.010E-1000 Underflow Subnormal Inexact Rounded
emax457 toSci  -0.10105E-999   -> -1.010E-1000 Underflow Subnormal Inexact Rounded
emax458 toSci  -0.10107E-999   -> -1.011E-1000 Underflow Subnormal Inexact Rounded
emax459 toSci  -0.10113E-999   -> -1.011E-1000 Underflow Subnormal Inexact Rounded
emax460 toSci  -0.10115E-999   -> -1.012E-1000 Underflow Subnormal Inexact Rounded
emax461 toSci  -0.10117E-999   -> -1.012E-1000 Underflow Subnormal Inexact Rounded

-- '999s' cases
emax464 toSci  999999E-999         -> 1.0000E-993 Inexact Rounded
emax465 toSci  99999.0E-999        -> 9.9999E-995 Rounded
emax466 toSci  99999.E-999         -> 9.9999E-995
emax467 toSci  9999.9E-999         -> 9.9999E-996
emax468 toSci  999.99E-999         -> 9.9999E-997
emax469 toSci  99.999E-999         -> 9.9999E-998
emax470 toSci  9.9999E-999         -> 9.9999E-999
emax471 toSci  0.99999E-999        -> 1.0000E-999 Underflow Subnormal Inexact Rounded
emax472 toSci  0.099999E-999       -> 1.000E-1000 Underflow Subnormal Inexact Rounded
emax473 toSci  0.0099999E-999      -> 1.00E-1001  Underflow Subnormal Inexact Rounded
emax474 toSci  0.00099999E-999     -> 1.0E-1002   Underflow Subnormal Inexact Rounded
emax475 toSci  0.000099999E-999    -> 1E-1003     Underflow Subnormal Inexact Rounded
emax476 toSci  0.0000099999E-999   -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped
emax477 toSci  0.00000099999E-999  -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped
emax478 toSci  0.000000099999E-999 -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped

-- Exponents with insignificant leading zeros
precision:   16
maxExponent: 999999999
minexponent: -999999999
basx1001 toSci  1e999999999 -> 1E+999999999
basx1002 toSci  1e0999999999 -> 1E+999999999
basx1003 toSci  1e00999999999 -> 1E+999999999
basx1004 toSci  1e000999999999 -> 1E+999999999
basx1005 toSci  1e*********000999999999 -> 1E+999999999
basx1006 toSci  1e*********001000000007 -> Infinity Overflow Inexact Rounded
basx1007 toSci  1e-999999999 -> 1E-999999999
basx1008 toSci  1e-0999999999 -> 1E-999999999
basx1009 toSci  1e-00999999999 -> 1E-999999999
basx1010 toSci  1e-000999999999 -> 1E-999999999
basx1011 toSci  1e-*********000999999999 -> 1E-999999999
basx1012 toSci  1e-*********001000000007 -> 1E-1000000007 Subnormal

-- Edge cases for int32 exponents...
basx1021 tosci 1e+2147483649 -> Infinity Overflow Inexact Rounded
basx1022 tosci 1e+2147483648 -> Infinity Overflow Inexact Rounded
basx1023 tosci 1e+2147483647 -> Infinity Overflow Inexact Rounded
basx1024 tosci 1e-2147483647 -> 0E-1000000014 Underflow Subnormal Inexact Rounded Clamped
basx1025 tosci 1e-2147483648 -> 0E-1000000014 Underflow Subnormal Inexact Rounded Clamped
basx1026 tosci 1e-2147483649 -> 0E-1000000014 Underflow Subnormal Inexact Rounded Clamped
-- same unbalanced
precision:   7
maxExponent: 96
minexponent: -95
basx1031 tosci 1e+2147483649 -> Infinity Overflow Inexact Rounded
basx1032 tosci 1e+2147483648 -> Infinity Overflow Inexact Rounded
basx1033 tosci 1e+2147483647 -> Infinity Overflow Inexact Rounded
basx1034 tosci 1e-2147483647 -> 0E-101 Underflow Subnormal Inexact Rounded Clamped
basx1035 tosci 1e-2147483648 -> 0E-101 Underflow Subnormal Inexact Rounded Clamped
basx1036 tosci 1e-2147483649 -> 0E-101 Underflow Subnormal Inexact Rounded Clamped

-- check for double-rounded subnormals
precision:   5
maxexponent: 79
minexponent: -79
basx1041 toSci     1.52444E-80  ->  1.524E-80 Inexact Rounded Subnormal Underflow
basx1042 toSci     1.52445E-80  ->  1.524E-80 Inexact Rounded Subnormal Underflow
basx1043 toSci     1.52446E-80  ->  1.524E-80 Inexact Rounded Subnormal Underflow

-- clamped zeros [see also clamp.decTest]
precision:   34
maxExponent: 6144
minExponent: -6143

basx1061 apply   0e+10000  ->  0E+6144 Clamped
basx1062 apply   0e-10000  ->  0E-6176 Clamped
basx1063 apply  -0e+10000  -> -0E+6144 Clamped
basx1064 apply  -0e-10000  -> -0E-6176 Clamped

precision:   16
maxExponent: 384
minExponent: -383

basx1065 apply   0e+10000  ->  0E+384  Clamped
basx1066 apply   0e-10000  ->  0E-398  Clamped
basx1067 apply  -0e+10000  -> -0E+384  Clamped
basx1068 apply  -0e-10000  -> -0E-398  Clamped

-- same with IEEE clamping
clamp:       1

precision:   34
maxExponent: 6144
minExponent: -6143

basx1071 apply   0e+10000  ->  0E+6111 Clamped
basx1072 apply   0e-10000  ->  0E-6176 Clamped
basx1073 apply  -0e+10000  -> -0E+6111 Clamped
basx1074 apply  -0e-10000  -> -0E-6176 Clamped

precision:   16
maxExponent: 384
minExponent: -383

basx1075 apply   0e+10000  ->  0E+369  Clamped
basx1076 apply   0e-10000  ->  0E-398  Clamped
basx1077 apply  -0e+10000  -> -0E+369  Clamped
basx1078 apply  -0e-10000  -> -0E-398  Clamped


