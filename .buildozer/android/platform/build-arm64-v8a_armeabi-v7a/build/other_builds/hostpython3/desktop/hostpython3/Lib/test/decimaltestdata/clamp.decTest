------------------------------------------------------------------------
-- clamp.decTest -- clamped exponent tests (format-independent)       --
-- Copyright (c) IBM Corporation, 2000, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- This set of tests uses the same limits as the 8-byte concrete
-- representation, but applies clamping without using format-specific
-- conversions.

extended:    1
precision:   16
rounding:    half_even
maxExponent: 384
minExponent: -383
clamp:       1

-- General testcases

-- Normality
clam010 apply   1234567890123456   ->  1234567890123456
clam011 apply   1234567890123456.0 ->  1234567890123456 Rounded
clam012 apply   1234567890123456.1 ->  1234567890123456 Rounded Inexact
clam013 apply  -1234567890123456   -> -1234567890123456
clam014 apply  -1234567890123456.0 -> -1234567890123456 Rounded
clam015 apply  -1234567890123456.1 -> -1234567890123456 Rounded Inexact


-- Nmax and similar
clam022 apply   9.999999999999999E+384  -> 9.999999999999999E+384
clam024 apply   1.234567890123456E+384  -> 1.234567890123456E+384
-- fold-downs (more below)
clam030 apply   1.23E+384               -> 1.230000000000000E+384 Clamped
clam032 apply   1E+384                  -> 1.000000000000000E+384 Clamped

clam051 apply   12345                   -> 12345
clam053 apply   1234                    -> 1234
clam055 apply   123                     -> 123
clam057 apply   12                      -> 12
clam059 apply   1                       -> 1
clam061 apply   1.23                    -> 1.23
clam063 apply   123.45                  -> 123.45

-- Nmin and below
clam071 apply   1E-383                  -> 1E-383
clam073 apply   1.000000000000000E-383  -> 1.000000000000000E-383
clam075 apply   1.000000000000001E-383  -> 1.000000000000001E-383

clam077 apply   0.100000000000000E-383  -> 1.00000000000000E-384  Subnormal
clam079 apply   0.000000000000010E-383  -> 1.0E-397               Subnormal
clam081 apply   0.00000000000001E-383   -> 1E-397                 Subnormal
clam083 apply   0.000000000000001E-383  -> 1E-398                 Subnormal

-- underflows
clam090 apply   1e-398                  -> #0000000000000001  Subnormal
clam091 apply   1.9e-398                -> #0000000000000002  Subnormal Underflow Inexact Rounded
clam092 apply   1.1e-398                -> #0000000000000001  Subnormal Underflow Inexact Rounded
clam093 apply   1.00000000001e-398      -> #0000000000000001  Subnormal Underflow Inexact Rounded
clam094 apply   1.00000000000001e-398   -> #0000000000000001  Subnormal Underflow Inexact Rounded
clam095 apply   1.000000000000001e-398  -> #0000000000000001  Subnormal Underflow Inexact Rounded
clam096 apply   0.1e-398                -> #0000000000000000  Subnormal Underflow Inexact Rounded Clamped
clam097 apply   0.00000000001e-398      -> #0000000000000000  Subnormal Underflow Inexact Rounded Clamped
clam098 apply   0.00000000000001e-398   -> #0000000000000000  Subnormal Underflow Inexact Rounded Clamped
clam099 apply   0.000000000000001e-398  -> #0000000000000000  Subnormal Underflow Inexact Rounded Clamped

-- Same again, negatives
-- Nmax and similar
clam122 apply  -9.999999999999999E+384  -> -9.999999999999999E+384
clam124 apply  -1.234567890123456E+384  -> -1.234567890123456E+384
-- fold-downs (more below)
clam130 apply  -1.23E+384               -> -1.230000000000000E+384 Clamped
clam132 apply  -1E+384                  -> -1.000000000000000E+384 Clamped

clam151 apply  -12345                   -> -12345
clam153 apply  -1234                    -> -1234
clam155 apply  -123                     -> -123
clam157 apply  -12                      -> -12
clam159 apply  -1                       -> -1
clam161 apply  -1.23                    -> -1.23
clam163 apply  -123.45                  -> -123.45

-- Nmin and below
clam171 apply  -1E-383                  -> -1E-383
clam173 apply  -1.000000000000000E-383  -> -1.000000000000000E-383
clam175 apply  -1.000000000000001E-383  -> -1.000000000000001E-383

clam177 apply  -0.100000000000000E-383  -> -1.00000000000000E-384  Subnormal
clam179 apply  -0.000000000000010E-383  -> -1.0E-397               Subnormal
clam181 apply  -0.00000000000001E-383   -> -1E-397                 Subnormal
clam183 apply  -0.000000000000001E-383  -> -1E-398                 Subnormal

-- underflows
clam189 apply   -1e-398                 -> #8000000000000001  Subnormal
clam190 apply   -1.0e-398               -> #8000000000000001  Subnormal Rounded
clam191 apply   -1.9e-398               -> #8000000000000002  Subnormal Underflow Inexact Rounded
clam192 apply   -1.1e-398               -> #8000000000000001  Subnormal Underflow Inexact Rounded
clam193 apply   -1.00000000001e-398     -> #8000000000000001  Subnormal Underflow Inexact Rounded
clam194 apply   -1.00000000000001e-398  -> #8000000000000001  Subnormal Underflow Inexact Rounded
clam195 apply   -1.000000000000001e-398 -> #8000000000000001  Subnormal Underflow Inexact Rounded
clam196 apply   -0.1e-398               -> #8000000000000000  Subnormal Underflow Inexact Rounded Clamped
clam197 apply   -0.00000000001e-398     -> #8000000000000000  Subnormal Underflow Inexact Rounded Clamped
clam198 apply   -0.00000000000001e-398  -> #8000000000000000  Subnormal Underflow Inexact Rounded Clamped
clam199 apply   -0.000000000000001e-398 -> #8000000000000000  Subnormal Underflow Inexact Rounded Clamped

-- zeros
clam401 apply   0E-500                  -> 0E-398  Clamped
clam402 apply   0E-400                  -> 0E-398  Clamped
clam403 apply   0E-398                  -> 0E-398
clam404 apply   0.000000000000000E-383  -> 0E-398
clam405 apply   0E-2                    ->  0.00
clam406 apply   0                       -> 0
clam407 apply   0E+3                    -> 0E+3
clam408 apply   0E+369                  -> 0E+369
-- clamped zeros...
clam410 apply   0E+370                  -> 0E+369 Clamped
clam411 apply   0E+384                  -> 0E+369 Clamped
clam412 apply   0E+400                  -> 0E+369 Clamped
clam413 apply   0E+500                  -> 0E+369 Clamped

-- negative zeros
clam420 apply   -0E-500                 -> -0E-398 Clamped
clam421 apply   -0E-400                 -> -0E-398 Clamped
clam422 apply   -0E-398                 -> -0E-398
clam423 apply   -0.000000000000000E-383 -> -0E-398
clam424 apply   -0E-2                   -> -0.00
clam425 apply   -0                      -> -0
clam426 apply   -0E+3                   -> -0E+3
clam427 apply   -0E+369                 -> -0E+369
-- clamped zeros...
clam431 apply   -0E+370                 -> -0E+369 Clamped
clam432 apply   -0E+384                 -> -0E+369 Clamped
clam433 apply   -0E+400                 -> -0E+369 Clamped
clam434 apply   -0E+500                 -> -0E+369 Clamped

-- fold-down full sequence
clam601 apply   1E+384                  -> 1.000000000000000E+384 Clamped
clam603 apply   1E+383                  -> 1.00000000000000E+383 Clamped
clam605 apply   1E+382                  -> 1.0000000000000E+382 Clamped
clam607 apply   1E+381                  -> 1.000000000000E+381 Clamped
clam609 apply   1E+380                  -> 1.00000000000E+380 Clamped
clam611 apply   1E+379                  -> 1.0000000000E+379 Clamped
clam613 apply   1E+378                  -> 1.000000000E+378 Clamped
clam615 apply   1E+377                  -> 1.00000000E+377 Clamped
clam617 apply   1E+376                  -> 1.0000000E+376 Clamped
clam619 apply   1E+375                  -> 1.000000E+375 Clamped
clam621 apply   1E+374                  -> 1.00000E+374 Clamped
clam623 apply   1E+373                  -> 1.0000E+373 Clamped
clam625 apply   1E+372                  -> 1.000E+372 Clamped
clam627 apply   1E+371                  -> 1.00E+371 Clamped
clam629 apply   1E+370                  -> 1.0E+370 Clamped
clam631 apply   1E+369                  -> 1E+369
clam633 apply   1E+368                  -> 1E+368
-- same with 9s
clam641 apply   9E+384                  -> 9.000000000000000E+384 Clamped
clam643 apply   9E+383                  -> 9.00000000000000E+383 Clamped
clam645 apply   9E+382                  -> 9.0000000000000E+382 Clamped
clam647 apply   9E+381                  -> 9.000000000000E+381 Clamped
clam649 apply   9E+380                  -> 9.00000000000E+380 Clamped
clam651 apply   9E+379                  -> 9.0000000000E+379 Clamped
clam653 apply   9E+378                  -> 9.000000000E+378 Clamped
clam655 apply   9E+377                  -> 9.00000000E+377 Clamped
clam657 apply   9E+376                  -> 9.0000000E+376 Clamped
clam659 apply   9E+375                  -> 9.000000E+375 Clamped
clam661 apply   9E+374                  -> 9.00000E+374 Clamped
clam663 apply   9E+373                  -> 9.0000E+373 Clamped
clam665 apply   9E+372                  -> 9.000E+372 Clamped
clam667 apply   9E+371                  -> 9.00E+371 Clamped
clam669 apply   9E+370                  -> 9.0E+370 Clamped
clam671 apply   9E+369                  -> 9E+369
clam673 apply   9E+368                  -> 9E+368

-- subnormals clamped to 0-Etiny
precision:   16
maxExponent: 384
minExponent: -383
clam681 apply 7E-398     -> 7E-398 Subnormal
clam682 apply 0E-398     -> 0E-398
clam683 apply 7E-399     -> 1E-398 Subnormal Underflow Inexact Rounded
clam684 apply 4E-399     -> 0E-398 Clamped Subnormal Underflow Inexact Rounded
clam685 apply 7E-400     -> 0E-398 Clamped Subnormal Underflow Inexact Rounded
clam686 apply 7E-401     -> 0E-398 Clamped Subnormal Underflow Inexact Rounded
clam687 apply 0E-399     -> 0E-398 Clamped
clam688 apply 0E-400     -> 0E-398 Clamped
clam689 apply 0E-401     -> 0E-398 Clamped

-- example from documentation
precision:   7
rounding:    half_even
maxExponent: +96
minExponent: -95

clamp:       0
clam700 apply   1.23E+96                -> 1.23E+96

clamp:       1
clam701 apply   1.23E+96                -> 1.230000E+96 Clamped
