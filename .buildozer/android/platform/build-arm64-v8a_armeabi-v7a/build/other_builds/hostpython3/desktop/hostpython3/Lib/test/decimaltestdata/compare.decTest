------------------------------------------------------------------------
-- compare.decTest -- decimal comparison that allows quiet NaNs       --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- Note that we cannot assume add/subtract tests cover paths adequately,
-- here, because the code might be quite different (comparison cannot
-- overflow or underflow, so actual subtractions are not necessary).

extended: 1

precision:   9
rounding:    half_up
maxExponent: 999
minexponent: -999

-- sanity checks
comx001 compare  -2  -2  -> 0
comx002 compare  -2  -1  -> -1
comx003 compare  -2   0  -> -1
comx004 compare  -2   1  -> -1
comx005 compare  -2   2  -> -1
comx006 compare  -1  -2  -> 1
comx007 compare  -1  -1  -> 0
comx008 compare  -1   0  -> -1
comx009 compare  -1   1  -> -1
comx010 compare  -1   2  -> -1
comx011 compare   0  -2  -> 1
comx012 compare   0  -1  -> 1
comx013 compare   0   0  -> 0
comx014 compare   0   1  -> -1
comx015 compare   0   2  -> -1
comx016 compare   1  -2  -> 1
comx017 compare   1  -1  -> 1
comx018 compare   1   0  -> 1
comx019 compare   1   1  -> 0
comx020 compare   1   2  -> -1
comx021 compare   2  -2  -> 1
comx022 compare   2  -1  -> 1
comx023 compare   2   0  -> 1
comx025 compare   2   1  -> 1
comx026 compare   2   2  -> 0

comx031 compare  -20  -20  -> 0
comx032 compare  -20  -10  -> -1
comx033 compare  -20   00  -> -1
comx034 compare  -20   10  -> -1
comx035 compare  -20   20  -> -1
comx036 compare  -10  -20  -> 1
comx037 compare  -10  -10  -> 0
comx038 compare  -10   00  -> -1
comx039 compare  -10   10  -> -1
comx040 compare  -10   20  -> -1
comx041 compare   00  -20  -> 1
comx042 compare   00  -10  -> 1
comx043 compare   00   00  -> 0
comx044 compare   00   10  -> -1
comx045 compare   00   20  -> -1
comx046 compare   10  -20  -> 1
comx047 compare   10  -10  -> 1
comx048 compare   10   00  -> 1
comx049 compare   10   10  -> 0
comx050 compare   10   20  -> -1
comx051 compare   20  -20  -> 1
comx052 compare   20  -10  -> 1
comx053 compare   20   00  -> 1
comx055 compare   20   10  -> 1
comx056 compare   20   20  -> 0

comx061 compare  -2.0  -2.0  -> 0
comx062 compare  -2.0  -1.0  -> -1
comx063 compare  -2.0   0.0  -> -1
comx064 compare  -2.0   1.0  -> -1
comx065 compare  -2.0   2.0  -> -1
comx066 compare  -1.0  -2.0  -> 1
comx067 compare  -1.0  -1.0  -> 0
comx068 compare  -1.0   0.0  -> -1
comx069 compare  -1.0   1.0  -> -1
comx070 compare  -1.0   2.0  -> -1
comx071 compare   0.0  -2.0  -> 1
comx072 compare   0.0  -1.0  -> 1
comx073 compare   0.0   0.0  -> 0
comx074 compare   0.0   1.0  -> -1
comx075 compare   0.0   2.0  -> -1
comx076 compare   1.0  -2.0  -> 1
comx077 compare   1.0  -1.0  -> 1
comx078 compare   1.0   0.0  -> 1
comx079 compare   1.0   1.0  -> 0
comx080 compare   1.0   2.0  -> -1
comx081 compare   2.0  -2.0  -> 1
comx082 compare   2.0  -1.0  -> 1
comx083 compare   2.0   0.0  -> 1
comx085 compare   2.0   1.0  -> 1
comx086 compare   2.0   2.0  -> 0

-- now some cases which might overflow if subtract were used
maxexponent: 999999999
minexponent: -999999999
comx095 compare  9.99999999E+999999999 9.99999999E+999999999  -> 0
comx096 compare -9.99999999E+999999999 9.99999999E+999999999  -> -1
comx097 compare  9.99999999E+999999999 -9.99999999E+999999999 -> 1
comx098 compare -9.99999999E+999999999 -9.99999999E+999999999 -> 0

-- some differing length/exponent cases
comx100 compare   7.0    7.0    -> 0
comx101 compare   7.0    7      -> 0
comx102 compare   7      7.0    -> 0
comx103 compare   7E+0   7.0    -> 0
comx104 compare   70E-1  7.0    -> 0
comx105 compare   0.7E+1 7      -> 0
comx106 compare   70E-1  7      -> 0
comx107 compare   7.0    7E+0   -> 0
comx108 compare   7.0    70E-1  -> 0
comx109 compare   7      0.7E+1 -> 0
comx110 compare   7      70E-1  -> 0

comx120 compare   8.0    7.0    -> 1
comx121 compare   8.0    7      -> 1
comx122 compare   8      7.0    -> 1
comx123 compare   8E+0   7.0    -> 1
comx124 compare   80E-1  7.0    -> 1
comx125 compare   0.8E+1 7      -> 1
comx126 compare   80E-1  7      -> 1
comx127 compare   8.0    7E+0   -> 1
comx128 compare   8.0    70E-1  -> 1
comx129 compare   8      0.7E+1  -> 1
comx130 compare   8      70E-1  -> 1

comx140 compare   8.0    9.0    -> -1
comx141 compare   8.0    9      -> -1
comx142 compare   8      9.0    -> -1
comx143 compare   8E+0   9.0    -> -1
comx144 compare   80E-1  9.0    -> -1
comx145 compare   0.8E+1 9      -> -1
comx146 compare   80E-1  9      -> -1
comx147 compare   8.0    9E+0   -> -1
comx148 compare   8.0    90E-1  -> -1
comx149 compare   8      0.9E+1 -> -1
comx150 compare   8      90E-1  -> -1

-- and again, with sign changes -+ ..
comx200 compare  -7.0    7.0    -> -1
comx201 compare  -7.0    7      -> -1
comx202 compare  -7      7.0    -> -1
comx203 compare  -7E+0   7.0    -> -1
comx204 compare  -70E-1  7.0    -> -1
comx205 compare  -0.7E+1 7      -> -1
comx206 compare  -70E-1  7      -> -1
comx207 compare  -7.0    7E+0   -> -1
comx208 compare  -7.0    70E-1  -> -1
comx209 compare  -7      0.7E+1 -> -1
comx210 compare  -7      70E-1  -> -1

comx220 compare  -8.0    7.0    -> -1
comx221 compare  -8.0    7      -> -1
comx222 compare  -8      7.0    -> -1
comx223 compare  -8E+0   7.0    -> -1
comx224 compare  -80E-1  7.0    -> -1
comx225 compare  -0.8E+1 7      -> -1
comx226 compare  -80E-1  7      -> -1
comx227 compare  -8.0    7E+0   -> -1
comx228 compare  -8.0    70E-1  -> -1
comx229 compare  -8      0.7E+1 -> -1
comx230 compare  -8      70E-1  -> -1

comx240 compare  -8.0    9.0    -> -1
comx241 compare  -8.0    9      -> -1
comx242 compare  -8      9.0    -> -1
comx243 compare  -8E+0   9.0    -> -1
comx244 compare  -80E-1  9.0    -> -1
comx245 compare  -0.8E+1 9      -> -1
comx246 compare  -80E-1  9      -> -1
comx247 compare  -8.0    9E+0   -> -1
comx248 compare  -8.0    90E-1  -> -1
comx249 compare  -8      0.9E+1 -> -1
comx250 compare  -8      90E-1  -> -1

-- and again, with sign changes +- ..
comx300 compare   7.0    -7.0    -> 1
comx301 compare   7.0    -7      -> 1
comx302 compare   7      -7.0    -> 1
comx303 compare   7E+0   -7.0    -> 1
comx304 compare   70E-1  -7.0    -> 1
comx305 compare   .7E+1  -7      -> 1
comx306 compare   70E-1  -7      -> 1
comx307 compare   7.0    -7E+0   -> 1
comx308 compare   7.0    -70E-1  -> 1
comx309 compare   7      -.7E+1  -> 1
comx310 compare   7      -70E-1  -> 1

comx320 compare   8.0    -7.0    -> 1
comx321 compare   8.0    -7      -> 1
comx322 compare   8      -7.0    -> 1
comx323 compare   8E+0   -7.0    -> 1
comx324 compare   80E-1  -7.0    -> 1
comx325 compare   .8E+1  -7      -> 1
comx326 compare   80E-1  -7      -> 1
comx327 compare   8.0    -7E+0   -> 1
comx328 compare   8.0    -70E-1  -> 1
comx329 compare   8      -.7E+1  -> 1
comx330 compare   8      -70E-1  -> 1

comx340 compare   8.0    -9.0    -> 1
comx341 compare   8.0    -9      -> 1
comx342 compare   8      -9.0    -> 1
comx343 compare   8E+0   -9.0    -> 1
comx344 compare   80E-1  -9.0    -> 1
comx345 compare   .8E+1  -9      -> 1
comx346 compare   80E-1  -9      -> 1
comx347 compare   8.0    -9E+0   -> 1
comx348 compare   8.0    -90E-1  -> 1
comx349 compare   8      -.9E+1  -> 1
comx350 compare   8      -90E-1  -> 1

-- and again, with sign changes -- ..
comx400 compare   -7.0    -7.0    -> 0
comx401 compare   -7.0    -7      -> 0
comx402 compare   -7      -7.0    -> 0
comx403 compare   -7E+0   -7.0    -> 0
comx404 compare   -70E-1  -7.0    -> 0
comx405 compare   -.7E+1  -7      -> 0
comx406 compare   -70E-1  -7      -> 0
comx407 compare   -7.0    -7E+0   -> 0
comx408 compare   -7.0    -70E-1  -> 0
comx409 compare   -7      -.7E+1  -> 0
comx410 compare   -7      -70E-1  -> 0

comx420 compare   -8.0    -7.0    -> -1
comx421 compare   -8.0    -7      -> -1
comx422 compare   -8      -7.0    -> -1
comx423 compare   -8E+0   -7.0    -> -1
comx424 compare   -80E-1  -7.0    -> -1
comx425 compare   -.8E+1  -7      -> -1
comx426 compare   -80E-1  -7      -> -1
comx427 compare   -8.0    -7E+0   -> -1
comx428 compare   -8.0    -70E-1  -> -1
comx429 compare   -8      -.7E+1  -> -1
comx430 compare   -8      -70E-1  -> -1

comx440 compare   -8.0    -9.0    -> 1
comx441 compare   -8.0    -9      -> 1
comx442 compare   -8      -9.0    -> 1
comx443 compare   -8E+0   -9.0    -> 1
comx444 compare   -80E-1  -9.0    -> 1
comx445 compare   -.8E+1  -9      -> 1
comx446 compare   -80E-1  -9      -> 1
comx447 compare   -8.0    -9E+0   -> 1
comx448 compare   -8.0    -90E-1  -> 1
comx449 compare   -8      -.9E+1  -> 1
comx450 compare   -8      -90E-1  -> 1

-- misalignment traps for little-endian
comx451 compare      1.0       0.1  -> 1
comx452 compare      0.1       1.0  -> -1
comx453 compare     10.0       0.1  -> 1
comx454 compare      0.1      10.0  -> -1
comx455 compare      100       1.0  -> 1
comx456 compare      1.0       100  -> -1
comx457 compare     1000      10.0  -> 1
comx458 compare     10.0      1000  -> -1
comx459 compare    10000     100.0  -> 1
comx460 compare    100.0     10000  -> -1
comx461 compare   100000    1000.0  -> 1
comx462 compare   1000.0    100000  -> -1
comx463 compare  1000000   10000.0  -> 1
comx464 compare  10000.0   1000000  -> -1

-- testcases that subtract to lots of zeros at boundaries [pgr]
precision: 40
comx470 compare 123.4560000000000000E789 123.456E789 -> 0
comx471 compare 123.456000000000000E-89 123.456E-89 -> 0
comx472 compare 123.45600000000000E789 123.456E789 -> 0
comx473 compare 123.4560000000000E-89 123.456E-89 -> 0
comx474 compare 123.456000000000E789 123.456E789 -> 0
comx475 compare 123.45600000000E-89 123.456E-89 -> 0
comx476 compare 123.4560000000E789 123.456E789 -> 0
comx477 compare 123.456000000E-89 123.456E-89 -> 0
comx478 compare 123.45600000E789 123.456E789 -> 0
comx479 compare 123.4560000E-89 123.456E-89 -> 0
comx480 compare 123.456000E789 123.456E789 -> 0
comx481 compare 123.45600E-89 123.456E-89 -> 0
comx482 compare 123.4560E789 123.456E789 -> 0
comx483 compare 123.456E-89 123.456E-89 -> 0
comx484 compare 123.456E-89 123.4560000000000000E-89 -> 0
comx485 compare 123.456E789 123.456000000000000E789 -> 0
comx486 compare 123.456E-89 123.45600000000000E-89 -> 0
comx487 compare 123.456E789 123.4560000000000E789 -> 0
comx488 compare 123.456E-89 123.456000000000E-89 -> 0
comx489 compare 123.456E789 123.45600000000E789 -> 0
comx490 compare 123.456E-89 123.4560000000E-89 -> 0
comx491 compare 123.456E789 123.456000000E789 -> 0
comx492 compare 123.456E-89 123.45600000E-89 -> 0
comx493 compare 123.456E789 123.4560000E789 -> 0
comx494 compare 123.456E-89 123.456000E-89 -> 0
comx495 compare 123.456E789 123.45600E789 -> 0
comx496 compare 123.456E-89 123.4560E-89 -> 0
comx497 compare 123.456E789 123.456E789 -> 0

-- wide-ranging, around precision; signs equal
precision: 9
comx500 compare    1     1E-15    -> 1
comx501 compare    1     1E-14    -> 1
comx502 compare    1     1E-13    -> 1
comx503 compare    1     1E-12    -> 1
comx504 compare    1     1E-11    -> 1
comx505 compare    1     1E-10    -> 1
comx506 compare    1     1E-9     -> 1
comx507 compare    1     1E-8     -> 1
comx508 compare    1     1E-7     -> 1
comx509 compare    1     1E-6     -> 1
comx510 compare    1     1E-5     -> 1
comx511 compare    1     1E-4     -> 1
comx512 compare    1     1E-3     -> 1
comx513 compare    1     1E-2     -> 1
comx514 compare    1     1E-1     -> 1
comx515 compare    1     1E-0     -> 0
comx516 compare    1     1E+1     -> -1
comx517 compare    1     1E+2     -> -1
comx518 compare    1     1E+3     -> -1
comx519 compare    1     1E+4     -> -1
comx521 compare    1     1E+5     -> -1
comx522 compare    1     1E+6     -> -1
comx523 compare    1     1E+7     -> -1
comx524 compare    1     1E+8     -> -1
comx525 compare    1     1E+9     -> -1
comx526 compare    1     1E+10    -> -1
comx527 compare    1     1E+11    -> -1
comx528 compare    1     1E+12    -> -1
comx529 compare    1     1E+13    -> -1
comx530 compare    1     1E+14    -> -1
comx531 compare    1     1E+15    -> -1
-- LR swap
comx540 compare    1E-15  1       -> -1
comx541 compare    1E-14  1       -> -1
comx542 compare    1E-13  1       -> -1
comx543 compare    1E-12  1       -> -1
comx544 compare    1E-11  1       -> -1
comx545 compare    1E-10  1       -> -1
comx546 compare    1E-9   1       -> -1
comx547 compare    1E-8   1       -> -1
comx548 compare    1E-7   1       -> -1
comx549 compare    1E-6   1       -> -1
comx550 compare    1E-5   1       -> -1
comx551 compare    1E-4   1       -> -1
comx552 compare    1E-3   1       -> -1
comx553 compare    1E-2   1       -> -1
comx554 compare    1E-1   1       -> -1
comx555 compare    1E-0   1       ->  0
comx556 compare    1E+1   1       ->  1
comx557 compare    1E+2   1       ->  1
comx558 compare    1E+3   1       ->  1
comx559 compare    1E+4   1       ->  1
comx561 compare    1E+5   1       ->  1
comx562 compare    1E+6   1       ->  1
comx563 compare    1E+7   1       ->  1
comx564 compare    1E+8   1       ->  1
comx565 compare    1E+9   1       ->  1
comx566 compare    1E+10  1       ->  1
comx567 compare    1E+11  1       ->  1
comx568 compare    1E+12  1       ->  1
comx569 compare    1E+13  1       ->  1
comx570 compare    1E+14  1       ->  1
comx571 compare    1E+15  1       ->  1
-- similar with a useful coefficient, one side only
comx580 compare  0.000000987654321     1E-15    -> 1
comx581 compare  0.000000987654321     1E-14    -> 1
comx582 compare  0.000000987654321     1E-13    -> 1
comx583 compare  0.000000987654321     1E-12    -> 1
comx584 compare  0.000000987654321     1E-11    -> 1
comx585 compare  0.000000987654321     1E-10    -> 1
comx586 compare  0.000000987654321     1E-9     -> 1
comx587 compare  0.000000987654321     1E-8     -> 1
comx588 compare  0.000000987654321     1E-7     -> 1
comx589 compare  0.000000987654321     1E-6     -> -1
comx590 compare  0.000000987654321     1E-5     -> -1
comx591 compare  0.000000987654321     1E-4     -> -1
comx592 compare  0.000000987654321     1E-3     -> -1
comx593 compare  0.000000987654321     1E-2     -> -1
comx594 compare  0.000000987654321     1E-1     -> -1
comx595 compare  0.000000987654321     1E-0     -> -1
comx596 compare  0.000000987654321     1E+1     -> -1
comx597 compare  0.000000987654321     1E+2     -> -1
comx598 compare  0.000000987654321     1E+3     -> -1
comx599 compare  0.000000987654321     1E+4     -> -1

-- check some unit-y traps
precision: 20
comx600 compare   12            12.2345 -> -1
comx601 compare   12.0          12.2345 -> -1
comx602 compare   12.00         12.2345 -> -1
comx603 compare   12.000        12.2345 -> -1
comx604 compare   12.0000       12.2345 -> -1
comx605 compare   12.00000      12.2345 -> -1
comx606 compare   12.000000     12.2345 -> -1
comx607 compare   12.0000000    12.2345 -> -1
comx608 compare   12.00000000   12.2345 -> -1
comx609 compare   12.000000000  12.2345 -> -1
comx610 compare   12.1234 12            ->  1
comx611 compare   12.1234 12.0          ->  1
comx612 compare   12.1234 12.00         ->  1
comx613 compare   12.1234 12.000        ->  1
comx614 compare   12.1234 12.0000       ->  1
comx615 compare   12.1234 12.00000      ->  1
comx616 compare   12.1234 12.000000     ->  1
comx617 compare   12.1234 12.0000000    ->  1
comx618 compare   12.1234 12.00000000   ->  1
comx619 compare   12.1234 12.000000000  ->  1
comx620 compare  -12           -12.2345 ->  1
comx621 compare  -12.0         -12.2345 ->  1
comx622 compare  -12.00        -12.2345 ->  1
comx623 compare  -12.000       -12.2345 ->  1
comx624 compare  -12.0000      -12.2345 ->  1
comx625 compare  -12.00000     -12.2345 ->  1
comx626 compare  -12.000000    -12.2345 ->  1
comx627 compare  -12.0000000   -12.2345 ->  1
comx628 compare  -12.00000000  -12.2345 ->  1
comx629 compare  -12.000000000 -12.2345 ->  1
comx630 compare  -12.1234 -12           -> -1
comx631 compare  -12.1234 -12.0         -> -1
comx632 compare  -12.1234 -12.00        -> -1
comx633 compare  -12.1234 -12.000       -> -1
comx634 compare  -12.1234 -12.0000      -> -1
comx635 compare  -12.1234 -12.00000     -> -1
comx636 compare  -12.1234 -12.000000    -> -1
comx637 compare  -12.1234 -12.0000000   -> -1
comx638 compare  -12.1234 -12.00000000  -> -1
comx639 compare  -12.1234 -12.000000000 -> -1
precision: 9

-- extended zeros
comx640 compare   0     0   -> 0
comx641 compare   0    -0   -> 0
comx642 compare   0    -0.0 -> 0
comx643 compare   0     0.0 -> 0
comx644 compare  -0     0   -> 0
comx645 compare  -0    -0   -> 0
comx646 compare  -0    -0.0 -> 0
comx647 compare  -0     0.0 -> 0
comx648 compare   0.0   0   -> 0
comx649 compare   0.0  -0   -> 0
comx650 compare   0.0  -0.0 -> 0
comx651 compare   0.0   0.0 -> 0
comx652 compare  -0.0   0   -> 0
comx653 compare  -0.0  -0   -> 0
comx654 compare  -0.0  -0.0 -> 0
comx655 compare  -0.0   0.0 -> 0

comx656 compare  -0E1   0.0 -> 0
comx657 compare  -0E2   0.0 -> 0
comx658 compare   0E1   0.0 -> 0
comx659 compare   0E2   0.0 -> 0
comx660 compare  -0E1   0   -> 0
comx661 compare  -0E2   0   -> 0
comx662 compare   0E1   0   -> 0
comx663 compare   0E2   0   -> 0
comx664 compare  -0E1  -0E1 -> 0
comx665 compare  -0E2  -0E1 -> 0
comx666 compare   0E1  -0E1 -> 0
comx667 compare   0E2  -0E1 -> 0
comx668 compare  -0E1  -0E2 -> 0
comx669 compare  -0E2  -0E2 -> 0
comx670 compare   0E1  -0E2 -> 0
comx671 compare   0E2  -0E2 -> 0
comx672 compare  -0E1   0E1 -> 0
comx673 compare  -0E2   0E1 -> 0
comx674 compare   0E1   0E1 -> 0
comx675 compare   0E2   0E1 -> 0
comx676 compare  -0E1   0E2 -> 0
comx677 compare  -0E2   0E2 -> 0
comx678 compare   0E1   0E2 -> 0
comx679 compare   0E2   0E2 -> 0

-- trailing zeros; unit-y
precision: 20
comx680 compare   12    12           -> 0
comx681 compare   12    12.0         -> 0
comx682 compare   12    12.00        -> 0
comx683 compare   12    12.000       -> 0
comx684 compare   12    12.0000      -> 0
comx685 compare   12    12.00000     -> 0
comx686 compare   12    12.000000    -> 0
comx687 compare   12    12.0000000   -> 0
comx688 compare   12    12.00000000  -> 0
comx689 compare   12    12.000000000 -> 0
comx690 compare   12              12 -> 0
comx691 compare   12.0            12 -> 0
comx692 compare   12.00           12 -> 0
comx693 compare   12.000          12 -> 0
comx694 compare   12.0000         12 -> 0
comx695 compare   12.00000        12 -> 0
comx696 compare   12.000000       12 -> 0
comx697 compare   12.0000000      12 -> 0
comx698 compare   12.00000000     12 -> 0
comx699 compare   12.000000000    12 -> 0

-- long operand checks
maxexponent: 999
minexponent: -999
precision: 9
comx701 compare 12345678000  1 ->  1
comx702 compare 1 12345678000  -> -1
comx703 compare 1234567800   1 ->  1
comx704 compare 1 1234567800   -> -1
comx705 compare 1234567890   1 ->  1
comx706 compare 1 1234567890   -> -1
comx707 compare 1234567891   1 ->  1
comx708 compare 1 1234567891   -> -1
comx709 compare 12345678901  1 ->  1
comx710 compare 1 12345678901  -> -1
comx711 compare 1234567896   1 ->  1
comx712 compare 1 1234567896   -> -1
comx713 compare -1234567891  1 -> -1
comx714 compare 1 -1234567891  ->  1
comx715 compare -12345678901 1 -> -1
comx716 compare 1 -12345678901 ->  1
comx717 compare -1234567896  1 -> -1
comx718 compare 1 -1234567896  ->  1

precision: 15
-- same with plenty of precision
comx721 compare 12345678000 1 -> 1
comx722 compare 1 12345678000 -> -1
comx723 compare 1234567800  1 -> 1
comx724 compare 1 1234567800  -> -1
comx725 compare 1234567890  1 -> 1
comx726 compare 1 1234567890  -> -1
comx727 compare 1234567891  1 -> 1
comx728 compare 1 1234567891  -> -1
comx729 compare 12345678901 1 -> 1
comx730 compare 1 12345678901 -> -1
comx731 compare 1234567896  1 -> 1
comx732 compare 1 1234567896  -> -1

-- residue cases
precision: 5
comx740 compare  1  0.9999999  -> 1
comx741 compare  1  0.999999   -> 1
comx742 compare  1  0.99999    -> 1
comx743 compare  1  1.0000     -> 0
comx744 compare  1  1.00001    -> -1
comx745 compare  1  1.000001   -> -1
comx746 compare  1  1.0000001  -> -1
comx750 compare  0.9999999  1  -> -1
comx751 compare  0.999999   1  -> -1
comx752 compare  0.99999    1  -> -1
comx753 compare  1.0000     1  -> 0
comx754 compare  1.00001    1  -> 1
comx755 compare  1.000001   1  -> 1
comx756 compare  1.0000001  1  -> 1

-- a selection of longies
comx760 compare -36852134.84194296250843579428931 -5830629.8347085025808756560357940 -> -1
comx761 compare -36852134.84194296250843579428931 -36852134.84194296250843579428931  -> 0
comx762 compare -36852134.94194296250843579428931 -36852134.84194296250843579428931  -> -1
comx763 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
-- precisions above or below the difference should have no effect
precision:   11
comx764 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:   10
comx765 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    9
comx766 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    8
comx767 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    7
comx768 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    6
comx769 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    5
comx770 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    4
comx771 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    3
comx772 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    2
comx773 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1
precision:    1
comx774 compare -36852134.84194296250843579428931 -36852134.94194296250843579428931  -> 1

-- Specials
precision:   9
comx780 compare  Inf  -Inf   ->  1
comx781 compare  Inf  -1000  ->  1
comx782 compare  Inf  -1     ->  1
comx783 compare  Inf  -0     ->  1
comx784 compare  Inf   0     ->  1
comx785 compare  Inf   1     ->  1
comx786 compare  Inf   1000  ->  1
comx787 compare  Inf   Inf   ->  0
comx788 compare -1000  Inf   -> -1
comx789 compare -Inf   Inf   -> -1
comx790 compare -1     Inf   -> -1
comx791 compare -0     Inf   -> -1
comx792 compare  0     Inf   -> -1
comx793 compare  1     Inf   -> -1
comx794 compare  1000  Inf   -> -1
comx795 compare  Inf   Inf   ->  0

comx800 compare -Inf  -Inf   ->  0
comx801 compare -Inf  -1000  -> -1
comx802 compare -Inf  -1     -> -1
comx803 compare -Inf  -0     -> -1
comx804 compare -Inf   0     -> -1
comx805 compare -Inf   1     -> -1
comx806 compare -Inf   1000  -> -1
comx807 compare -Inf   Inf   -> -1
comx808 compare -Inf  -Inf   ->  0
comx809 compare -1000 -Inf   ->  1
comx810 compare -1    -Inf   ->  1
comx811 compare -0    -Inf   ->  1
comx812 compare  0    -Inf   ->  1
comx813 compare  1    -Inf   ->  1
comx814 compare  1000 -Inf   ->  1
comx815 compare  Inf  -Inf   ->  1

comx821 compare  NaN -Inf    ->  NaN
comx822 compare  NaN -1000   ->  NaN
comx823 compare  NaN -1      ->  NaN
comx824 compare  NaN -0      ->  NaN
comx825 compare  NaN  0      ->  NaN
comx826 compare  NaN  1      ->  NaN
comx827 compare  NaN  1000   ->  NaN
comx828 compare  NaN  Inf    ->  NaN
comx829 compare  NaN  NaN    ->  NaN
comx830 compare -Inf  NaN    ->  NaN
comx831 compare -1000 NaN    ->  NaN
comx832 compare -1    NaN    ->  NaN
comx833 compare -0    NaN    ->  NaN
comx834 compare  0    NaN    ->  NaN
comx835 compare  1    NaN    ->  NaN
comx836 compare  1000 NaN    ->  NaN
comx837 compare  Inf  NaN    ->  NaN
comx838 compare -NaN -NaN    -> -NaN
comx839 compare +NaN -NaN    ->  NaN
comx840 compare -NaN +NaN    -> -NaN

comx841 compare  sNaN -Inf   ->  NaN  Invalid_operation
comx842 compare  sNaN -1000  ->  NaN  Invalid_operation
comx843 compare  sNaN -1     ->  NaN  Invalid_operation
comx844 compare  sNaN -0     ->  NaN  Invalid_operation
comx845 compare  sNaN  0     ->  NaN  Invalid_operation
comx846 compare  sNaN  1     ->  NaN  Invalid_operation
comx847 compare  sNaN  1000  ->  NaN  Invalid_operation
comx848 compare  sNaN  NaN   ->  NaN  Invalid_operation
comx849 compare  sNaN sNaN   ->  NaN  Invalid_operation
comx850 compare  NaN  sNaN   ->  NaN  Invalid_operation
comx851 compare -Inf  sNaN   ->  NaN  Invalid_operation
comx852 compare -1000 sNaN   ->  NaN  Invalid_operation
comx853 compare -1    sNaN   ->  NaN  Invalid_operation
comx854 compare -0    sNaN   ->  NaN  Invalid_operation
comx855 compare  0    sNaN   ->  NaN  Invalid_operation
comx856 compare  1    sNaN   ->  NaN  Invalid_operation
comx857 compare  1000 sNaN   ->  NaN  Invalid_operation
comx858 compare  Inf  sNaN   ->  NaN  Invalid_operation
comx859 compare  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
comx860 compare  NaN9 -Inf   ->  NaN9
comx861 compare  NaN8  999   ->  NaN8
comx862 compare  NaN77 Inf   ->  NaN77
comx863 compare -NaN67 NaN5  -> -NaN67
comx864 compare -Inf  -NaN4  -> -NaN4
comx865 compare -999  -NaN33 -> -NaN33
comx866 compare  Inf   NaN2  ->  NaN2
comx867 compare -NaN41 -NaN42 -> -NaN41
comx868 compare +NaN41 -NaN42 ->  NaN41
comx869 compare -NaN41 +NaN42 -> -NaN41
comx870 compare +NaN41 +NaN42 ->  NaN41

comx871 compare -sNaN99 -Inf    -> -NaN99 Invalid_operation
comx872 compare  sNaN98 -11     ->  NaN98 Invalid_operation
comx873 compare  sNaN97  NaN    ->  NaN97 Invalid_operation
comx874 compare  sNaN16 sNaN94  ->  NaN16 Invalid_operation
comx875 compare  NaN85  sNaN83  ->  NaN83 Invalid_operation
comx876 compare -Inf    sNaN92  ->  NaN92 Invalid_operation
comx877 compare  088    sNaN81  ->  NaN81 Invalid_operation
comx878 compare  Inf    sNaN90  ->  NaN90 Invalid_operation
comx879 compare  NaN   -sNaN89  -> -NaN89 Invalid_operation

-- overflow and underflow tests .. subnormal results now allowed
maxExponent: 999999999
minexponent: -999999999
comx880 compare +1.23456789012345E-0 9E+999999999 -> -1
comx881 compare 9E+999999999 +1.23456789012345E-0 ->  1
comx882 compare +0.100 9E-999999999               ->  1
comx883 compare 9E-999999999 +0.100               -> -1
comx885 compare -1.23456789012345E-0 9E+999999999 -> -1
comx886 compare 9E+999999999 -1.23456789012345E-0 ->  1
comx887 compare -0.100 9E-999999999               -> -1
comx888 compare 9E-999999999 -0.100               ->  1

comx889 compare 1e-599999999 1e-400000001   -> -1
comx890 compare 1e-599999999 1e-400000000   -> -1
comx891 compare 1e-600000000 1e-400000000   -> -1
comx892 compare 9e-999999998 0.01           -> -1
comx893 compare 9e-999999998 0.1            -> -1
comx894 compare 0.01 9e-999999998           ->  1
comx895 compare 1e599999999 1e400000001     ->  1
comx896 compare 1e599999999 1e400000000     ->  1
comx897 compare 1e600000000 1e400000000     ->  1
comx898 compare 9e999999998 100             ->  1
comx899 compare 9e999999998 10              ->  1
comx900 compare 100  9e999999998            -> -1
-- signs
comx901 compare  1e+777777777  1e+411111111 ->  1
comx902 compare  1e+777777777 -1e+411111111 ->  1
comx903 compare -1e+777777777  1e+411111111 -> -1
comx904 compare -1e+777777777 -1e+411111111 -> -1
comx905 compare  1e-777777777  1e-411111111 -> -1
comx906 compare  1e-777777777 -1e-411111111 ->  1
comx907 compare -1e-777777777  1e-411111111 -> -1
comx908 compare -1e-777777777 -1e-411111111 ->  1

-- spread zeros
comx910 compare   0E-383  0       ->  0
comx911 compare   0E-383 -0       ->  0
comx912 compare  -0E-383  0       ->  0
comx913 compare  -0E-383 -0       ->  0
comx914 compare   0E-383  0E+384  ->  0
comx915 compare   0E-383 -0E+384  ->  0
comx916 compare  -0E-383  0E+384  ->  0
comx917 compare  -0E-383 -0E+384  ->  0
comx918 compare   0       0E+384  ->  0
comx919 compare   0      -0E+384  ->  0
comx920 compare  -0       0E+384  ->  0
comx921 compare  -0      -0E+384  ->  0
comx930 compare   0E+384  0       ->  0
comx931 compare   0E+384 -0       ->  0
comx932 compare  -0E+384  0       ->  0
comx933 compare  -0E+384 -0       ->  0
comx934 compare   0E+384  0E-383  ->  0
comx935 compare   0E+384 -0E-383  ->  0
comx936 compare  -0E+384  0E-383  ->  0
comx937 compare  -0E+384 -0E-383  ->  0
comx938 compare   0       0E-383  ->  0
comx939 compare   0      -0E-383  ->  0
comx940 compare  -0       0E-383  ->  0
comx941 compare  -0      -0E-383  ->  0

-- Null tests
comx990 compare 10  # -> NaN Invalid_operation
comx991 compare  # 10 -> NaN Invalid_operation
