Fix build failure with +quartz variant on OS X 10.8 and 10.9.
Even though Gestalt was deprecated in OS X 10.8, it should work fine
through OS X 10.9, and its replacement NSOperatingSystemVersion was
not introduced until OS X 10.10.

Patch from MacPorts project and reported upstream:
https://trac.macports.org/ticket/55649
--- tk8.6.8/macosx/tkMacOSXXStubs.c.orig	2017-12-06 09:25:08.000000000 -0600
+++ tk8.6.8-patched/macosx/tkMacOSXXStubs.c	2018-01-06 19:34:17.000000000 -0600
@@ -175,7 +175,7 @@
     {
 	int major, minor, patch;
 
-#if MAC_OS_X_VERSION_MIN_REQUIRED < 1080
+#if MAC_OS_X_VERSION_MIN_REQUIRED < 101000
 	Gestalt(gestaltSystemVersionMajor, (SInt32*)&major);
 	Gestalt(gestaltSystemVersionMinor, (SInt32*)&minor);
 	Gestalt(gestaltSystemVersionBugFix, (SInt32*)&patch);
