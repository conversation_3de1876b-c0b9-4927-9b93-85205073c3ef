This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by python configure 3.11, which was
generated by GNU Autoconf 2.69.  Invocation command line was

  $ /home/<USER>/Project/Downloader/.buildozer/android/platform/build-arm64-v8a_armeabi-v7a/build/other_builds/hostpython3/desktop/hostpython3/configure 

## --------- ##
## Platform. ##
## --------- ##

hostname = anik
uname -m = x86_64
uname -r = 6.11.0-25-generic
uname -s = Linux
uname -v = #25~24.04.1-Ubuntu SMP PREEMPT_DYNAMIC Tue Apr 15 17:20:50 UTC 2

/usr/bin/uname -p = x86_64
/bin/uname -X     = unknown

/bin/arch              = x86_64
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /home/<USER>/.buildozer/android/platform/apache-ant-1.9.4/bin
PATH: /home/<USER>/Project/Downloader/venv/bin
PATH: /usr/local/sbin
PATH: /usr/local/bin
PATH: /usr/sbin
PATH: /usr/bin
PATH: /sbin
PATH: /bin
PATH: /usr/games
PATH: /usr/local/games
PATH: /snap/bin
PATH: /snap/bin
PATH: /home/<USER>/.local/bin
PATH: /home/<USER>/.local/bin
PATH: /home/<USER>/.local/bin
PATH: /home/<USER>/.local/bin
PATH: /home/<USER>/.local/bin
PATH: /home/<USER>/.local/bin
PATH: /home/<USER>/.local/bin
PATH: /home/<USER>/.local/bin


## ----------- ##
## Core tests. ##
## ----------- ##

configure:3164: checking build system type
configure:3178: result: x86_64-pc-linux-gnu
configure:3198: checking host system type
configure:3211: result: x86_64-pc-linux-gnu
configure:3283: checking for Python interpreter freezing
configure:3285: result: ./_bootstrap_python
configure:3317: checking for python3.11
configure:3347: result: no
configure:3317: checking for python3.10
configure:3333: found /usr/bin/python3.10
configure:3344: result: python3.10
configure:3358: checking Python for regen version
configure:3361: result: Python 3.10.17
configure:3488: checking for pkg-config
configure:3506: found /usr/bin/pkg-config
configure:3518: result: /usr/bin/pkg-config
configure:3543: checking pkg-config is at least version 0.9.0
configure:3546: result: yes
configure:3569: checking for --enable-universalsdk
configure:3616: result: no
configure:3640: checking for --with-universal-archs
configure:3655: result: no
configure:3807: checking MACHDEP
configure:3864: result: "linux"
configure:4161: checking for gcc
configure:4177: found /usr/bin/gcc
configure:4188: result: gcc
configure:4417: checking for C compiler version
configure:4426: gcc --version >&5
gcc (Ubuntu 13.3.0-6ubuntu2~24.04) 13.3.0
Copyright (C) 2023 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:4437: $? = 0
configure:4426: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 13.3.0-6ubuntu2~24.04' --with-bugurl=file:///usr/share/doc/gcc-13/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-13 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/libexec --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-libstdcxx-backtrace --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-13-fG75Ri/gcc-13-13.3.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 13.3.0 (Ubuntu 13.3.0-6ubuntu2~24.04) 
... rest of stderr output deleted ...
configure:4437: $? = 0
configure:4426: gcc -V >&5
gcc: error: unrecognized command-line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:4437: $? = 1
configure:4426: gcc -qversion >&5
gcc: error: unrecognized command-line option '-qversion'; did you mean '--version'?
gcc: fatal error: no input files
compilation terminated.
configure:4437: $? = 1
configure:4457: checking whether the C compiler works
configure:4479: gcc    conftest.c  >&5
configure:4483: $? = 0
configure:4531: result: yes
configure:4534: checking for C compiler default output file name
configure:4536: result: a.out
configure:4542: checking for suffix of executables
configure:4549: gcc -o conftest    conftest.c  >&5
configure:4553: $? = 0
configure:4575: result: 
configure:4597: checking whether we are cross compiling
configure:4605: gcc -o conftest    conftest.c  >&5
In file included from conftest.c:10:
/usr/include/stdio.h:28:10: fatal error: bits/libc-header-start.h: No such file or directory
   28 | #include <bits/libc-header-start.h>
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~
compilation terminated.
configure:4609: $? = 1
configure:4616: ./conftest
/home/<USER>/Project/Downloader/.buildozer/android/platform/build-arm64-v8a_armeabi-v7a/build/other_builds/hostpython3/desktop/hostpython3/configure: line 4618: ./conftest: No such file or directory
configure:4620: $? = 127
configure:4627: error: in `/home/<USER>/Project/Downloader/.buildozer/android/platform/build-arm64-v8a_armeabi-v7a/build/other_builds/hostpython3/desktop/hostpython3/native-build':
configure:4629: error: cannot run C compiled programs.
If you meant to cross compile, use `--host'.
See `config.log' for more details

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=x86_64-pc-linux-gnu
ac_cv_env_BZIP2_CFLAGS_set=
ac_cv_env_BZIP2_CFLAGS_value=
ac_cv_env_BZIP2_LIBS_set=
ac_cv_env_BZIP2_LIBS_value=
ac_cv_env_CC_set=
ac_cv_env_CC_value=
ac_cv_env_CFLAGS_set=
ac_cv_env_CFLAGS_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CPP_set=
ac_cv_env_CPP_value=
ac_cv_env_GDBM_CFLAGS_set=
ac_cv_env_GDBM_CFLAGS_value=
ac_cv_env_GDBM_LIBS_set=
ac_cv_env_GDBM_LIBS_value=
ac_cv_env_HOSTRUNNER_set=
ac_cv_env_HOSTRUNNER_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBB2_CFLAGS_set=
ac_cv_env_LIBB2_CFLAGS_value=
ac_cv_env_LIBB2_LIBS_set=
ac_cv_env_LIBB2_LIBS_value=
ac_cv_env_LIBCRYPT_CFLAGS_set=
ac_cv_env_LIBCRYPT_CFLAGS_value=
ac_cv_env_LIBCRYPT_LIBS_set=
ac_cv_env_LIBCRYPT_LIBS_value=
ac_cv_env_LIBLZMA_CFLAGS_set=
ac_cv_env_LIBLZMA_CFLAGS_value=
ac_cv_env_LIBLZMA_LIBS_set=
ac_cv_env_LIBLZMA_LIBS_value=
ac_cv_env_LIBNSL_CFLAGS_set=
ac_cv_env_LIBNSL_CFLAGS_value=
ac_cv_env_LIBNSL_LIBS_set=
ac_cv_env_LIBNSL_LIBS_value=
ac_cv_env_LIBSQLITE3_CFLAGS_set=
ac_cv_env_LIBSQLITE3_CFLAGS_value=
ac_cv_env_LIBSQLITE3_LIBS_set=
ac_cv_env_LIBSQLITE3_LIBS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_LIBUUID_CFLAGS_set=
ac_cv_env_LIBUUID_CFLAGS_value=
ac_cv_env_LIBUUID_LIBS_set=
ac_cv_env_LIBUUID_LIBS_value=
ac_cv_env_MACHDEP_set=
ac_cv_env_MACHDEP_value=
ac_cv_env_PKG_CONFIG_LIBDIR_set=
ac_cv_env_PKG_CONFIG_LIBDIR_value=
ac_cv_env_PKG_CONFIG_PATH_set=set
ac_cv_env_PKG_CONFIG_PATH_value=
ac_cv_env_PKG_CONFIG_set=
ac_cv_env_PKG_CONFIG_value=
ac_cv_env_PROFILE_TASK_set=
ac_cv_env_PROFILE_TASK_value=
ac_cv_env_TCLTK_CFLAGS_set=
ac_cv_env_TCLTK_CFLAGS_value=
ac_cv_env_TCLTK_LIBS_set=
ac_cv_env_TCLTK_LIBS_value=
ac_cv_env_X11_CFLAGS_set=
ac_cv_env_X11_CFLAGS_value=
ac_cv_env_X11_LIBS_set=
ac_cv_env_X11_LIBS_value=
ac_cv_env_ZLIB_CFLAGS_set=
ac_cv_env_ZLIB_CFLAGS_value=
ac_cv_env_ZLIB_LIBS_set=
ac_cv_env_ZLIB_LIBS_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_host=x86_64-pc-linux-gnu
ac_cv_path_ac_pt_PKG_CONFIG=/usr/bin/pkg-config
ac_cv_prog_PYTHON_FOR_REGEN=python3.10
ac_cv_prog_ac_ct_CC=gcc

## ----------------- ##
## Output variables. ##
## ----------------- ##

ABIFLAGS=''
ALT_SOABI=''
AR=''
ARCH_RUN_32BIT=''
ARFLAGS=''
BASECFLAGS=''
BASECPPFLAGS='-IObjects -IInclude -IPython'
BINLIBDEST=''
BLDLIBRARY=''
BLDSHARED=''
BUILDEXEEXT=''
BZIP2_CFLAGS=''
BZIP2_LIBS=''
CC='gcc'
CCSHARED=''
CFLAGS=''
CFLAGSFORSHARED=''
CFLAGS_ALIASING=''
CFLAGS_NODIST=''
CONFIGURE_MACOSX_DEPLOYMENT_TARGET=''
CONFIG_ARGS=' '\''PKG_CONFIG_PATH='\'''
CPP=''
CPPFLAGS=''
CXX=''
DEFS=''
DEF_MAKE_ALL_RULE=''
DEF_MAKE_RULE=''
DFLAGS=''
DLINCLDIR=''
DLLLIBRARY=''
DTRACE=''
DTRACE_HEADERS=''
DTRACE_OBJS=''
DYNLOADFILE=''
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
EGREP=''
ENSUREPIP=''
EXEEXT=''
EXPORTSFROM=''
EXPORTSYMS=''
EXPORT_MACOSX_DEPLOYMENT_TARGET='#'
EXT_SUFFIX=''
FRAMEWORKALTINSTALLFIRST=''
FRAMEWORKALTINSTALLLAST=''
FRAMEWORKINSTALLAPPSPREFIX=''
FRAMEWORKINSTALLFIRST=''
FRAMEWORKINSTALLLAST=''
FRAMEWORKPYTHONW=''
FRAMEWORKUNIXTOOLSPREFIX='/usr/local'
FREEZE_MODULE='$(PYTHON_FOR_FREEZE) $(srcdir)/Programs/_freeze_module.py'
FREEZE_MODULE_BOOTSTRAP='./Programs/_freeze_module'
FREEZE_MODULE_BOOTSTRAP_DEPS='Programs/_freeze_module'
FREEZE_MODULE_DEPS='_bootstrap_python $(srcdir)/Programs/_freeze_module.py'
GDBM_CFLAGS=''
GDBM_LIBS=''
GITBRANCH=''
GITTAG=''
GITVERSION=''
GNULD=''
GREP=''
HAS_GIT='no-repository'
HAVE_GETHOSTBYNAME=''
HAVE_GETHOSTBYNAME_R=''
HAVE_GETHOSTBYNAME_R_3_ARG=''
HAVE_GETHOSTBYNAME_R_5_ARG=''
HAVE_GETHOSTBYNAME_R_6_ARG=''
HOSTRUNNER=''
INSTALL_DATA=''
INSTALL_PROGRAM=''
INSTALL_SCRIPT=''
INSTSONAME=''
LDCXXSHARED=''
LDFLAGS=''
LDFLAGS_NODIST=''
LDFLAGS_NOLTO=''
LDLIBRARY=''
LDLIBRARYDIR=''
LDSHARED=''
LDVERSION=''
LIBB2_CFLAGS=''
LIBB2_LIBS=''
LIBC=''
LIBCRYPT_CFLAGS=''
LIBCRYPT_LIBS=''
LIBEXPAT_CFLAGS=''
LIBEXPAT_INTERNAL=''
LIBEXPAT_LDFLAGS=''
LIBFFI_INCLUDEDIR=''
LIBLZMA_CFLAGS=''
LIBLZMA_LIBS=''
LIBM=''
LIBMPDEC_CFLAGS=''
LIBMPDEC_INTERNAL=''
LIBMPDEC_LDFLAGS=''
LIBNSL_CFLAGS=''
LIBNSL_LIBS=''
LIBOBJS=''
LIBPL=''
LIBPYTHON=''
LIBRARY=''
LIBRARY_DEPS=''
LIBS=''
LIBSQLITE3_CFLAGS=''
LIBSQLITE3_LIBS=''
LIBTOOL_CRUFT=''
LIBUUID_CFLAGS=''
LIBUUID_LIBS=''
LINKCC=''
LINKFORSHARED=''
LINK_PYTHON_DEPS=''
LINK_PYTHON_OBJS=''
LIPO_32BIT_FLAGS=''
LIPO_INTEL64_FLAGS=''
LLVM_AR=''
LLVM_AR_FOUND=''
LLVM_PROFDATA=''
LLVM_PROF_ERR=''
LLVM_PROF_FILE=''
LLVM_PROF_FOUND=''
LLVM_PROF_MERGER=''
LN=''
LTLIBOBJS=''
MACHDEP='linux'
MACHDEP_OBJS=''
MAINCC=''
MKDIR_P=''
MODULES_SETUP_STDLIB=''
MODULE_ARRAY_FALSE=''
MODULE_ARRAY_TRUE=''
MODULE_AUDIOOP_FALSE=''
MODULE_AUDIOOP_TRUE=''
MODULE_BINASCII_FALSE=''
MODULE_BINASCII_TRUE=''
MODULE_BLOCK=''
MODULE_BUILDTYPE=''
MODULE_CMATH_FALSE=''
MODULE_CMATH_TRUE=''
MODULE_FCNTL_FALSE=''
MODULE_FCNTL_TRUE=''
MODULE_GRP_FALSE=''
MODULE_GRP_TRUE=''
MODULE_MATH_FALSE=''
MODULE_MATH_TRUE=''
MODULE_MMAP_FALSE=''
MODULE_MMAP_TRUE=''
MODULE_NIS_FALSE=''
MODULE_NIS_TRUE=''
MODULE_OSSAUDIODEV_FALSE=''
MODULE_OSSAUDIODEV_TRUE=''
MODULE_PWD_FALSE=''
MODULE_PWD_TRUE=''
MODULE_PYEXPAT_FALSE=''
MODULE_PYEXPAT_TRUE=''
MODULE_RESOURCE_FALSE=''
MODULE_RESOURCE_TRUE=''
MODULE_SELECT_FALSE=''
MODULE_SELECT_TRUE=''
MODULE_SPWD_FALSE=''
MODULE_SPWD_TRUE=''
MODULE_SYSLOG_FALSE=''
MODULE_SYSLOG_TRUE=''
MODULE_TERMIOS_FALSE=''
MODULE_TERMIOS_TRUE=''
MODULE_TIME_FALSE=''
MODULE_TIME_TRUE=''
MODULE_UNICODEDATA_FALSE=''
MODULE_UNICODEDATA_TRUE=''
MODULE_XXLIMITED_35_FALSE=''
MODULE_XXLIMITED_35_TRUE=''
MODULE_XXLIMITED_FALSE=''
MODULE_XXLIMITED_TRUE=''
MODULE_ZLIB_FALSE=''
MODULE_ZLIB_TRUE=''
MODULE__ASYNCIO_FALSE=''
MODULE__ASYNCIO_TRUE=''
MODULE__BISECT_FALSE=''
MODULE__BISECT_TRUE=''
MODULE__BLAKE2_FALSE=''
MODULE__BLAKE2_TRUE=''
MODULE__BZ2_FALSE=''
MODULE__BZ2_TRUE=''
MODULE__CODECS_CN_FALSE=''
MODULE__CODECS_CN_TRUE=''
MODULE__CODECS_HK_FALSE=''
MODULE__CODECS_HK_TRUE=''
MODULE__CODECS_ISO2022_FALSE=''
MODULE__CODECS_ISO2022_TRUE=''
MODULE__CODECS_JP_FALSE=''
MODULE__CODECS_JP_TRUE=''
MODULE__CODECS_KR_FALSE=''
MODULE__CODECS_KR_TRUE=''
MODULE__CODECS_TW_FALSE=''
MODULE__CODECS_TW_TRUE=''
MODULE__CONTEXTVARS_FALSE=''
MODULE__CONTEXTVARS_TRUE=''
MODULE__CRYPT_FALSE=''
MODULE__CRYPT_TRUE=''
MODULE__CSV_FALSE=''
MODULE__CSV_TRUE=''
MODULE__CTYPES_TEST_FALSE=''
MODULE__CTYPES_TEST_TRUE=''
MODULE__DATETIME_FALSE=''
MODULE__DATETIME_TRUE=''
MODULE__DECIMAL_FALSE=''
MODULE__DECIMAL_TRUE=''
MODULE__ELEMENTTREE_FALSE=''
MODULE__ELEMENTTREE_TRUE=''
MODULE__GDBM_FALSE=''
MODULE__GDBM_TRUE=''
MODULE__HASHLIB_FALSE=''
MODULE__HASHLIB_TRUE=''
MODULE__HEAPQ_FALSE=''
MODULE__HEAPQ_TRUE=''
MODULE__IO_FALSE=''
MODULE__IO_TRUE=''
MODULE__JSON_FALSE=''
MODULE__JSON_TRUE=''
MODULE__LSPROF_FALSE=''
MODULE__LSPROF_TRUE=''
MODULE__LZMA_FALSE=''
MODULE__LZMA_TRUE=''
MODULE__MD5_FALSE=''
MODULE__MD5_TRUE=''
MODULE__MULTIBYTECODEC_FALSE=''
MODULE__MULTIBYTECODEC_TRUE=''
MODULE__MULTIPROCESSING_FALSE=''
MODULE__MULTIPROCESSING_TRUE=''
MODULE__OPCODE_FALSE=''
MODULE__OPCODE_TRUE=''
MODULE__PICKLE_FALSE=''
MODULE__PICKLE_TRUE=''
MODULE__POSIXSHMEM_FALSE=''
MODULE__POSIXSHMEM_TRUE=''
MODULE__POSIXSUBPROCESS_FALSE=''
MODULE__POSIXSUBPROCESS_TRUE=''
MODULE__QUEUE_FALSE=''
MODULE__QUEUE_TRUE=''
MODULE__RANDOM_FALSE=''
MODULE__RANDOM_TRUE=''
MODULE__SCPROXY_FALSE=''
MODULE__SCPROXY_TRUE=''
MODULE__SHA1_FALSE=''
MODULE__SHA1_TRUE=''
MODULE__SHA256_FALSE=''
MODULE__SHA256_TRUE=''
MODULE__SHA3_FALSE=''
MODULE__SHA3_TRUE=''
MODULE__SHA512_FALSE=''
MODULE__SHA512_TRUE=''
MODULE__SOCKET_FALSE=''
MODULE__SOCKET_TRUE=''
MODULE__SQLITE3_FALSE=''
MODULE__SQLITE3_TRUE=''
MODULE__SSL_FALSE=''
MODULE__SSL_TRUE=''
MODULE__STATISTICS_FALSE=''
MODULE__STATISTICS_TRUE=''
MODULE__STRUCT_FALSE=''
MODULE__STRUCT_TRUE=''
MODULE__TESTBUFFER_FALSE=''
MODULE__TESTBUFFER_TRUE=''
MODULE__TESTCAPI_FALSE=''
MODULE__TESTCAPI_TRUE=''
MODULE__TESTCLINIC_FALSE=''
MODULE__TESTCLINIC_TRUE=''
MODULE__TESTIMPORTMULTIPLE_FALSE=''
MODULE__TESTIMPORTMULTIPLE_TRUE=''
MODULE__TESTINTERNALCAPI_FALSE=''
MODULE__TESTINTERNALCAPI_TRUE=''
MODULE__TESTMULTIPHASE_FALSE=''
MODULE__TESTMULTIPHASE_TRUE=''
MODULE__TKINTER_FALSE=''
MODULE__TKINTER_TRUE=''
MODULE__TYPING_FALSE=''
MODULE__TYPING_TRUE=''
MODULE__UUID_FALSE=''
MODULE__UUID_TRUE=''
MODULE__XXSUBINTERPRETERS_FALSE=''
MODULE__XXSUBINTERPRETERS_TRUE=''
MODULE__XXTESTFUZZ_FALSE=''
MODULE__XXTESTFUZZ_TRUE=''
MODULE__ZONEINFO_FALSE=''
MODULE__ZONEINFO_TRUE=''
MULTIARCH=''
MULTIARCH_CPPFLAGS=''
NODE=''
NO_AS_NEEDED=''
OBJEXT=''
OPENSSL_INCLUDES=''
OPENSSL_LDFLAGS=''
OPENSSL_LIBS=''
OPENSSL_RPATH=''
OPT=''
OTHER_LIBTOOL_OPT=''
PACKAGE_BUGREPORT='https://github.com/python/cpython/issues/'
PACKAGE_NAME='python'
PACKAGE_STRING='python 3.11'
PACKAGE_TARNAME='python'
PACKAGE_URL=''
PACKAGE_VERSION='3.11'
PATH_SEPARATOR=':'
PGO_PROF_GEN_FLAG=''
PGO_PROF_USE_FLAG=''
PKG_CONFIG='/usr/bin/pkg-config'
PKG_CONFIG_LIBDIR=''
PKG_CONFIG_PATH=''
PLATFORM_HEADERS=''
PLATFORM_OBJS=''
PLATFORM_TRIPLET=''
PLATLIBDIR=''
PROFILE_TASK=''
PY3LIBRARY=''
PYTHONFRAMEWORK=''
PYTHONFRAMEWORKDIR='no-framework'
PYTHONFRAMEWORKIDENTIFIER='org.python.python'
PYTHONFRAMEWORKINSTALLDIR=''
PYTHONFRAMEWORKPREFIX=''
PYTHON_FOR_BUILD='./$(BUILDPYTHON) -E'
PYTHON_FOR_BUILD_DEPS='$(BUILDPYTHON)'
PYTHON_FOR_FREEZE='./_bootstrap_python'
PYTHON_FOR_REGEN='python3.10'
PY_ENABLE_SHARED=''
READELF=''
RUNSHARED=''
SED=''
SHELL='/bin/bash'
SHLIBS=''
SHLIB_SUFFIX=''
SOABI=''
SOVERSION='1.0'
SRCDIRS=''
STATIC_LIBPYTHON=''
TCLTK_CFLAGS=''
TCLTK_LIBS=''
TEST_MODULES=''
THREADHEADERS=''
TRUE=''
TZPATH=''
UNIVERSALSDK=''
UNIVERSAL_ARCH_FLAGS=''
VERSION='3.11'
WASM_ASSETS_DIR=''
WASM_STDLIB=''
WHEEL_PKG_DIR=''
X11_CFLAGS=''
X11_LIBS=''
ZLIB_CFLAGS=''
ZLIB_LIBS=''
_PYTHON_HOST_PLATFORM=''
ac_ct_AR=''
ac_ct_CC='gcc'
ac_ct_CXX=''
ac_ct_READELF=''
bindir='${exec_prefix}/bin'
build='x86_64-pc-linux-gnu'
build_alias=''
build_cpu='x86_64'
build_os='linux-gnu'
build_vendor='pc'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='NONE'
host='x86_64-pc-linux-gnu'
host_alias=''
host_cpu='x86_64'
host_os='linux-gnu'
host_vendor='pc'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='NONE'
program_transform_name='s,x,x,'
psdir='${docdir}'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='${prefix}/etc'
target_alias=''

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define _NETBSD_SOURCE 1
#define __BSD_VISIBLE 1
#define _DARWIN_C_SOURCE 1
#define _PYTHONFRAMEWORK ""
#define _XOPEN_SOURCE 700
#define _XOPEN_SOURCE_EXTENDED 1
#define _POSIX_C_SOURCE 200809L

configure: exit 1
