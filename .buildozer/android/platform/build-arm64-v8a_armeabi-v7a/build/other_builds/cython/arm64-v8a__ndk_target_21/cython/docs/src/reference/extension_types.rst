.. highlight:: cython

***************
Extension Types
***************

.. note::

    The sections in this page were moved to the :ref:`extension-types`
    and :ref:`special-methods` in the userguide.

==========
Attributes
==========

This section was moved to :ref:`readonly`.

=======
Methods
=======

==========
Properties
==========

This section was moved to :ref:`properties`.

===============
Special Methods
===============

This section was moved to :ref:`special-methods`.

Declaration
===========

This section was moved to :ref:`declaration`.

Docstrings
==========

This section was moved to :ref:`docstrings`.

Initialization: ``__cinit__()`` and ``__init__()``
==================================================

This section was moved to :ref:`initialisation_methods`.

Finalization: ``__dealloc__()``
===============================

This section was moved to :ref:`finalization_method`.

Arithmetic Methods
==================

This section was moved to :ref:`arithmetic_methods`.

Rich Comparisons
================

This section was moved to :ref:`righ_comparisons`.

The ``__next__()`` Method
=========================

This section was moved to :ref:`the__next__method`.

===========
Subclassing
===========

This section was moved to :ref:`subclassing`.

====================
Forward Declarations
====================

This section was moved to :ref:`forward_declaring_extension_types`.

========================
Extension Types and None
========================

This section was moved to :ref:`extension_types_and_none`.

================
Weak Referencing
================

This section was moved to :ref:`making_extension_types_weak_referenceable`.

==================
Dynamic Attributes
==================

This section was moved to :ref:`dynamic_attributes`.

=========================
External and Public Types
=========================


Public
======

This section was moved to :ref:`public`.

External
========

This section was moved to :ref:`external_extension_types`.

Name Specification Clause
=========================

This section was moved to :ref:`name_specification_clause`.

================================
Type Names vs. Constructor Names
================================

This section was moved to :ref:`types_names_vs_constructor_names`.
