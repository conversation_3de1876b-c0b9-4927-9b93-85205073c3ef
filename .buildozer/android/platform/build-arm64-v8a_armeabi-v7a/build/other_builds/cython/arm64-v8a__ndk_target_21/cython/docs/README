Welcome to Cython's documentation.

To build the documentation on Linux, you need Make and Sphinx installed on your system. Then execute::

	make html

On windows systems, you only need Sphinx. Open PowerShell and type::

	./make.bat html

You can then see the documentation by opening in a browser ``cython/docs/build/html/index.html``.

The current Cython documentation files are hosted at
https://cython.readthedocs.io/en/latest/


Notes
=======

1) Some css work should definitely be done.
2) Use local 'top-of-page' contents rather than the sidebar, imo.
3) Provide a link from each (sub)section to the TOC of the page.
4) Fix cython highlighter for cdef blocks
