40
targetIdent
0
MProject
1
MComponent
0
2
WString
4
NEXE
3
WString
5
nc2en
1
0
0
4
MCommand
0
5
MCommand
34
cd ..\..
projects\owatcom\pngtest
6
MItem
11
pngtest.exe
7
WString
4
NEXE
8
WVList
4
9
MVState
10
WString
7
WINLINK
11
WString
28
?????Library directories(;):
1
12
WString
8
$(%zlib)
0
13
MVState
14
WString
7
WINLINK
15
WString
18
?????Libraries(,):
1
16
WString
19
libpng.lib zlib.lib
0
17
MVState
18
WString
7
WINLINK
19
WString
28
?????Library directories(;):
0
20
WString
8
$(%zlib)
0
21
MVState
22
WString
7
WINLINK
23
WString
18
?????Libraries(,):
0
24
WString
19
libpng.lib zlib.lib
0
25
WVList
0
-1
1
1
0
26
WPickList
2
27
MItem
3
*.c
28
WString
4
COBJ
29
WVList
2
30
MVState
31
WString
3
WCC
32
WString
25
n????Include directories:
1
33
WString
39
"$(%zlib);$(%watcom)/h;$(%watcom)/h/nt"
0
34
MVState
35
WString
3
WCC
36
WString
25
n????Include directories:
0
37
WString
39
"$(%zlib);$(%watcom)/h;$(%watcom)/h/nt"
0
38
WVList
0
-1
1
1
0
39
MItem
15
..\..\pngtest.c
40
WString
4
COBJ
41
WVList
0
42
WVList
0
27
1
1
0
