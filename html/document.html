<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<HTML>
<HEAD>
<TITLE>
TIFF Documentation
</TITLE>
<style type="text/css">
body {
font-family: Arial, Helvetica, Sans;
}
</style>
</HEAD>
<BODY>
<H1>
<IMG SRC="images/jim.gif" WIDTH=139 HEIGHT=170 ALIGN=left BORDER=1 HSPACE=6 ALT="jim">
TIFF Documentation
</H1>

<P>
A copy of the 6.0 specification is available from Adobe at
<A HREF="http://partners.adobe.com/public/developer/en/tiff/TIFF6.pdf">http://partners.adobe.com/public/developer/en/tiff/TIFF6.pdf</A>, or from the libtiff
ftp site at <a href="https://download.osgeo.org/libtiff/doc/TIFF6.pdf">
https://download.osgeo.org/libtiff/doc/TIFF6.pdf</A>.<p>

<P>
Draft <a href="TIFFTechNote2.html">TIFF Technical Note #2</A> covers problems 
with the TIFF 6.0 design for embedding JPEG-compressed data in TIFF, and 
describes an alternative. <p>

Other Adobe information on TIFF can be retrieved from:

<A HREF="http://partners.adobe.com/public/developer/tiff/index.html">
http://partners.adobe.com/public/developer/tiff/index.html</A>

<P>
Joris Van Damme maintains a list of known tags and their descriptions and
definitions. It is available online at
<A HREF="http://www.awaresystems.be/imaging/tiff/tifftags.html">
http://www.awaresystems.be/imaging/tiff/tifftags.html</A>

<P>
There is a FAQ, related both to TIFF format and libtiff library:
<A HREF="http://www.awaresystems.be/imaging/tiff/faq.html">
http://www.awaresystems.be/imaging/tiff/faq.html</A>

<P>
There is a preliminary <a href="bigtiffdesign.html">BigTIFF Design</a> for
a TIFF variation supporting files larger than 4GB.

<HR>

<ADDRESS>
 Last updated: $Date: 2016-09-25 20:05:44 $
</ADDRESS>

</BODY>
</HTML>
