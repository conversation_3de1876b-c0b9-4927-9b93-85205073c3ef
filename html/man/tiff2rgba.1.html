<!-- Creator     : groff version 1.18.1 -->
<!-- CreationDate: Sat Feb 24 18:37:19 2007 -->
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta name="Content-Style" content="text/css">
<title>TIFF2RGBA</title>
</head>
<body>

<h1 align=center>TIFF2RGBA</h1>
<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#OPTIONS">OPTIONS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>
<a name="NAME"></a>
<h2>NAME</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>tiff2rgba &minus; convert a <small>TIFF</small> image to
RGBA color space</p>
</td>
</table>
<a name="SYNOPSIS"></a>
<h2>SYNOPSIS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>tiff2rgba</b> [ <i>options</i> ] <i>input.tif
output.tif</i></p>
</td>
</table>
<a name="DESCRIPTION"></a>
<h2>DESCRIPTION</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><i>Tiff2rgba</i> converts a wide variety of TIFF images
into an RGBA TIFF image. This includes the ability to
translate different color spaces and photometric
interpretation into RGBA, support for alpha blending, and
translation of many different bit depths into a 32bit RGBA
image.</p>
<!-- INDENTATION -->
<p>Internally this program is implemented using the
<i>TIFFReadRGBAImage()</i> function, and it suffers any
limitations of that image. This includes limited support for
&gt; 8 BitsPerSample images, and flaws with some esoteric
combinations of BitsPerSample, photometric interpretation,
block organization and planar configuration.</p>
<!-- INDENTATION -->
<p>The generated images are stripped images with four
samples per pixel (red, green, blue and alpha) or if the
<b>&minus;n</b> flag is used, three samples per pixel (red,
green, and blue). The resulting images are always planar
configuration contiguous. For this reason, this program is a
useful utility for transform exotic TIFF files into a form
ingestible by almost any TIFF supporting software.</p>
</td>
</table>
<a name="OPTIONS"></a>
<h2>OPTIONS</h2>
<!-- TABS -->
<table width="100%" border=0 rules="none" frame="void"
       cols="5" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="2%">

<p><b>&minus;c</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify a compression scheme to use when writing image
data: <b>&minus;c none</b> for no compression (the default),
<b>&minus;c packbits</b> for the PackBits compression
algorithm, <b>&minus;c zip</b> for the Deflate compression
algorithm, <b>&minus;c jpeg</b> for the JPEG compression
algorithm, and <b>&minus;c lzw</b> for Lempel-Ziv &amp;
Welch.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="2%">

<p><b>&minus;r</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Write data with a specified number of rows per strip; by
default the number of rows/strip is selected so that each
strip is approximately 8 kilobytes.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="2%">

<p><b>&minus;b</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Process the image one block (strip/tile) at a time
instead of by reading the whole image into memory at once.
This may be necessary for very large images on systems with
limited RAM.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="2%">

<p><b>&minus;n</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Drop the alpha component from the output file, producing
a pure RGB file. Currently this does not work if the
<b>&minus;b</b> flag is also in effect.</p>
</td>
<td width="0%">
</td>
</table>
<a name="SEE ALSO"></a>
<h2>SEE ALSO</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>tiff2bw</b>(1), <b>TIFFReadRGBAImage</b>(3t),
<b>libtiff</b>(3)</p>
<!-- INDENTATION -->
<p>Libtiff library home page:
<b>http://www.simplesystems.org/libtiff/</b></p>
</td>
</table>
<hr>
</body>
</html>
