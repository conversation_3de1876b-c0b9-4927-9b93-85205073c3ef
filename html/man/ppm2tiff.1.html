<!-- Creator     : groff version 1.18.1 -->
<!-- CreationDate: Sat Feb 24 18:37:19 2007 -->
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta name="Content-Style" content="text/css">
<title>PPM2TIFF</title>
</head>
<body>

<h1 align=center>PPM2TIFF</h1>
<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#OPTIONS">OPTIONS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>
<a name="NAME"></a>
<h2>NAME</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>ppm2tiff &minus; create a <small>TIFF</small> file from
<small>PPM, PGM</small> and <small>PBM</small> image
files</p>
</td>
</table>
<a name="SYNOPSIS"></a>
<h2>SYNOPSIS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>ppm2tiff</b> [ <i>options</i> ] [ <i>input.ppm</i> ]
<i>output.tif</i></p>
</td>
</table>
<a name="DESCRIPTION"></a>
<h2>DESCRIPTION</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><i>ppm2tiff</i> converts a file in the <small>PPM,
PGM</small> and <small>PBM</small> image formats to
<small>TIFF.</small> By default, the <small>TIFF</small>
image is created with data samples packed
(<i>PlanarConfiguration</i>=1), compressed with the Packbits
algorithm (<i>Compression</i>=32773), and with each strip no
more than 8 kilobytes. These characteristics can be
overridden, or explicitly specified with the options
described below</p>
<!-- INDENTATION -->
<p>If the <small>PPM</small> file contains greyscale data,
then the <i>PhotometricInterpretation</i> tag is set to 1
(min-is-black), otherwise it is set to 2 (RGB).</p>
<!-- INDENTATION -->
<p>If no <small>PPM</small> file is specified on the command
line, <i>ppm2tiff</i> will read from the standard input.</p>
</td>
</table>
<a name="OPTIONS"></a>
<h2>OPTIONS</h2>
<!-- TABS -->
<table width="100%" border=0 rules="none" frame="void"
       cols="5" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;c</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify a compression scheme to use when writing image
data: <b>none</b> for no compression, <b>packbits</b> for
PackBits compression (will be used by default), <b>lzw</b>
for Lempel-Ziv &amp; Welch compression, <b>jpeg</b> for
baseline JPEG compression, <b>zip</b> for Deflate
compression, <b>g3</b> for CCITT Group 3 (T.4) compression,
and <b>g4</b> for CCITT Group 4 (T.6) compression.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;r</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Write data with a specified number of rows per strip; by
default the number of rows/strip is selected so that each
strip is approximately 8 kilobytes.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;R</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Mark the resultant image to have the specified X and Y
resolution (in dots/inch).</p>
</td>
<td width="0%">
</td>
</table>
<a name="SEE ALSO"></a>
<h2>SEE ALSO</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>tiffinfo</b>(1), <b>tiffcp</b>(1),
<b>tiffmedian</b>(1), <b>libtiff</b>(3)</p>
<!-- INDENTATION -->
<p>Libtiff library home page:
<b>http://www.simplesystems.org/libtiff/</b></p>
</td>
</table>
<hr>
</body>
</html>
