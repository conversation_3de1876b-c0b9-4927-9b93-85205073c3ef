<!-- Creator     : groff version 1.18.1 -->
<!-- CreationDate: Sat Feb 24 18:37:15 2007 -->
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta name="Content-Style" content="text/css">
<title>TIFFBUFFER</title>
</head>
<body>

<h1 align=center>TIFFBUFFER</h1>
<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#DIAGNOSTICS">DIAGNOSTICS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>
<a name="NAME"></a>
<h2>NAME</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>TIFFReadBufferSetup, TIFFWriteBufferSetup &minus; I/O
buffering control routines</p>
</td>
</table>
<a name="SYNOPSIS"></a>
<h2>SYNOPSIS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<pre><b>#include &lt;tiffio.h&gt;

int TIFFReadBufferSetup(TIFF *</b><i>tif</i><b>, tdata_t</b> <i>buffer</i><b>, tsize_t</b> <i>size</i><b>);
int TIFFWriteBufferSetup(TIFF *</b><i>tif</i><b>, tdata_t</b> <i>buffer</i><b>, tsize_t</b> <i>size</i><b>);
</b></pre>
</td>
</table>
<a name="DESCRIPTION"></a>
<h2>DESCRIPTION</h2>
<!-- INDENTATION -->

<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>The following routines are provided for client-control of
the I/O buffers used by the library. Applications need never
use these routines; they are provided only for
&lsquo;&lsquo;intelligent clients&rsquo;&rsquo; that wish to
optimize memory usage and/or eliminate potential copy
operations that can occur when working with images that have
data stored without compression.</p>
<!-- INDENTATION -->
<p><i>TIFFReadBufferSetup</i> sets up the data buffer used
to read raw (encoded) data from a file. If the specified
pointer is <small>NULL</small> (zero), then a buffer of the
appropriate size is allocated. Otherwise the caller must
guarantee that the buffer is large enough to hold any
individual strip of raw data. <i>TIFFReadBufferSetup</i>
returns a non-zero value if the setup was successful and
zero otherwise.</p>
<!-- INDENTATION -->
<p><i>TIFFWriteBufferSetup</i> sets up the data buffer used
to write raw (encoded) data to a file. If the specified
<i>size</i> is &minus;1 then the buffer size is selected to
hold a complete tile or strip, or at least 8 kilobytes,
whichever is greater. If the specified <i>buffer</i> is
<small>NULL</small> (zero), then a buffer of the appropriate
size is dynamically allocated. <i>TIFFWriteBufferSetup</i>
returns a non-zero value if the setup was successful and
zero otherwise.</p>
</td>
</table>
<a name="DIAGNOSTICS"></a>
<h2>DIAGNOSTICS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>%s: No space for data buffer at scanline %ld</b>.
<i>TIFFReadBufferSetup</i> was unable to dynamically
allocate space for a data buffer.</p>
<!-- INDENTATION -->
<p><b>%s: No space for output buffer</b>.
<i>TIFFWriteBufferSetup</i> was unable to dynamically
allocate space for a data buffer.</p>
</td>
</table>
<a name="SEE ALSO"></a>
<h2>SEE ALSO</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>libtiff</b>(3TIFF)</p>
<!-- INDENTATION -->
<p>Libtiff library home page:
<b>http://www.simplesystems.org/libtiff/</b></p>
</td>
</table>
<hr>
</body>
</html>
