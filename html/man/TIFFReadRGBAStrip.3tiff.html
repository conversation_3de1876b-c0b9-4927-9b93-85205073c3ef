<!-- Creator     : groff version 1.18.1 -->
<!-- CreationDate: Sat Feb 24 18:37:17 2007 -->
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta name="Content-Style" content="text/css">
<title>TIFFReadRGBAStrip</title>
</head>
<body>

<h1 align=center>TIFFReadRGBAStrip</h1>
<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#NOTES">NOTES</a><br>
<a href="#RETURN VALUES">RETURN VALUES</a><br>
<a href="#DIAGNOSTICS">DIAGNOSTICS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>
<a name="NAME"></a>
<h2>NAME</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>TIFFReadRGBAStrip &minus; read and decode an image strip
into a fixed-format raster</p>
</td>
</table>
<a name="SYNOPSIS"></a>
<h2>SYNOPSIS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>#include &lt;tiffio.h&gt;</b></p>
<!-- INDENTATION -->
<p><b>#define TIFFGetR(abgr) ((abgr) &amp; 0xff)<br>
#define TIFFGetG(abgr) (((abgr) &gt;&gt; 8) &amp; 0xff)<br>
#define TIFFGetB(abgr) (((abgr) &gt;&gt; 16) &amp; 0xff)<br>
#define TIFFGetA(abgr) (((abgr) &gt;&gt; 24) &amp;
0xff)</b></p>
<!-- INDENTATION -->
<p><b>int TIFFReadRGBAStrip(TIFF *</b><i>tif</i><b>,
uint32</b> <i>row</i><b>, uint32
*</b><i>raster</i><b>)</b></p>
</td>
</table>
<a name="DESCRIPTION"></a>
<h2>DESCRIPTION</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><i>TIFFReadRGBAStrip</i> reads a single strip of a
strip-based image into memory, storing the result in the
user supplied RGBA <i>raster</i>. The raster is assumed to
be an array of width times rowsperstrip 32-bit entries,
where width is the width of the image (TIFFTAG_IMAGEWIDTH)
and rowsperstrip is the maximum lines in a strip
(TIFFTAG_ROWSPERSTRIP).</p>
<!-- INDENTATION -->
<p>The <i>row</i> value should be the row of the first row
in the strip (strip * rowsperstrip, zero based).</p>
<!-- INDENTATION -->
<p>Note that the raster is assume to be organized such that
the pixel at location (<i>x</i>,<i>y</i>) is
<i>raster</i>[<i>y</i>*<i>width</i>+<i>x</i>]; with the
raster origin in the <i>lower-left hand corner</i> of the
strip. That is bottom to top organization. When reading a
partial last strip in the file the last line of the image
will begin at the beginning of the buffer.</p>
<!-- INDENTATION -->
<p>Raster pixels are 8-bit packed red, green, blue, alpha
samples. The macros <i>TIFFGetR</i>, <i>TIFFGetG</i>,
<i>TIFFGetB</i>, and <i>TIFFGetA</i> should be used to
access individual samples. Images without Associated Alpha
matting information have a constant Alpha of 1.0 (255).</p>
<!-- INDENTATION -->
<p>See the <i>TIFFRGBAImage</i>(3TIFF) page for more details
on how various image types are converted to RGBA values.</p>
</td>
</table>
<a name="NOTES"></a>
<h2>NOTES</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>Samples must be either 1, 2, 4, 8, or 16 bits.
Colorimetric samples/pixel must be either 1, 3, or 4 (i.e.
<i>SamplesPerPixel</i> minus <i>ExtraSamples</i>).</p>
<!-- INDENTATION -->
<p>Palette image colormaps that appear to be incorrectly
written as 8-bit values are automatically scaled to
16-bits.</p>
<!-- INDENTATION -->
<p><i>TIFFReadRGBAStrip</i> is just a wrapper around the
more general <i>TIFFRGBAImage</i>(3TIFF) facilities.
It&rsquo;s main advantage over the similar
<i>TIFFReadRGBAImage()</i> function is that for large images
a single buffer capable of holding the whole image
doesn&rsquo;t need to be allocated, only enough for one
strip. The <i>TIFFReadRGBATile()</i> function does a similar
operation for tiled images.</p>
</td>
</table>
<a name="RETURN VALUES"></a>
<h2>RETURN VALUES</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>1 is returned if the image was successfully read and
converted. Otherwise, 0 is returned if an error was
encountered.</p>
</td>
</table>
<a name="DIAGNOSTICS"></a>
<h2>DIAGNOSTICS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>All error messages are directed to the
<i>TIFFError</i>(3TIFF) routine.</p>
<!-- INDENTATION -->
<p><b>Sorry, can not handle %d-bit pictures</b>. The image
had <i>BitsPerSample</i> other than 1, 2, 4, 8, or 16.</p>
<!-- INDENTATION -->
<p><b>Sorry, can not handle %d-channel images</b>. The image
had <i>SamplesPerPixel</i> other than 1, 3, or 4.</p>
<!-- INDENTATION -->
<p><b>Missing needed &quot;PhotometricInterpretation&quot;
tag</b>. The image did not have a tag that describes how to
display the data.</p>
<!-- INDENTATION -->
<p><b>No &quot;PhotometricInterpretation&quot; tag, assuming
RGB</b>. The image was missing a tag that describes how to
display it, but because it has 3 or 4 samples/pixel, it is
assumed to be <small>RGB.</small></p>
<!-- INDENTATION -->
<p><b>No &quot;PhotometricInterpretation&quot; tag, assuming
min-is-black</b>. The image was missing a tag that describes
how to display it, but because it has 1 sample/pixel, it is
assumed to be a grayscale or bilevel image.</p>
<!-- INDENTATION -->
<p><b>No space for photometric conversion table</b>. There
was insufficient memory for a table used to convert image
samples to 8-bit <small>RGB.</small></p>
<!-- INDENTATION -->
<p><b>Missing required &quot;Colormap&quot; tag</b>. A
Palette image did not have a required <i>Colormap</i>
tag.</p>
<!-- INDENTATION -->
<p><b>No space for tile buffer</b>. There was insufficient
memory to allocate an i/o buffer.</p>
<!-- INDENTATION -->
<p><b>No space for strip buffer</b>. There was insufficient
memory to allocate an i/o buffer.</p>
<!-- INDENTATION -->
<p><b>Can not handle format</b>. The image has a format
(combination of <i>BitsPerSample</i>,
<i>SamplesPerPixel</i>, and
<i>PhotometricInterpretation</i>) that
<i>TIFFReadRGBAImage</i> can not handle.</p>
<!-- INDENTATION -->
<p><b>No space for B&amp;W mapping table</b>. There was
insufficient memory to allocate a table used to map
grayscale data to <small>RGB.</small></p>
<!-- INDENTATION -->
<p><b>No space for Palette mapping table</b>. There was
insufficient memory to allocate a table used to map data to
8-bit <small>RGB.</small></p>
</td>
</table>
<a name="SEE ALSO"></a>
<h2>SEE ALSO</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>TIFFOpen</b>(3TIFF), <b>TIFFRGBAImage</b>(3TIFF),
<b>TIFFReadRGBAImage</b>(3TIFF),
<b>TIFFReadRGBATile</b>(3TIFF), <b>libtiff</b>(3TIFF)</p>
<!-- INDENTATION -->
<p>Libtiff library home page:
<b>http://www.simplesystems.org/libtiff/</b></p>
</td>
</table>
<hr>
</body>
</html>
