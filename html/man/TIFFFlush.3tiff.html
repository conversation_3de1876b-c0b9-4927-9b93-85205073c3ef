<!-- Creator     : groff version 1.18.1 -->
<!-- CreationDate: Sat Feb 24 18:37:16 2007 -->
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta name="Content-Style" content="text/css">
<title>TIFFFlush</title>
</head>
<body>

<h1 align=center>TIFFFlush</h1>
<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#RETURN VALUES">RETURN VALUES</a><br>
<a href="#DIAGNOSTICS">DIAGNOSTICS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>
<a name="NAME"></a>
<h2>NAME</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>TIFFFlush, TIFFFlushData &minus; flush pending writes to
an open <small>TIFF</small> file</p>
</td>
</table>
<a name="SYNOPSIS"></a>
<h2>SYNOPSIS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>#include &lt;tiffio.h&gt;</b></p>
<!-- INDENTATION -->
<p><b>int TIFFFlush(TIFF *</b><i>tif</i><b>)<br>
int TIFFFlushData(TIFF *</b><i>tif</i><b>)</b></p>
</td>
</table>
<a name="DESCRIPTION"></a>
<h2>DESCRIPTION</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><i>TIFFFlush</i> causes any pending writes for the
specified file (including writes for the current directory)
to be done. In normal operation this call is never needed
&minus; the library automatically does any flushing
required.</p>
<!-- INDENTATION -->
<p><i>TIFFFlushData</i> flushes any pending image data for
the specified file to be written out; directory-related data
are not flushed. In normal operation this call is never
needed &minus; the library automatically does any flushing
required.</p>
</td>
</table>
<a name="RETURN VALUES"></a>
<h2>RETURN VALUES</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>0 is returned if an error is encountered, otherwise 1 is
returned.</p>
</td>
</table>
<a name="DIAGNOSTICS"></a>
<h2>DIAGNOSTICS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>All error messages are directed to the
<b>TIFFError</b>(3TIFF) routine.</p>
</td>
</table>
<a name="SEE ALSO"></a>
<h2>SEE ALSO</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>TIFFOpen</b>(3TIFF),
<b>TIFFWriteEncodedStrip</b>(3TIFF),
<b>TIFFWriteEncodedTile</b>(3TIFF),
<b>TIFFWriteRawStrip</b>(3TIFF),
<b>TIFFWriteRawTile</b>(3TIFF),
<b>TIFFWriteScanline</b>(3TIFF), <b>TIFFWriteTile</b>(3TIFF)
<b>libtiff</b>(3TIFF),</p>
<!-- INDENTATION -->
<p>Libtiff library home page:
<b>http://www.simplesystems.org/libtiff/</b></p>
</td>
</table>
<hr>
</body>
</html>
