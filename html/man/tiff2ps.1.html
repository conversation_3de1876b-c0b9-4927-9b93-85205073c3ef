<!-- Creator     : groff version 1.18.1 -->
<!-- CreationDate: Sat Feb 24 18:37:19 2007 -->
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta name="Content-Style" content="text/css">
<title>TIFF2PS</title>
</head>
<body>

<h1 align=center>TIFF2PS</h1>
<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#OPTIONS">OPTIONS</a><br>
<a href="#EXAMPLES">EXAMPLES</a><br>
<a href="#BUGS">BUGS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>
<a name="NAME"></a>
<h2>NAME</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>tiff2ps &minus; convert a <small>TIFF</small> image to
PostScript&trade;</p>
</td>
</table>
<a name="SYNOPSIS"></a>
<h2>SYNOPSIS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>tiff2ps</b> [ <i>options</i> ] <i>input.tif
...</i></p>
</td>
</table>
<a name="DESCRIPTION"></a>
<h2>DESCRIPTION</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><i>tiff2ps</i> reads <small>TIFF</small> images and
writes PostScript or Encapsulated PostScript (EPS) on the
standard output. By default, <i>tiff2ps</i> writes
Encapsulated PostScript for the first image in the specified
<small>TIFF</small> image file.</p>
<!-- INDENTATION -->
<p>By default, <i>tiff2ps</i> will generate PostScript that
fills a printed area specified by the <small>TIFF</small>
tags in the input file. If the file does not contain
<i>XResolution</i> or <i>YResolution</i> tags, then the
printed area is set according to the image dimensions. The
<b>&minus;w</b> and <b>&minus;h</b> options (see below) can
be used to set the dimensions of the printed area in inches;
overriding any relevant <small>TIFF</small> tags.</p>
<!-- INDENTATION -->
<p>The PostScript generated for <small>RGB,</small> palette,
and <small>CMYK</small> images uses the <i>colorimage</i>
operator. The PostScript generated for greyscale and bilevel
images uses the <i>image</i> operator. When the
<i>colorimage</i> operator is used, PostScript code to
emulate this operator on older PostScript printers is also
generated. Note that this emulation code can be very
slow.</p>
<!-- INDENTATION -->
<p>Color images with associated alpha data are composited
over a white background.</p>
</td>
</table>
<a name="OPTIONS"></a>
<h2>OPTIONS</h2>
<!-- TABS -->
<table width="100%" border=0 rules="none" frame="void"
       cols="5" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;1</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Generate PostScript Level 1 (the default).</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;2</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Generate PostScript Level 2.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;3</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Generate PostScript Level 3. It basically allows one to
use the /flateDecode filter for ZIP compressed TIFF
images.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;8</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Disable use of ASCII85 encoding with PostScript Level 2/3.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;a</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Generate output for all IFDs (pages) in the input
file.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;b</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify the bottom margin for the output (in inches).
This does not affect the height of the printed image.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;c</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Center the image in the output. This option only shows
an effect if both the <b>&minus;w</b> and the
<b>&minus;h</b> option are given.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;C</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify the document creator name.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;d</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Set the initial <small>TIFF</small> directory to the
specified directory number. (NB: Directories are numbered
starting at zero.) This option is useful for selecting
individual pages in a multi-page (e.g. facsimile) file.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;D</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Enable duplex printing (two pages per sheet of paper).</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;e</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Force the generation of Encapsulated PostScript (implies
<b>&minus;z</b>).</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;h</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify the vertical size of the printed area (in
inches).</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;H</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify the maximum height of image (in inches). Images
with larger sizes will be split in several pages. Option
<b>&minus;L</b> may be used for specifying size of split
images overlapping.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;i</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Enable/disable pixel interpolation. This option requires
a single numeric value: zero to disable pixel interpolation
and non-zero to enable. The default is enabled.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;L</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify the size of overlapping for split images (in
inches). Used in conjunction with <b>&minus;H</b> and <b>&minus;W</b>
options.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;l</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify the left margin for the output (in inches). This
does not affect the width of the printed image.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;m</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Where possible render using the <i>imagemask</i>
PostScript operator instead of the <i>image</i> operator.
When this option is specified <i>tiff2ps</i> will use
<i>imagemask</i> for rendering 1 bit deep images. If this
option is not specified or if the image depth is greater
than 1 then the <i>image</i> operator is used.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;o</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Set the initial <small>TIFF</small> directory to the
<small>IFD</small> at the specified file offset. This option
is useful for selecting thumbnail images and the like which
are hidden using the <i>SubIFD</i> tag.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;O</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Write PostScript to specified file instead of standard output.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;p</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Force the generation of (non-Encapsulated)
PostScript.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;P</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Set optional PageOrientation DSC comment to Landscape or Portrait.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;r</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Rotate image by 180 degrees.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;s</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Generate output for a single IFD (page) in the input
file.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;t</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify the document title string.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;T</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Print pages for top edge binding.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;w</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify the horizontal size of the printed area (in
inches).</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;W</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Specify the maximum width of image (in inches). Images
with larger sizes will be split in several pages. Option
<b>&minus;L</b> may be used for specifying size of split
images overlapping.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;x</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Override resolution units specified in the TIFF as
centimeters.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;y</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>Override resolution units specified in the TIFF as
inches.</p>
</td>
<td width="0%">
</td>
<tr valign="top" align="left">
<td width="10%"></td>
<td width="3%">

<p><b>&minus;z</b></p>
</td>
<td width="5%"></td>
<td width="80%">

<p>When generating PostScript Level 2, data is scaled so
that it does not image into the <i>deadzone</i> on a page
(the outer margin that the printing device is unable to
mark). This option suppresses this behavior. When PostScript
Level 1 is generated, data is imaged to the entire printed
page and this option has no affect.</p>
</td>
<td width="0%">
</td>
</table>
<a name="EXAMPLES"></a>
<h2>EXAMPLES</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>The following generates PostScript Level 2 for all pages
of a facsimile:</p></td>
</table>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="17%"></td>
<td width="82%">
<pre>tiff2ps &minus;a2 fax.tif | lpr
</pre>
</td>
</table>
<!-- INDENTATION -->

<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>Note also that if you have version 2.6.1 or newer of
Ghostscript then you can efficiently preview facsimile
generated with the above command.</p>
<!-- INDENTATION -->
<p>To generate Encapsulated PostScript for a the image at
directory 2 of an image use:</p></td>
</table>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="17%"></td>
<td width="82%">
<pre>tiff2ps &minus;d 1 foo.tif
</pre>
</td>
</table>
<!-- INDENTATION -->

<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>(Notice that directories are numbered starting at
zero.)</p>
<!-- INDENTATION -->
<p>If you have a long image, it may be split in several
pages:</p></td>
</table>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="17%"></td>
<td width="82%">
<pre>tiff2ps &minus;h11 &minus;w8.5 &minus;H14 &minus;L.5 foo.tif &gt; foo.ps
</pre>
</td>
</table>
<!-- INDENTATION -->

<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>The page size is set to 8.5x11 by <b>&minus;w</b> and
<b>&minus;h</b> options. We will accept a small amount of
vertical compression, so <b>&minus;H</b> set to 14. Any
pages between 11 and 14 inches will be fit onto one page.
Pages longer than 14 inches are cut off at 11 and continued
on the next page. The <b>&minus;L.5</b> option says to
repeat a half inch on the next page (to improve
readability).</p>
</td>
</table>
<a name="BUGS"></a>
<h2>BUGS</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p>Because PostScript does not support the notion of a
colormap, 8-bit palette images produce 24-bit PostScript
images. This conversion results in output that is six times
bigger than the original image and which takes a long time
to send to a printer over a serial line. Matters are even
worse for 4-, 2-, and 1-bit palette images.</p>
<!-- INDENTATION -->
<p>Does not handle tiled images when generating PostScript
Level I output.</p>
</td>
</table>
<a name="SEE ALSO"></a>
<h2>SEE ALSO</h2>
<!-- INDENTATION -->
<table width="100%" border=0 rules="none" frame="void"
       cols="2" cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="8%"></td>
<td width="91%">
<p><b>pal2rgb</b>(1), <b>tiffinfo</b>(1), <b>tiffcp</b>(1),
<b>tiffgt</b>(1), <b>tiffmedian</b>(1), <b>tiff2bw</b>(1),
<b>tiffsv</b>(1), <b>libtiff</b>(3)</p>
<!-- INDENTATION -->
<p>Libtiff library home page:
<b>http://www.simplesystems.org/libtiff/</b></p>
</td>
</table>
<hr>
</body>
</html>
