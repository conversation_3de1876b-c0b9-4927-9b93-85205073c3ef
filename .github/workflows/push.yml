name: Unit tests & build apps

on: ['push', 'pull_request']

env:
  APK_ARTIFACT_FILENAME: bdist_unit_tests_app-debug-1.1.apk
  AAB_ARTIFACT_FILENAME: bdist_unit_tests_app-release-1.1.aab
  AAR_ARTIFACT_FILENAME: bdist_unit_tests_app-release-1.1.aar
  PYTHONFORANDROID_PREREQUISITES_INSTALL_INTERACTIVE: 0

jobs:

  flake8:
    name: Flake8 tests
    runs-on: ubuntu-latest
    steps:
    - name: Checkout python-for-android
      uses: actions/checkout@v4
    - name: Set up Python 3.x
      uses: actions/setup-python@v4
      with:
        python-version: 3.x
    - name: Run flake8
      run: |
        python -m pip install --upgrade pip
        pip install tox>=2.0
        tox -e pep8

  test:
    name: Pytest [Python ${{ matrix.python-version }} | ${{ matrix.os }}]
    needs: flake8
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        python-version: ['3.8', '3.9', '3.10', '3.11']
        os: [ubuntu-latest, macOs-latest]
    steps:
    - name: Checkout python-for-android
      uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Tox tests
      run: |
        python -m pip install --upgrade pip
        pip install tox>=2.0
        make test
    - name: Coveralls
      uses: AndreMiras/coveralls-python-action@develop
      if: ${{ matrix.os == 'ubuntu-latest' }}
      with:
        parallel: true
        flag-name: run-${{ matrix.os }}-${{ matrix.python-version }}

  ubuntu_build:
    name: Build test APP [ ${{ matrix.runs_on }} | ${{ matrix.bootstrap.name }} ]
    needs: [flake8]
    runs-on: ${{ matrix.runs_on }}
    continue-on-error: true
    strategy:
      matrix:
        runs_on: [ubuntu-latest]
        bootstrap:
          - name: sdl2
            target: testapps-with-numpy
          - name: sdl2_scipy
            target: testapps-with-scipy
          - name: webview
            target: testapps-webview
          - name: service_library
            target: testapps-service_library-aar
          - name: qt
            target: testapps-qt
    steps:
    - name: Checkout python-for-android
      uses: actions/checkout@v4
    - name: Build python-for-android docker image
      run: |
        docker build --tag=kivy/python-for-android .
    - name: Build multi-arch ${{ matrix.bootstrap.target }} artifact with docker
      run: |
        docker run --name p4a-latest kivy/python-for-android make ${{ matrix.bootstrap.target }}
    - name: Copy produced artifacts from docker container (*.apk, *.aab)
      if: matrix.bootstrap.name != 'service_library'
      run: |
        mkdir -p dist
        docker cp p4a-latest:/home/<USER>/app/testapps/on_device_unit_tests/${{ env.APK_ARTIFACT_FILENAME }} dist/
        docker cp p4a-latest:/home/<USER>/app/testapps/on_device_unit_tests/${{ env.AAB_ARTIFACT_FILENAME }} dist/
    - name: Copy produced artifacts from docker container (*.aar)
      if: matrix.bootstrap.name == 'service_library'
      run: |
        mkdir -p dist
        docker cp p4a-latest:/home/<USER>/app/testapps/on_device_unit_tests/${{ env.AAR_ARTIFACT_FILENAME }} dist/
    - name: Rename artifacts to include the build platform name (*.apk, *.aab, *.aar)
      run: |
        if [ -f dist/${{ env.APK_ARTIFACT_FILENAME }} ]; then mv dist/${{ env.APK_ARTIFACT_FILENAME }} dist/${{ matrix.runs_on }}-${{ matrix.bootstrap.name }}-${{ env.APK_ARTIFACT_FILENAME }}; fi
        if [ -f dist/${{ env.AAB_ARTIFACT_FILENAME }} ]; then mv dist/${{ env.AAB_ARTIFACT_FILENAME }} dist/${{ matrix.runs_on }}-${{ matrix.bootstrap.name }}-${{ env.AAB_ARTIFACT_FILENAME }}; fi
        if [ -f dist/${{ env.AAR_ARTIFACT_FILENAME }} ]; then mv dist/${{ env.AAR_ARTIFACT_FILENAME }} dist/${{ matrix.runs_on }}-${{ matrix.bootstrap.name }}-${{ env.AAR_ARTIFACT_FILENAME }}; fi
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.runs_on }}-${{ matrix.bootstrap.name }}-artifacts
        path: dist

  macos_build:
    name: Build test APP [ ${{ matrix.runs_on }} | ${{ matrix.bootstrap.name }} ]
    needs: [flake8]
    runs-on: ${{ matrix.runs_on }}
    continue-on-error: true
    strategy:
      matrix:
        runs_on: [macos-latest, apple-silicon-m1]
        bootstrap:
          - name: sdl2
            target: testapps-with-numpy
          - name: webview
            target: testapps-webview
    env:
      ANDROID_HOME: ${HOME}/.android
      ANDROID_SDK_ROOT: ${HOME}/.android/android-sdk
      ANDROID_SDK_HOME: ${HOME}/.android/android-sdk
      ANDROID_NDK_HOME: ${HOME}/.android/android-ndk
    steps:
      - name: Checkout python-for-android
        uses: actions/checkout@v4
      - name: Install python-for-android
        run: |
          source ci/osx_ci.sh
          arm64_set_path_and_python_version 3.9.7
          python3 -m pip install -e .
      - name: Install prerequisites via pythonforandroid/prerequisites.py (Experimental)
        run: |
          source ci/osx_ci.sh
          arm64_set_path_and_python_version 3.9.7
          python3 pythonforandroid/prerequisites.py
      - name: Install dependencies (Legacy)
        run: |
          source ci/osx_ci.sh
          arm64_set_path_and_python_version 3.9.7
          make --file ci/makefiles/osx.mk
      - name: Build multi-arch apk Python 3 (armeabi-v7a, arm64-v8a, x86_64, x86)
        run: |
          source ci/osx_ci.sh
          arm64_set_path_and_python_version 3.9.7
          make ${{ matrix.bootstrap.target }}
      - name: Copy produced artifacts into dist/ (*.apk, *.aab)
        run: |
          mkdir -p dist
          cp testapps/on_device_unit_tests/*.apk dist/
          cp testapps/on_device_unit_tests/*.aab dist/
          ls -l dist/
      - name: Rename artifacts to include the build platform name (*.apk, *.aab)
        run: |
          if [ -f dist/${{ env.APK_ARTIFACT_FILENAME }} ]; then mv dist/${{ env.APK_ARTIFACT_FILENAME }} dist/${{ matrix.runs_on }}-${{ matrix.bootstrap.name }}-${{ env.APK_ARTIFACT_FILENAME }}; fi
          if [ -f dist/${{ env.AAB_ARTIFACT_FILENAME }} ]; then mv dist/${{ env.AAB_ARTIFACT_FILENAME }} dist/${{ matrix.runs_on }}-${{ matrix.bootstrap.name }}-${{ env.AAB_ARTIFACT_FILENAME }}; fi
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.runs_on }}-${{ matrix.bootstrap.name }}-artifacts
          path: dist

  ubuntu_rebuild_updated_recipes:
    name: Test updated recipes for arch ${{ matrix.android_arch }} [ ubuntu-latest ]
    needs: [flake8]
    runs-on: ubuntu-latest
    continue-on-error: true
    strategy:
      matrix:
        android_arch: ["arm64-v8a", "armeabi-v7a", "x86_64", "x86"]
    env:
      REBUILD_UPDATED_RECIPES_EXTRA_ARGS: --arch=${{ matrix.android_arch }}
    steps:
    - name: Checkout python-for-android (all-history)
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    # helps with GitHub runner getting out of space
    - name: Free disk space
      run: |
        df -h
        sudo swapoff -a
        sudo rm -f /swapfile
        sudo apt -y clean
        docker rmi $(docker image ls -aq)
        df -h
    - name: Pull docker image
      run: |
        make docker/pull
    - name: Rebuild updated recipes
      run: |
        make docker/run/make/rebuild_updated_recipes

  macos_rebuild_updated_recipes:
    name: Test updated recipes for arch ${{ matrix.android_arch }} [ ${{ matrix.runs_on }} ]
    needs: [flake8]
    runs-on: ${{ matrix.runs_on }}
    continue-on-error: true
    strategy:
      matrix:
        android_arch: ["arm64-v8a", "armeabi-v7a", "x86_64", "x86"]
        runs_on: [macos-latest, apple-silicon-m1]
    env:
      ANDROID_HOME: ${HOME}/.android
      ANDROID_SDK_ROOT: ${HOME}/.android/android-sdk
      ANDROID_SDK_HOME: ${HOME}/.android/android-sdk
      ANDROID_NDK_HOME: ${HOME}/.android/android-ndk
      REBUILD_UPDATED_RECIPES_EXTRA_ARGS: --arch=${{ matrix.android_arch }}
    steps:
      - name: Checkout python-for-android (all-history)
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install python-for-android
        run: |
          source ci/osx_ci.sh
          arm64_set_path_and_python_version 3.9.7
          python3 -m pip install -e .
      - name: Install prerequisites via pythonforandroid/prerequisites.py (Experimental)
        run: |
          source ci/osx_ci.sh
          arm64_set_path_and_python_version 3.9.7
          python3 pythonforandroid/prerequisites.py
      - name: Install dependencies (Legacy)
        run: |
          source ci/osx_ci.sh
          arm64_set_path_and_python_version 3.9.7
          make --file ci/makefiles/osx.mk
      - name: Rebuild updated recipes
        run: |
          source ci/osx_ci.sh
          arm64_set_path_and_python_version 3.9.7
          make rebuild_updated_recipes

  coveralls_finish:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - name: Coveralls Finished
      uses: AndreMiras/coveralls-python-action@develop
      with:
        parallel-finished: true

  documentation:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Requirements
      run: |
        python -m pip install --upgrade pip
        pip install -r doc/requirements.txt
    - name: Check links
      run: sphinx-build -b linkcheck doc/source doc/build
    - name: Generate documentation
      run: sphinx-build doc/source doc/build

