diff --git a/pip/download.py b/pip/download.py
index 54d3131..1aab70f 100644
--- a/pip/download.py
+++ b/pip/download.py
@@ -89,23 +89,25 @@ def user_agent():
         # Complete Guess
         data["implementation"]["version"] = platform.python_version()
 
-    if sys.platform.startswith("linux"):
-        from pip._vendor import distro
-        distro_infos = dict(filter(
-            lambda x: x[1],
-            zip(["name", "version", "id"], distro.linux_distribution()),
-        ))
-        libc = dict(filter(
-            lambda x: x[1],
-            zip(["lib", "version"], libc_ver()),
-        ))
-        if libc:
-            distro_infos["libc"] = libc
-        if distro_infos:
-            data["distro"] = distro_infos
-
-    if sys.platform.startswith("darwin") and platform.mac_ver()[0]:
-        data["distro"] = {"name": "macOS", "version": platform.mac_ver()[0]}
+    # if sys.platform.startswith("linux"):
+    #     from pip._vendor import distro
+    #     distro_infos = dict(filter(
+    #         lambda x: x[1],
+    #         zip(["name", "version", "id"], distro.linux_distribution()),
+    #     ))
+    #     libc = dict(filter(
+    #         lambda x: x[1],
+    #         zip(["lib", "version"], libc_ver()),
+    #     ))
+    #     if libc:
+    #         distro_infos["libc"] = libc
+    #     if distro_infos:
+    #         data["distro"] = distro_infos
+
+    # if sys.platform.startswith("darwin") and platform.mac_ver()[0]:
+    #     data["distro"] = {"name": "macOS", "version": platform.mac_ver()[0]}
+
+    data['distro'] = {'name': 'Android'}
 
     if platform.system():
         data.setdefault("system", {})["name"] = platform.system()
