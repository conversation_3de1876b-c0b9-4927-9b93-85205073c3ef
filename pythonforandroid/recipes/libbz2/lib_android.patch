Set default compiler to `clang` and disable versioned shared library
--- bzip2-1.0.8/Makefile-libbz2_so.orig	2019-07-13 19:50:05.000000000 +0200
+++ bzip2-1.0.8/Makefile-libbz2_so	2020-03-13 20:10:32.336990786 +0100
@@ -22,7 +22,7 @@
 
 
 SHELL=/bin/sh
-CC=gcc
+CC=clang
 BIGFILES=-D_FILE_OFFSET_BITS=64
 CFLAGS=-fpic -fPIC -Wall -Winline -O2 -g $(BIGFILES)
 
@@ -35,13 +35,11 @@ OBJS= blocksort.o  \
       bzlib.o
 
 all: $(OBJS)
-	$(CC) -shared -Wl,-soname -Wl,libbz2.so.1.0 -o libbz2.so.1.0.8 $(OBJS)
-	$(CC) $(CFLAGS) -o bzip2-shared bzip2.c libbz2.so.1.0.8
-	rm -f libbz2.so.1.0
-	ln -s libbz2.so.1.0.8 libbz2.so.1.0
+	$(CC) -shared -Wl,-soname=libbz2.so -o libbz2.so $(OBJS)
+	$(CC) $(CFLAGS) -o bzip2-shared bzip2.c libbz2.so
 
 clean: 
-	rm -f $(OBJS) bzip2.o libbz2.so.1.0.8 libbz2.so.1.0 bzip2-shared
+	rm -f $(OBJS) bzip2.o libbz2.so bzip2-shared
 
 blocksort.o: blocksort.c
 	$(CC) $(CFLAGS) -c blocksort.c
